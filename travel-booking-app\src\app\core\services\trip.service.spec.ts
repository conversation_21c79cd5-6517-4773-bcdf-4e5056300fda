import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { TripService } from './trip.service';
import { ApiService } from './api.service';
import { Trip, TripCategory, CreateTripDto, TripFilterDto } from '../models/trip.model';
import { PagedResult, PaginationParameters } from '../models/common.model';

describe('TripService', () => {
  let service: TripService;
  let apiServiceSpy: jasmine.SpyObj<ApiService>;

  const mockTripCategory: TripCategory = {
    id: 1,
    name: 'Adventure',
    description: 'Adventure trips',
    iconUrl: 'adventure-icon.png'
  };

  const mockTrip: Trip = {
    id: 1,
    name: 'Bali Adventure',
    shortDescription: 'Amazing adventure in Bali',
    price: 1200,
    discountPrice: 1000,
    duration: 7,
    maxCapacity: 20,
    minAge: 18,
    maxAge: 65,
    difficulty: 2,
    mainImageUrl: 'bali-main.jpg',
    isFeatured: true,
    availableFrom: new Date('2024-01-01'),
    availableTo: new Date('2024-12-31'),
    category: mockTripCategory,
    destinationCity: {
      id: 1,
      name: 'Ubud',
      country: { id: 1, name: 'Indonesia', code: 'ID' }
    },
    departureCity: {
      id: 2,
      name: 'Jakarta',
      country: { id: 1, name: 'Indonesia', code: 'ID' }
    },
    images: [
      {
        id: 1,
        imageUrl: 'bali-1.jpg',
        caption: 'Beautiful Bali',
        isMain: true,
        displayOrder: 1
      }
    ],
    effectivePrice: 1000,
    hasDiscount: true,
    discountPercentage: 16.67
  };

  const mockPagedResult: PagedResult<Trip> = {
    items: [mockTrip],
    totalCount: 1,
    pageNumber: 1,
    pageSize: 10,
    totalPages: 1,
    hasPreviousPage: false,
    hasNextPage: false
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', [
      'get', 'post', 'put', 'delete', 'getPaged', 'uploadFile', 'getRaw'
    ]);

    TestBed.configureTestingModule({
      providers: [
        TripService,
        { provide: ApiService, useValue: apiSpy }
      ]
    });

    service = TestBed.inject(TripService);
    apiServiceSpy = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getTrips', () => {
    it('should fetch trips with pagination', (done) => {
      const pagination: PaginationParameters = { page: 1, pageSize: 10 };
      const filters: TripFilterDto = { categoryId: 1 };

      apiServiceSpy.getPaged.and.returnValue(of(mockPagedResult));

      service.getTrips(pagination, filters).subscribe({
        next: (result) => {
          expect(result).toEqual(mockPagedResult);
          expect(result.items.length).toBe(1);
          expect(result.items[0]).toEqual(mockTrip);
          done();
        }
      });

      expect(apiServiceSpy.getPaged).toHaveBeenCalledWith('trips', pagination, filters);
    });

    it('should handle error when fetching trips', (done) => {
      const pagination: PaginationParameters = { page: 1, pageSize: 10 };
      const errorResponse = { error: { message: 'Server error' } };

      apiServiceSpy.getPaged.and.returnValue(throwError(() => errorResponse));

      service.getTrips(pagination).subscribe({
        error: (error) => {
          expect(error).toEqual(errorResponse);
          done();
        }
      });
    });
  });

  describe('getTripById', () => {
    it('should fetch trip by id', (done) => {
      const tripId = 1;

      apiServiceSpy.get.and.returnValue(of(mockTrip));

      service.getTripById(tripId).subscribe({
        next: (trip) => {
          expect(trip).toEqual(mockTrip);
          done();
        }
      });

      expect(apiServiceSpy.get).toHaveBeenCalledWith(`trips/${tripId}`);
    });

    it('should handle error when trip not found', (done) => {
      const tripId = 999;
      const errorResponse = { error: { message: 'Trip not found' } };

      apiServiceSpy.get.and.returnValue(throwError(() => errorResponse));

      service.getTripById(tripId).subscribe({
        error: (error) => {
          expect(error).toEqual(errorResponse);
          done();
        }
      });
    });
  });

  describe('getFeaturedTrips', () => {
    it('should fetch featured trips', (done) => {
      const featuredTrips = [mockTrip];

      apiServiceSpy.get.and.returnValue(of(featuredTrips));

      service.getFeaturedTrips().subscribe({
        next: (trips) => {
          expect(trips).toEqual(featuredTrips);
          expect(trips.length).toBe(1);
          expect(trips[0].isFeatured).toBe(true);
          done();
        }
      });

      expect(apiServiceSpy.get).toHaveBeenCalledWith('trips/featured');
    });
  });

  describe('getTripCategories', () => {
    it('should fetch trip categories', (done) => {
      const categories = [mockTripCategory];

      apiServiceSpy.get.and.returnValue(of(categories));

      service.getTripCategories().subscribe({
        next: (result) => {
          expect(result).toEqual(categories);
          expect(result.length).toBe(1);
          expect(result[0]).toEqual(mockTripCategory);
          done();
        }
      });

      expect(apiServiceSpy.get).toHaveBeenCalledWith('trips/categories');
    });
  });

  describe('searchTrips', () => {
    it('should search trips with search term', (done) => {
      const searchTerm = 'Bali';
      const filters: TripFilterDto = { categoryId: 1 };

      apiServiceSpy.getPaged.and.returnValue(of(mockPagedResult));

      service.searchTrips(searchTerm, filters).subscribe({
        next: (result) => {
          expect(result).toEqual(mockPagedResult);
          done();
        }
      });

      expect(apiServiceSpy.getPaged).toHaveBeenCalledWith(
        'trips',
        { page: 1, pageSize: 20 },
        { ...filters, search: searchTerm }
      );
    });
  });

  describe('Admin operations', () => {
    describe('createTrip', () => {
      it('should create a new trip', (done) => {
        const createTripData: CreateTripDto = {
          name: 'New Trip',
          shortDescription: 'A new amazing trip',
          description: 'Full description of the trip',
          price: 1500,
          duration: 10,
          maxCapacity: 15,
          difficulty: 1,
          categoryId: 1,
          destinationCityId: 1,
          departureCityId: 2,
          mainImageUrl: 'new-trip.jpg',
          isFeatured: false,
          availableFrom: new Date('2024-06-01'),
          availableTo: new Date('2024-12-31'),
          includesAccommodation: true,
          includesTransport: true,
          includesMeals: false,
          includesGuide: true
        };

        const createdTrip = { ...mockTrip, ...createTripData, id: 2 };

        apiServiceSpy.post.and.returnValue(of(createdTrip));

        service.createTrip(createTripData).subscribe({
          next: (trip) => {
            expect(trip).toEqual(createdTrip);
            expect(trip.name).toBe(createTripData.name);
            done();
          }
        });

        expect(apiServiceSpy.post).toHaveBeenCalledWith('admin/trips', createTripData);
      });
    });

    describe('updateTrip', () => {
      it('should update an existing trip', (done) => {
        const updateData = { id: 1, name: 'Updated Trip Name' };
        const updatedTrip = { ...mockTrip, name: 'Updated Trip Name' };

        apiServiceSpy.put.and.returnValue(of(updatedTrip));

        service.updateTrip(updateData).subscribe({
          next: (trip) => {
            expect(trip).toEqual(updatedTrip);
            expect(trip.name).toBe('Updated Trip Name');
            done();
          }
        });

        expect(apiServiceSpy.put).toHaveBeenCalledWith(`admin/trips/${updateData.id}`, updateData);
      });
    });

    describe('deleteTrip', () => {
      it('should delete a trip', (done) => {
        const tripId = 1;

        apiServiceSpy.delete.and.returnValue(of(undefined));

        service.deleteTrip(tripId).subscribe({
          next: () => {
            expect(true).toBe(true); // Test passes if we reach here
            done();
          }
        });

        expect(apiServiceSpy.delete).toHaveBeenCalledWith(`admin/trips/${tripId}`);
      });
    });

    describe('toggleTripStatus', () => {
      it('should toggle trip status', (done) => {
        const tripId = 1;
        const updatedTrip = { ...mockTrip, isActive: false };

        apiServiceSpy.put.and.returnValue(of(updatedTrip));

        service.toggleTripStatus(tripId).subscribe({
          next: (trip) => {
            expect(trip).toEqual(updatedTrip);
            done();
          }
        });

        expect(apiServiceSpy.put).toHaveBeenCalledWith(`admin/trips/${tripId}/toggle-status`, {});
      });
    });
  });

  describe('Image operations', () => {
    it('should upload trip image', (done) => {
      const tripId = 1;
      const file = new File([''], 'test-image.jpg', { type: 'image/jpeg' });
      const uploadResponse = { imageUrl: 'uploaded-image.jpg' };

      apiServiceSpy.uploadFile.and.returnValue(of(uploadResponse));

      service.uploadTripImage(tripId, file).subscribe({
        next: (response) => {
          expect(response).toEqual(uploadResponse);
          done();
        }
      });

      expect(apiServiceSpy.uploadFile).toHaveBeenCalledWith(`admin/trips/${tripId}/images`, file);
    });

    it('should delete trip image', (done) => {
      const tripId = 1;
      const imageId = 1;

      apiServiceSpy.delete.and.returnValue(of(undefined));

      service.deleteTripImage(tripId, imageId).subscribe({
        next: () => {
          expect(true).toBe(true);
          done();
        }
      });

      expect(apiServiceSpy.delete).toHaveBeenCalledWith(`admin/trips/${tripId}/images/${imageId}`);
    });
  });

  describe('Statistics', () => {
    it('should get trip statistics', (done) => {
      const tripId = 1;
      const stats = { totalBookings: 10, revenue: 15000 };

      apiServiceSpy.get.and.returnValue(of(stats));

      service.getTripStats(tripId).subscribe({
        next: (result) => {
          expect(result).toEqual(stats);
          done();
        }
      });

      expect(apiServiceSpy.get).toHaveBeenCalledWith(`admin/trips/${tripId}/stats`);
    });
  });

  describe('Export functionality', () => {
    it('should export trips', (done) => {
      const filters: TripFilterDto = { categoryId: 1 };
      const blob = new Blob(['trip data'], { type: 'text/csv' });

      apiServiceSpy.getRaw.and.returnValue(of(blob));

      service.exportTrips(filters).subscribe({
        next: (result) => {
          expect(result).toEqual(blob);
          done();
        }
      });

      expect(apiServiceSpy.getRaw).toHaveBeenCalledWith('admin/trips/export', filters);
    });
  });
});
