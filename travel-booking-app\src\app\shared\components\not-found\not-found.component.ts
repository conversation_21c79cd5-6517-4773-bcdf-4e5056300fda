import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="error-container">
      <div class="error-content">
        <div class="error-icon">🔍</div>
        <h1>Page Not Found</h1>
        <p>The page you're looking for doesn't exist or has been moved.</p>
        <div class="error-actions">
          <a routerLink="/home" class="btn btn-primary">Go Home</a>
          <a routerLink="/trips" class="btn btn-outline">Browse Trips</a>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .error-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
      padding: 20px;
    }
    .error-content {
      text-align: center;
      max-width: 500px;
    }
    .error-icon {
      font-size: 5rem;
      margin-bottom: 30px;
    }
    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 15px;
    }
    p {
      font-size: 1.1rem;
      color: #718096;
      margin-bottom: 40px;
    }
    .error-actions {
      display: flex;
      gap: 20px;
      justify-content: center;
    }
    .btn {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
    }
    .btn-primary {
      background: #667eea;
      color: white;
    }
    .btn-outline {
      background: transparent;
      color: #667eea;
      border: 2px solid #667eea;
    }
  `]
})
export class NotFoundComponent {}
