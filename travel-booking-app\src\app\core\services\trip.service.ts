import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  Trip, 
  TripCategory, 
  CreateTripDto, 
  UpdateTripDto, 
  TripFilterDto,
  City,
  Country 
} from '../models/trip.model';
import { PagedResult, PaginationParameters } from '../models/common.model';

@Injectable({
  providedIn: 'root'
})
export class TripService {
  private readonly apiService = inject(ApiService);

  // Public trip methods
  getTrips(pagination: PaginationParameters, filters?: TripFilterDto): Observable<PagedResult<Trip>> {
    return this.apiService.getPaged<Trip>('trips', pagination, filters);
  }

  getTripById(id: number): Observable<Trip> {
    return this.apiService.get<Trip>(`trips/${id}`);
  }

  getFeaturedTrips(): Observable<Trip[]> {
    return this.apiService.get<Trip[]>('trips/featured');
  }

  getTripCategories(): Observable<TripCategory[]> {
    return this.apiService.get<TripCategory[]>('trips/categories');
  }

  getDestinations(): Observable<City[]> {
    return this.apiService.get<City[]>('trips/destinations');
  }

  searchTrips(searchTerm: string, filters?: TripFilterDto): Observable<PagedResult<Trip>> {
    const searchFilters = { ...filters, search: searchTerm };
    const pagination: PaginationParameters = { page: 1, pageSize: 20 };
    return this.getTrips(pagination, searchFilters);
  }

  // Admin trip methods
  createTrip(tripData: CreateTripDto): Observable<Trip> {
    return this.apiService.post<Trip>('admin/trips', tripData);
  }

  updateTrip(id: number, tripData: Partial<Trip>): Observable<Trip> {
    return this.apiService.put<Trip>(`admin/trips/${id}`, tripData);
  }

  deleteTrip(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/trips/${id}`);
  }

  // Admin trip management
  getAdminTrips(pagination: PaginationParameters, filters?: TripFilterDto): Observable<PagedResult<Trip>> {
    return this.apiService.getPaged<Trip>('admin/trips', pagination, filters);
  }

  toggleTripStatus(id: number): Observable<Trip> {
    return this.apiService.put<Trip>(`admin/trips/${id}/toggle-status`, {});
  }

  toggleFeaturedStatus(id: number): Observable<Trip> {
    return this.apiService.put<Trip>(`admin/trips/${id}/toggle-featured`, {});
  }

  // Category management
  createTripCategory(categoryData: Omit<TripCategory, 'id'>): Observable<TripCategory> {
    return this.apiService.post<TripCategory>('admin/trip-categories', categoryData);
  }

  updateTripCategory(categoryData: TripCategory): Observable<TripCategory> {
    return this.apiService.put<TripCategory>(`admin/trip-categories/${categoryData.id}`, categoryData);
  }

  deleteTripCategory(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/trip-categories/${id}`);
  }

  // Location services
  getCountries(): Observable<Country[]> {
    return this.apiService.get<Country[]>('countries');
  }

  getCitiesByCountry(countryId: number): Observable<City[]> {
    return this.apiService.get<City[]>(`countries/${countryId}/cities`);
  }

  getAllCities(): Observable<City[]> {
    return this.apiService.get<City[]>('cities');
  }

  // Image upload
  uploadTripImage(tripId: number, file: File): Observable<any> {
    return this.apiService.uploadFile(`admin/trips/${tripId}/images`, file);
  }

  deleteTripImage(tripId: number, imageId: number): Observable<void> {
    return this.apiService.delete<void>(`admin/trips/${tripId}/images/${imageId}`);
  }

  // Itinerary management
  addItineraryItem(tripId: number, itineraryData: any): Observable<any> {
    return this.apiService.post(`admin/trips/${tripId}/itinerary`, itineraryData);
  }

  updateItineraryItem(tripId: number, itineraryId: number, itineraryData: any): Observable<any> {
    return this.apiService.put(`admin/trips/${tripId}/itinerary/${itineraryId}`, itineraryData);
  }

  deleteItineraryItem(tripId: number, itineraryId: number): Observable<void> {
    return this.apiService.delete<void>(`admin/trips/${tripId}/itinerary/${itineraryId}`);
  }

  // Trip statistics
  getTripStats(tripId: number): Observable<any> {
    return this.apiService.get(`admin/trips/${tripId}/stats`);
  }

  // Bulk operations
  bulkUpdateTrips(tripIds: number[], updateData: Partial<Trip>): Observable<void> {
    return this.apiService.put<void>('admin/trips/bulk-update', { tripIds, updateData });
  }

  bulkDeleteTrips(tripIds: number[]): Observable<void> {
    return this.apiService.delete<void>('admin/trips/bulk-delete', { tripIds });
  }

  // Export functionality
  exportTrips(filters?: TripFilterDto): Observable<Blob> {
    return this.apiService.getRaw<Blob>('admin/trips/export', filters);
  }
}
