2025-07-08 23:58:27.919 +03:00 [WRN] Entity 'Blog' has a global query filter defined and is the required end of a relationship with the entity 'BlogImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-07-08 23:58:28.049 +03:00 [WRN] Entity 'Booking' has a global query filter defined and is the required end of a relationship with the entity 'BookingPayment'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-07-08 23:58:28.055 +03:00 [WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-07-08 23:58:28.065 +03:00 [WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripItinerary'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-07-08 23:58:28.070 +03:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-07-08 23:58:28.255 +03:00 [WRN] The 'UserRole' property 'Role' on entity type 'User' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'UserRole' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-08 23:58:28.280 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Currency'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-08 23:58:32.408 +03:00 [INF] Starting Travel & Tourism API
2025-07-08 23:58:39.041 +03:00 [INF] Request GET /swagger started at "2025-07-08T20:58:39.0332640Z"
2025-07-08 23:58:39.409 +03:00 [INF] Request GET /swagger completed with status 404 in 375ms
2025-07-08 23:59:12.949 +03:00 [INF] Request GET /swagger started at "2025-07-08T20:59:12.9496056Z"
2025-07-08 23:59:12.958 +03:00 [INF] Request GET /swagger completed with status 404 in 8ms
2025-07-08 23:59:22.159 +03:00 [INF] Request GET /swagger.index started at "2025-07-08T20:59:22.1593041Z"
2025-07-08 23:59:22.169 +03:00 [INF] Request GET /swagger.index completed with status 404 in 9ms
