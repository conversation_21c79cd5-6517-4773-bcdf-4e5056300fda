# Components Documentation - Travel Booking App

## Component Architecture Overview

The Travel Booking App follows a hierarchical component structure with clear separation of concerns:

```
AppComponent (Root)
├── HeaderComponent (Navigation)
├── RouterOutlet (Dynamic Content)
│   ├── HomeComponent (Landing Page)
│   ├── TripsComponent (Trip Listing)
│   ├── TripDetailComponent (Trip Details)
│   ├── BookingListComponent (User Bookings)
│   ├── BookingDetailComponent (Booking Details)
│   ├── DashboardComponent (User Dashboard)
│   ├── ProfileComponent (User Profile)
│   └── AdminComponents (Admin Panel)
├── FooterComponent (Site Footer)
├── NotificationComponent (Toast Messages)
└── PWAInstallComponent (PWA Features)
```

## Core Components

### AppComponent
**File:** `src/app/app.ts`
**Purpose:** Root application component that provides the main application shell
**Type:** Container Component

**Key Features:**
- Application layout structure
- Global error boundary
- PWA integration
- Notification system integration

**Template Structure:**
```html
<div class="app-container">
  <app-header />
  <main class="main-content">
    <router-outlet />
  </main>
  <app-footer />
  <app-notification />
  <app-pwa-install />
</div>
```

**Dependencies:**
- `HeaderComponent`
- `FooterComponent`
- `NotificationComponent`
- `PWAInstallComponent`
- `RouterOutlet`

**Styling:**
- Global app layout
- Responsive container
- Main content area styling

---

### HeaderComponent
**File:** `src/app/shared/components/header/header.component.ts`
**Purpose:** Navigation header with user authentication and menu
**Type:** Presentation Component

**Key Features:**
- Responsive navigation menu
- User authentication status display
- Mobile menu toggle
- User dropdown menu
- Brand logo and navigation links

**Signals:**
```typescript
isMenuOpen = signal<boolean>(false);
isUserMenuOpen = signal<boolean>(false);
```

**Methods:**
- `toggleMobileMenu()`: Toggle mobile navigation menu
- `toggleUserMenu()`: Toggle user dropdown menu
- `logout()`: Handle user logout

**Template Sections:**
1. **Brand Logo**: Application logo and home link
2. **Navigation Menu**: Main navigation links (Trips, Blog)
3. **User Menu**: Authentication status and user actions
4. **Mobile Toggle**: Hamburger menu for mobile devices

**Responsive Behavior:**
- Desktop: Full horizontal navigation
- Tablet: Condensed navigation
- Mobile: Hamburger menu with overlay

**Dependencies:**
- `AuthService`: User authentication state
- `Router`: Navigation handling

---

### HomeComponent
**File:** `src/app/features/home/<USER>
**Purpose:** Landing page with featured content and search functionality
**Type:** Smart Component

**Key Features:**
- Hero section with call-to-action
- Search functionality
- Featured trips display
- Recent blog posts
- Animated content sections

**Signals:**
```typescript
featuredTrips = signal<Trip[]>([]);
recentBlogs = signal<Blog[]>([]);
isLoading = signal<boolean>(false);
searchQuery = signal<string>('');
```

**Lifecycle Methods:**
- `ngOnInit()`: Load featured content on component initialization

**Methods:**
- `loadFeaturedTrips()`: Fetch and display featured trips
- `loadRecentBlogs()`: Fetch and display recent blog posts
- `onSearch()`: Handle search form submission
- `getDifficultyText()`: Convert difficulty number to text
- `formatPrice()`: Format price with currency
- `getDiscountPercentage()`: Calculate discount percentage

**Template Sections:**
1. **Hero Section**: Main banner with search
2. **Search Section**: Trip search functionality
3. **Featured Trips**: Grid of featured travel packages
4. **Recent Blogs**: Latest blog posts preview

**Animations:**
- Fade-in animations for content sections
- Stagger animations for trip cards
- Parallax effects on hero section
- Tilt effects on interactive cards

**Dependencies:**
- `TripService`: Featured trips data
- `BlogService`: Recent blogs data
- `Router`: Navigation handling
- Animation directives

---

### TripsComponent
**File:** `src/app/features/trips/trips.component.ts`
**Purpose:** Trip listing with filtering, sorting, and pagination
**Type:** Smart Component

**Key Features:**
- Trip grid/list display
- Advanced filtering options
- Sorting capabilities
- Pagination
- Search functionality
- Loading states

**Signals:**
```typescript
trips = signal<Trip[]>([]);
categories = signal<Category[]>([]);
isLoading = signal<boolean>(false);
searchQuery = signal<string>('');
selectedCategory = signal<string>('');
selectedDifficulty = signal<string>('');
priceRange = signal<{min: number; max: number}>({min: 0, max: 5000});
sortBy = signal<string>('name');
sortOrder = signal<string>('asc');
currentPage = signal<number>(1);
totalPages = signal<number>(0);
totalResults = signal<number>(0);
```

**Methods:**
- `loadTrips()`: Fetch trips with current filters
- `loadCategories()`: Fetch trip categories
- `onSearch()`: Handle search input
- `onFilterChange()`: Handle filter changes
- `onSortChange()`: Handle sort changes
- `onPageChange()`: Handle pagination
- `clearFilters()`: Reset all filters
- `toggleView()`: Switch between grid/list view

**Template Sections:**
1. **Search Bar**: Trip search input
2. **Filters Panel**: Category, price, difficulty filters
3. **Sort Controls**: Sorting options
4. **Results Info**: Count and pagination info
5. **Trip Grid/List**: Trip cards display
6. **Pagination**: Page navigation controls

**Responsive Behavior:**
- Desktop: 4-column grid
- Tablet: 2-column grid
- Mobile: Single column list

**Dependencies:**
- `TripService`: Trip data and filtering
- `CategoryService`: Trip categories
- `Router`: Navigation and query params

---

### TripDetailComponent
**File:** `src/app/features/trips/trip-detail/trip-detail.component.ts`
**Purpose:** Detailed trip information and booking initiation
**Type:** Smart Component

**Key Features:**
- Comprehensive trip information
- Image gallery
- Itinerary display
- Booking form integration
- Related trips suggestions
- Social sharing

**Signals:**
```typescript
trip = signal<Trip | null>(null);
selectedImageIndex = signal<number>(0);
isLoading = signal<boolean>(false);
showBookingForm = signal<boolean>(false);
relatedTrips = signal<Trip[]>([]);
```

**Methods:**
- `loadTrip()`: Fetch trip details by ID
- `loadRelatedTrips()`: Fetch similar trips
- `selectImage()`: Change main display image
- `openBookingForm()`: Show booking form
- `shareTrip()`: Handle social sharing
- `addToWishlist()`: Add trip to user wishlist

**Template Sections:**
1. **Trip Header**: Name, price, basic info
2. **Image Gallery**: Main image with thumbnails
3. **Trip Details**: Description, inclusions, requirements
4. **Itinerary**: Day-by-day schedule
5. **Booking Section**: Price and booking button
6. **Related Trips**: Similar trip suggestions

**SEO Integration:**
- Dynamic meta tags
- Structured data (JSON-LD)
- Open Graph tags
- Twitter Card tags

**Dependencies:**
- `TripService`: Trip data
- `BookingService`: Booking initiation
- `SEOService`: Meta tag management
- `AuthService`: User authentication

---

### BookingListComponent
**File:** `src/app/features/bookings/booking-list/booking-list.component.ts`
**Purpose:** User's booking history with filtering and management
**Type:** Smart Component

**Key Features:**
- Booking history display
- Status filtering
- Booking actions (view, cancel)
- Pagination
- Export functionality

**Signals:**
```typescript
bookings = signal<Booking[]>([]);
isLoading = signal<boolean>(false);
selectedStatus = signal<string>('');
currentPage = signal<number>(1);
totalPages = signal<number>(0);
totalResults = signal<number>(0);
```

**Methods:**
- `loadBookings()`: Fetch user bookings
- `onStatusFilter()`: Filter by booking status
- `viewBooking()`: Navigate to booking details
- `cancelBooking()`: Cancel a booking
- `exportBookings()`: Export booking data
- `getStatusText()`: Convert status to text
- `getStatusClass()`: Get CSS class for status

**Template Sections:**
1. **Filter Controls**: Status and date filters
2. **Booking Cards**: Individual booking displays
3. **Action Buttons**: View, cancel, export actions
4. **Pagination**: Page navigation
5. **Empty State**: No bookings message

**Booking Status Handling:**
- Pending: Yellow indicator, cancel option
- Confirmed: Green indicator, view details
- Cancelled: Red indicator, view only
- Completed: Blue indicator, review option

**Dependencies:**
- `BookingService`: Booking data and operations
- `NotificationService`: Success/error messages
- `Router`: Navigation handling

---

### BookingDetailComponent
**File:** `src/app/features/bookings/booking-detail/booking-detail.component.ts`
**Purpose:** Detailed booking information and management
**Type:** Smart Component

**Key Features:**
- Complete booking details
- Passenger information
- Payment status
- Trip information
- Cancellation handling
- Document downloads

**Signals:**
```typescript
booking = signal<Booking | null>(null);
isLoading = signal<boolean>(false);
showCancelModal = signal<boolean>(false);
```

**Methods:**
- `loadBooking()`: Fetch booking details
- `cancelBooking()`: Handle booking cancellation
- `downloadVoucher()`: Download booking voucher
- `downloadInvoice()`: Download payment invoice
- `getStatusText()`: Get booking status text
- `getPaymentStatusText()`: Get payment status text

**Template Sections:**
1. **Booking Header**: Reference, status, dates
2. **Trip Information**: Trip details and itinerary
3. **Passenger Details**: Traveler information
4. **Payment Information**: Costs and payment status
5. **Actions**: Cancel, download, contact options

**Document Generation:**
- Booking voucher PDF
- Payment invoice PDF
- Itinerary document
- Passenger manifest

**Dependencies:**
- `BookingService`: Booking operations
- `NotificationService`: User feedback
- `Router`: Navigation handling

---

### DashboardComponent
**File:** `src/app/features/dashboard/dashboard.component.ts`
**Purpose:** User dashboard with overview and quick actions
**Type:** Smart Component

**Key Features:**
- User overview statistics
- Recent bookings
- Quick actions
- Upcoming trips
- Profile summary

**Signals:**
```typescript
user = signal<User | null>(null);
recentBookings = signal<Booking[]>([]);
upcomingTrips = signal<Booking[]>([]);
stats = signal<UserStats | null>(null);
isLoading = signal<boolean>(false);
```

**Methods:**
- `loadDashboardData()`: Fetch all dashboard data
- `loadRecentBookings()`: Get recent booking history
- `loadUpcomingTrips()`: Get upcoming travel plans
- `loadUserStats()`: Get user statistics

**Template Sections:**
1. **Welcome Section**: User greeting and summary
2. **Quick Stats**: Booking counts and statistics
3. **Recent Bookings**: Latest booking activity
4. **Upcoming Trips**: Future travel plans
5. **Quick Actions**: Common user actions

**Quick Actions:**
- Book new trip
- View all bookings
- Update profile
- Contact support

**Dependencies:**
- `AuthService`: User information
- `BookingService`: Booking data
- `UserService`: User statistics

---

### AdminDashboardComponent
**File:** `src/app/features/admin/dashboard/admin-dashboard.component.ts`
**Purpose:** Admin overview with analytics and system status
**Type:** Smart Component

**Key Features:**
- System analytics
- Revenue charts
- User statistics
- Recent activity
- Quick admin actions

**Signals:**
```typescript
stats = signal<DashboardStats>({...});
isLoading = signal<boolean>(false);
revenueTimeframe = signal<string>('6months');
```

**Methods:**
- `loadDashboardData()`: Fetch admin statistics
- `refreshData()`: Reload dashboard data
- `updateRevenueChart()`: Change revenue timeframe
- `getBarHeight()`: Calculate chart bar heights
- `getPercentage()`: Calculate percentages

**Template Sections:**
1. **Key Metrics**: Revenue, bookings, users, trips
2. **Revenue Chart**: Monthly revenue visualization
3. **Booking Status**: Distribution of booking statuses
4. **Popular Destinations**: Top destination analytics
5. **Recent Activity**: Latest system activity
6. **Quick Actions**: Admin shortcuts
7. **System Status**: Service health indicators

**Chart Types:**
- Bar charts for revenue
- Pie charts for status distribution
- Progress bars for destinations
- Activity timeline

**Dependencies:**
- `AdminService`: Admin statistics
- `AuthService`: Admin authentication

---

## Shared Components

### NotificationComponent
**File:** `src/app/shared/components/notification/notification.component.ts`
**Purpose:** Toast notification system
**Type:** Presentation Component

**Features:**
- Success, error, warning, info notifications
- Auto-dismiss functionality
- Manual dismiss option
- Animation effects
- Queue management

### PWAInstallComponent
**File:** `src/app/shared/components/pwa-install/pwa-install.component.ts`
**Purpose:** Progressive Web App installation prompt
**Type:** Presentation Component

**Features:**
- Install app prompt
- Update notifications
- Offline status indicator
- Network status display

### FooterComponent
**File:** `src/app/shared/components/footer/footer.component.ts`
**Purpose:** Site footer with links and information
**Type:** Presentation Component

**Features:**
- Company information
- Navigation links
- Social media links
- Newsletter signup
- Legal links

## Component Communication Patterns

### Parent-Child Communication
```typescript
// Parent to Child (Input)
@Input() trip: Trip;
@Input() isLoading: boolean;

// Child to Parent (Output)
@Output() bookingRequested = new EventEmitter<number>();
@Output() filterChanged = new EventEmitter<FilterOptions>();
```

### Service-Based Communication
```typescript
// Shared state through services
this.stateService.setCurrentTrip(trip);
this.notificationService.showSuccess('Booking confirmed!');
```

### Signal-Based Reactivity
```typescript
// Reactive updates with signals
readonly trips = computed(() => 
  this.allTrips().filter(trip => 
    trip.category.id === this.selectedCategory()
  )
);
```

## Component Testing Strategy

### Unit Testing
- Component logic testing
- Signal state testing
- Method behavior testing
- Template rendering testing

### Integration Testing
- Service integration testing
- Router navigation testing
- Form submission testing
- API interaction testing

### E2E Testing
- User journey testing
- Cross-component interaction
- Authentication flow testing
- Booking process testing
