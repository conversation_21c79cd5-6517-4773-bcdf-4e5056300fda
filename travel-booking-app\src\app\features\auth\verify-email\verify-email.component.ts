import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-verify-email',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h1>Verify Your Email</h1>
          <p>Please check your email and click the verification link</p>
        </div>
        <div class="placeholder">
          <p>Email verification flow will be implemented here</p>
          <a routerLink="/auth/login" class="btn btn-primary">Back to Login</a>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .auth-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 20px;
    }
    .auth-card {
      background: white;
      border-radius: 20px;
      padding: 40px;
      width: 100%;
      max-width: 450px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
      text-align: center;
    }
    .auth-header h1 {
      font-size: 2rem;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 10px;
    }
    .auth-header p {
      color: #718096;
      margin-bottom: 30px;
    }
    .placeholder p {
      color: #718096;
      margin-bottom: 20px;
    }
    .btn {
      padding: 12px 24px;
      background: #667eea;
      color: white;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
    }
  `]
})
export class VerifyEmailComponent {}
