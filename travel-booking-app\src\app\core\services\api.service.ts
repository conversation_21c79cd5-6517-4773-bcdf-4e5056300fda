import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { ApiResponse, PagedResult, PaginationParameters } from '../models/common.model';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private readonly http = inject(HttpClient);
  private readonly baseUrl = environment.apiUrl || 'https://localhost:7001/api/v1';

  private getHttpOptions(): { headers: HttpHeaders } {
    const token = localStorage.getItem('access_token');
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    });
    return { headers };
  }

  private buildParams(params: any): HttpParams {
    let httpParams = new HttpParams();
    
    Object.keys(params).forEach(key => {
      const value = params[key];
      if (value !== null && value !== undefined && value !== '') {
        if (value instanceof Date) {
          httpParams = httpParams.set(key, value.toISOString());
        } else if (Array.isArray(value)) {
          value.forEach(item => {
            httpParams = httpParams.append(key, item.toString());
          });
        } else {
          httpParams = httpParams.set(key, value.toString());
        }
      }
    });
    
    return httpParams;
  }

  private handleError(error: any): Observable<never> {
    console.error('API Error:', error);
    return throwError(() => error);
  }

  // Generic GET method
  get<T>(endpoint: string, params?: any): Observable<T> {
    const httpParams = params ? this.buildParams(params) : undefined;
    
    return this.http.get<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, {
      ...this.getHttpOptions(),
      params: httpParams
    }).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

  // Generic GET method for paginated results
  getPaged<T>(endpoint: string, pagination: PaginationParameters, filters?: any): Observable<PagedResult<T>> {
    const params = { ...pagination, ...filters };
    const httpParams = this.buildParams(params);
    
    return this.http.get<ApiResponse<PagedResult<T>>>(`${this.baseUrl}/${endpoint}`, {
      ...this.getHttpOptions(),
      params: httpParams
    }).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

  // Generic POST method
  post<T>(endpoint: string, data: any): Observable<T> {
    return this.http.post<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, data, this.getHttpOptions()).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

  // Generic PUT method
  put<T>(endpoint: string, data: any): Observable<T> {
    return this.http.put<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, data, this.getHttpOptions()).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

  // Generic DELETE method
  delete<T>(endpoint: string, data?: any): Observable<T> {
    const options = data ?
      { ...this.getHttpOptions(), body: data } :
      this.getHttpOptions();

    return this.http.delete<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, options).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

  // File upload method
  uploadFile(endpoint: string, file: File, additionalData?: any): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
    }

    const token = localStorage.getItem('access_token');
    const headers = new HttpHeaders({
      ...(token && { 'Authorization': `Bearer ${token}` })
    });

    return this.http.post<ApiResponse<any>>(`${this.baseUrl}/${endpoint}`, formData, { headers }).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

  // Raw HTTP methods (without ApiResponse wrapper)
  getRaw<T>(endpoint: string, params?: any): Observable<T> {
    const httpParams = params ? this.buildParams(params) : undefined;
    
    return this.http.get<T>(`${this.baseUrl}/${endpoint}`, {
      ...this.getHttpOptions(),
      params: httpParams
    }).pipe(
      catchError(this.handleError)
    );
  }

  postRaw<T>(endpoint: string, data: any): Observable<T> {
    return this.http.post<T>(`${this.baseUrl}/${endpoint}`, data, this.getHttpOptions()).pipe(
      catchError(this.handleError)
    );
  }
}
