import { Injectable, signal, computed } from '@angular/core';
import { Trip, TripCategory, TripFilterDto } from '../models/trip.model';
import { Blog, BlogCategory } from '../models/blog.model';
import { Booking } from '../models/booking.model';
import { User } from '../models/user.model';
import { PagedResult } from '../models/common.model';

export interface AppState {
  // User state
  currentUser: User | null;
  isAuthenticated: boolean;
  
  // Trip state
  trips: PagedResult<Trip> | null;
  featuredTrips: Trip[];
  tripCategories: TripCategory[];
  selectedTrip: Trip | null;
  tripFilters: TripFilterDto;
  
  // Blog state
  blogs: PagedResult<Blog> | null;
  featuredBlogs: Blog[];
  blogCategories: BlogCategory[];
  selectedBlog: Blog | null;
  
  // Booking state
  userBookings: PagedResult<Booking> | null;
  currentBooking: Booking | null;
  
  // UI state
  isLoading: boolean;
  sidebarOpen: boolean;
  theme: 'light' | 'dark';
  
  // Admin state
  adminStats: any;
}

@Injectable({
  providedIn: 'root'
})
export class StateService {
  // Initial state
  private initialState: AppState = {
    currentUser: null,
    isAuthenticated: false,
    trips: null,
    featuredTrips: [],
    tripCategories: [],
    selectedTrip: null,
    tripFilters: {},
    blogs: null,
    featuredBlogs: [],
    blogCategories: [],
    selectedBlog: null,
    userBookings: null,
    currentBooking: null,
    isLoading: false,
    sidebarOpen: false,
    theme: 'light',
    adminStats: null
  };

  // State signals
  private _state = signal<AppState>(this.initialState);

  // Computed selectors
  readonly state = this._state.asReadonly();
  readonly currentUser = computed(() => this._state().currentUser);
  readonly isAuthenticated = computed(() => this._state().isAuthenticated);
  readonly trips = computed(() => this._state().trips);
  readonly featuredTrips = computed(() => this._state().featuredTrips);
  readonly tripCategories = computed(() => this._state().tripCategories);
  readonly selectedTrip = computed(() => this._state().selectedTrip);
  readonly tripFilters = computed(() => this._state().tripFilters);
  readonly blogs = computed(() => this._state().blogs);
  readonly featuredBlogs = computed(() => this._state().featuredBlogs);
  readonly blogCategories = computed(() => this._state().blogCategories);
  readonly selectedBlog = computed(() => this._state().selectedBlog);
  readonly userBookings = computed(() => this._state().userBookings);
  readonly currentBooking = computed(() => this._state().currentBooking);
  readonly isLoading = computed(() => this._state().isLoading);
  readonly sidebarOpen = computed(() => this._state().sidebarOpen);
  readonly theme = computed(() => this._state().theme);
  readonly adminStats = computed(() => this._state().adminStats);

  // State update methods
  updateState(partialState: Partial<AppState>): void {
    this._state.update(state => ({ ...state, ...partialState }));
  }

  // User actions
  setCurrentUser(user: User | null): void {
    this.updateState({ 
      currentUser: user, 
      isAuthenticated: !!user 
    });
  }

  // Trip actions
  setTrips(trips: PagedResult<Trip>): void {
    this.updateState({ trips });
  }

  setFeaturedTrips(featuredTrips: Trip[]): void {
    this.updateState({ featuredTrips });
  }

  setTripCategories(tripCategories: TripCategory[]): void {
    this.updateState({ tripCategories });
  }

  setSelectedTrip(selectedTrip: Trip | null): void {
    this.updateState({ selectedTrip });
  }

  setTripFilters(tripFilters: TripFilterDto): void {
    this.updateState({ tripFilters });
  }

  updateTripFilters(partialFilters: Partial<TripFilterDto>): void {
    const currentFilters = this._state().tripFilters;
    this.updateState({ 
      tripFilters: { ...currentFilters, ...partialFilters } 
    });
  }

  clearTripFilters(): void {
    this.updateState({ tripFilters: {} });
  }

  // Blog actions
  setBlogs(blogs: PagedResult<Blog>): void {
    this.updateState({ blogs });
  }

  setFeaturedBlogs(featuredBlogs: Blog[]): void {
    this.updateState({ featuredBlogs });
  }

  setBlogCategories(blogCategories: BlogCategory[]): void {
    this.updateState({ blogCategories });
  }

  setSelectedBlog(selectedBlog: Blog | null): void {
    this.updateState({ selectedBlog });
  }

  // Booking actions
  setUserBookings(userBookings: PagedResult<Booking>): void {
    this.updateState({ userBookings });
  }

  setCurrentBooking(currentBooking: Booking | null): void {
    this.updateState({ currentBooking });
  }

  // UI actions
  setLoading(isLoading: boolean): void {
    this.updateState({ isLoading });
  }

  toggleSidebar(): void {
    this.updateState({ sidebarOpen: !this._state().sidebarOpen });
  }

  setSidebarOpen(sidebarOpen: boolean): void {
    this.updateState({ sidebarOpen });
  }

  setTheme(theme: 'light' | 'dark'): void {
    this.updateState({ theme });
    localStorage.setItem('theme', theme);
  }

  toggleTheme(): void {
    const currentTheme = this._state().theme;
    this.setTheme(currentTheme === 'light' ? 'dark' : 'light');
  }

  // Admin actions
  setAdminStats(adminStats: any): void {
    this.updateState({ adminStats });
  }

  // Reset actions
  resetState(): void {
    this._state.set(this.initialState);
  }

  resetUserState(): void {
    this.updateState({
      currentUser: null,
      isAuthenticated: false,
      userBookings: null,
      currentBooking: null
    });
  }

  // Persistence
  saveStateToStorage(): void {
    const stateToSave = {
      theme: this._state().theme,
      tripFilters: this._state().tripFilters,
      sidebarOpen: this._state().sidebarOpen
    };
    localStorage.setItem('app_state', JSON.stringify(stateToSave));
  }

  loadStateFromStorage(): void {
    const savedState = localStorage.getItem('app_state');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        this.updateState(parsedState);
      } catch (error) {
        console.error('Error loading state from storage:', error);
      }
    }
  }

  // Initialize state
  initialize(): void {
    this.loadStateFromStorage();
  }
}
