import { Injectable, inject, signal } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, BehaviorSubject, throwError, of } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import { ApiService } from './api.service';
import { 
  LoginDto, 
  RegisterDto, 
  ForgotPasswordDto, 
  ResetPasswordDto, 
  VerifyEmailDto, 
  AuthResultDto, 
  TokenDto,
  RefreshTokenRequest,
  ChangePasswordDto 
} from '../models/auth.model';
import { User } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly apiService = inject(ApiService);
  private readonly router = inject(Router);

  // Signals for reactive state management
  private readonly _currentUser = signal<User | null>(null);
  private readonly _isAuthenticated = signal<boolean>(false);
  private readonly _isLoading = signal<boolean>(false);

  // Public readonly signals
  readonly currentUser = this._currentUser.asReadonly();
  readonly isAuthenticated = this._isAuthenticated.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();

  // Legacy observables for compatibility
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    this.initializeAuth();
  }

  private initializeAuth(): void {
    const token = this.getStoredToken();
    const user = this.getStoredUser();
    
    if (token && user && !this.isTokenExpired(token)) {
      this.setCurrentUser(user);
      this._isAuthenticated.set(true);
    } else {
      this.clearAuth();
    }
  }

  login(credentials: LoginDto): Observable<AuthResultDto> {
    this._isLoading.set(true);
    
    return this.apiService.post<AuthResultDto>('auth/login', credentials).pipe(
      tap(result => {
        if (result.isSuccess) {
          this.handleAuthSuccess(result);
        }
      }),
      catchError(error => {
        this._isLoading.set(false);
        return throwError(() => error);
      }),
      tap(() => this._isLoading.set(false))
    );
  }

  register(userData: RegisterDto): Observable<AuthResultDto> {
    this._isLoading.set(true);
    
    return this.apiService.post<AuthResultDto>('auth/register', userData).pipe(
      tap(result => {
        if (result.isSuccess) {
          // Don't auto-login after registration, user needs to verify email
          this._isLoading.set(false);
        }
      }),
      catchError(error => {
        this._isLoading.set(false);
        return throwError(() => error);
      }),
      tap(() => this._isLoading.set(false))
    );
  }

  verifyEmail(verificationData: VerifyEmailDto): Observable<AuthResultDto> {
    return this.apiService.post<AuthResultDto>('auth/verify-email', verificationData).pipe(
      tap(result => {
        if (result.isSuccess) {
          this.handleAuthSuccess(result);
        }
      })
    );
  }

  forgotPassword(forgotPasswordData: ForgotPasswordDto): Observable<any> {
    return this.apiService.post('auth/forgot-password', forgotPasswordData);
  }

  resetPassword(resetPasswordData: ResetPasswordDto): Observable<any> {
    return this.apiService.post('auth/reset-password', resetPasswordData);
  }

  changePassword(changePasswordData: ChangePasswordDto): Observable<any> {
    return this.apiService.post('auth/change-password', changePasswordData);
  }

  refreshToken(): Observable<TokenDto> {
    const refreshToken = localStorage.getItem('refresh_token');
    
    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    const request: RefreshTokenRequest = { refreshToken };
    
    return this.apiService.post<TokenDto>('auth/refresh-token', request).pipe(
      tap(tokenData => {
        this.storeTokens(tokenData);
      }),
      catchError(error => {
        this.logout();
        return throwError(() => error);
      })
    );
  }

  logout(): void {
    this.clearAuth();
    this.router.navigate(['/auth/login']);
  }

  isLoggedIn(): boolean {
    const token = this.getStoredToken();
    return !!token && !this.isTokenExpired(token);
  }

  isAdmin(): boolean {
    const user = this._currentUser();
    return user?.role === 2; // Admin role
  }

  getToken(): string | null {
    return localStorage.getItem('access_token');
  }

  private handleAuthSuccess(authResult: AuthResultDto): void {
    this.storeTokens(authResult.token);
    this.storeUser(authResult.user);
    this.setCurrentUser(authResult.user);
    this._isAuthenticated.set(true);
  }

  private setCurrentUser(user: User): void {
    this._currentUser.set(user);
    this.currentUserSubject.next(user);
  }

  private storeTokens(tokenData: TokenDto): void {
    localStorage.setItem('access_token', tokenData.accessToken);
    localStorage.setItem('refresh_token', tokenData.refreshToken);
    localStorage.setItem('token_expires_in', tokenData.expiresIn.toString());
    localStorage.setItem('token_stored_at', Date.now().toString());
  }

  private storeUser(user: User): void {
    localStorage.setItem('current_user', JSON.stringify(user));
  }

  private getStoredToken(): string | null {
    return localStorage.getItem('access_token');
  }

  private getStoredUser(): User | null {
    const userJson = localStorage.getItem('current_user');
    return userJson ? JSON.parse(userJson) : null;
  }

  private isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expirationTime = payload.exp * 1000; // Convert to milliseconds
      return Date.now() >= expirationTime;
    } catch {
      return true;
    }
  }

  private clearAuth(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('token_expires_in');
    localStorage.removeItem('token_stored_at');
    localStorage.removeItem('current_user');
    this._currentUser.set(null);
    this._isAuthenticated.set(false);
    this.currentUserSubject.next(null);
  }
}
