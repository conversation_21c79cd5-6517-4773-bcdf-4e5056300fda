.footer {
  background: #2d3748;
  color: #e2e8f0;
  margin-top: auto;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1.5fr;
    gap: 40px;
    padding: 60px 0 40px;

    @media (max-width: 1024px) {
      grid-template-columns: 2fr 1fr 1fr;
      gap: 30px;
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr 1fr;
      gap: 30px;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: 30px;
      text-align: center;
    }
  }

  .footer-section {
    h3 {
      font-size: 1.1rem;
      font-weight: 600;
      color: white;
      margin-bottom: 20px;
    }

    &:first-child {
      @media (max-width: 768px) {
        grid-column: 1 / -1;
      }
    }
  }

  .footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 700;
    color: white;

    @media (max-width: 480px) {
      justify-content: center;
    }

    .logo-icon {
      font-size: 1.5rem;
    }
  }

  .footer-description {
    color: #a0aec0;
    line-height: 1.6;
    margin-bottom: 25px;
    max-width: 300px;

    @media (max-width: 480px) {
      max-width: none;
    }
  }

  .social-links {
    display: flex;
    gap: 15px;

    @media (max-width: 480px) {
      justify-content: center;
    }

    .social-link {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: #4a5568;
      border-radius: 8px;
      text-decoration: none;
      font-size: 1.2rem;
      transition: all 0.3s ease;

      &:hover {
        background: #667eea;
        transform: translateY(-2px);
      }
    }
  }

  .footer-links {
    list-style: none;
    margin: 0;
    padding: 0;

    li {
      margin-bottom: 12px;

      a {
        color: #a0aec0;
        text-decoration: none;
        transition: color 0.3s ease;
        font-size: 0.9rem;

        &:hover {
          color: white;
        }
      }
    }
  }

  .newsletter {
    p {
      color: #a0aec0;
      margin-bottom: 20px;
      font-size: 0.9rem;
      line-height: 1.5;
    }

    .newsletter-form {
      .input-group {
        display: flex;
        gap: 8px;
        margin-bottom: 15px;

        @media (max-width: 480px) {
          flex-direction: column;
        }

        input {
          flex: 1;
          padding: 10px 12px;
          border: 1px solid #4a5568;
          border-radius: 6px;
          background: #4a5568;
          color: white;
          font-size: 0.9rem;

          &::placeholder {
            color: #a0aec0;
          }

          &:focus {
            outline: none;
            border-color: #667eea;
            background: #2d3748;
          }
        }

        .btn {
          padding: 10px 16px;
          border: none;
          border-radius: 6px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 0.9rem;
          white-space: nowrap;

          &.btn-primary {
            background: #667eea;
            color: white;

            &:hover {
              background: #5a67d8;
            }
          }
        }
      }
    }
  }

  .footer-bottom {
    border-top: 1px solid #4a5568;
    padding: 25px 0;

    .footer-bottom-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .copyright {
        p {
          margin: 0;
          color: #a0aec0;
          font-size: 0.9rem;
        }
      }

      .footer-bottom-links {
        display: flex;
        gap: 25px;

        @media (max-width: 480px) {
          gap: 15px;
          flex-wrap: wrap;
          justify-content: center;
        }

        a {
          color: #a0aec0;
          text-decoration: none;
          font-size: 0.9rem;
          transition: color 0.3s ease;

          &:hover {
            color: white;
          }
        }
      }
    }
  }
}
