export interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  dateOfBirth?: Date;
  gender?: Gender;
  profileImageUrl?: string;
  address?: Address;
  role: UserRole;
  isEmailVerified: boolean;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt?: Date;
}

export interface Address {
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
}

export interface UserProfile {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  dateOfBirth?: Date;
  gender?: Gender;
  profileImageUrl?: string;
  address?: Address;
}

export interface UpdateUserDto {
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  dateOfBirth?: Date;
  gender?: Gender;
  address?: Address;
}

export interface UserFilterDto {
  search?: string;
  role?: UserRole;
  isActive?: boolean;
  country?: string;
  page?: number;
  pageSize?: number;
}

export enum UserRole {
  User = 1,
  Admin = 2
}

export enum Gender {
  Male = 1,
  Female = 2,
  Other = 3
}
