export interface ApiResponse<T = any> {
  data: T;
  message: string;
  isSuccess: boolean;
  errors?: string[];
  statusCode: number;
}

export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}

export interface PaginationParameters {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FileUploadDto {
  file: File;
  fileName?: string;
  folder?: string;
}

export interface FileUploadResponse {
  url: string;
  publicId: string;
  fileName: string;
  size: number;
  format: string;
}

export interface ErrorResponse {
  message: string;
  errors?: ValidationError[];
  statusCode: number;
  timestamp: Date;
  path: string;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface SelectOption {
  value: any;
  label: string;
  disabled?: boolean;
}

export interface FilterOption {
  key: string;
  value: any;
  label: string;
}

export interface SortOption {
  key: string;
  label: string;
  direction: 'asc' | 'desc';
}

export interface SearchResult<T> {
  items: T[];
  totalCount: number;
  searchTerm: string;
  filters: FilterOption[];
  sort: SortOption;
}

export interface DashboardStats {
  totalUsers: number;
  totalTrips: number;
  totalBookings: number;
  totalRevenue: number;
  monthlyBookings: MonthlyData[];
  monthlyRevenue: MonthlyData[];
  popularDestinations: PopularDestination[];
  recentBookings: RecentBooking[];
}

export interface MonthlyData {
  month: string;
  value: number;
}

export interface PopularDestination {
  cityName: string;
  countryName: string;
  bookingCount: number;
  revenue: number;
}

export interface RecentBooking {
  id: number;
  bookingNumber: string;
  userName: string;
  tripName: string;
  amount: number;
  status: string;
  createdAt: Date;
}

export interface Category {
  id: number;
  name: string;
  description?: string;
  iconUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}
