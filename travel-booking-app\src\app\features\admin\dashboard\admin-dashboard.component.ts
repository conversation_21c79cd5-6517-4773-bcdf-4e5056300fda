import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AdminService, AuthService } from '../../../core/services';

interface DashboardStats {
  totalUsers: number;
  totalBookings: number;
  totalRevenue: number;
  totalTrips: number;
  pendingBookings: number;
  activeUsers: number;
  popularDestinations: Array<{name: string; bookings: number}>;
  recentBookings: Array<any>;
  monthlyRevenue: Array<{month: string; revenue: number}>;
  bookingsByStatus: Array<{status: string; count: number}>;
}

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <div class="admin-dashboard-container">
      <div class="container">
        <div class="admin-header">
          <h1>Admin Dashboard</h1>
          <p>Welcome back, {{ currentUser()?.firstName }}! Here's what's happening with your platform.</p>
          <div class="last-updated">
            Last updated: {{ lastUpdated | date:'medium' }}
            <button class="refresh-btn" (click)="refreshData()" [disabled]="isLoading()">
              <span [class.spinning]="isLoading()">🔄</span>
            </button>
          </div>
        </div>

        @if (isLoading()) {
          <div class="loading-state">
            <div class="spinner"></div>
            <p>Loading dashboard data...</p>
          </div>
        } @else {
          <!-- Key Metrics -->
          <div class="metrics-grid">
            <div class="metric-card revenue">
              <div class="metric-icon">💰</div>
              <div class="metric-content">
                <div class="metric-value">\${{ stats().totalRevenue | number:'1.0-0' }}</div>
                <div class="metric-label">Total Revenue</div>
                <div class="metric-change positive">+12.5% from last month</div>
              </div>
            </div>

            <div class="metric-card bookings">
              <div class="metric-icon">📋</div>
              <div class="metric-content">
                <div class="metric-value">{{ stats().totalBookings | number }}</div>
                <div class="metric-label">Total Bookings</div>
                <div class="metric-change positive">+8.3% from last month</div>
              </div>
            </div>

            <div class="metric-card users">
              <div class="metric-icon">👥</div>
              <div class="metric-content">
                <div class="metric-value">{{ stats().totalUsers | number }}</div>
                <div class="metric-label">Total Users</div>
                <div class="metric-change positive">+15.2% from last month</div>
              </div>
            </div>

            <div class="metric-card trips">
              <div class="metric-icon">🎒</div>
              <div class="metric-content">
                <div class="metric-value">{{ stats().totalTrips | number }}</div>
                <div class="metric-label">Active Trips</div>
                <div class="metric-change neutral">No change</div>
              </div>
            </div>
          </div>

          <!-- Charts and Analytics -->
          <div class="analytics-grid">
            <!-- Revenue Chart -->
            <div class="chart-card">
              <div class="chart-header">
                <h3>Monthly Revenue</h3>
                <div class="chart-controls">
                  <select [(ngModel)]="revenueTimeframe" (change)="updateRevenueChart()">
                    <option value="6months">Last 6 Months</option>
                    <option value="12months">Last 12 Months</option>
                    <option value="year">This Year</option>
                  </select>
                </div>
              </div>
              <div class="chart-content">
                <div class="simple-chart">
                  @for (item of stats().monthlyRevenue; track item.month) {
                    <div class="chart-bar">
                      <div class="bar" [style.height.%]="getBarHeight(item.revenue, stats().monthlyRevenue)"></div>
                      <div class="bar-label">{{ item.month }}</div>
                      <div class="bar-value">\${{ item.revenue | number:'1.0-0' }}</div>
                    </div>
                  }
                </div>
              </div>
            </div>

            <!-- Booking Status Distribution -->
            <div class="chart-card">
              <div class="chart-header">
                <h3>Booking Status Distribution</h3>
              </div>
              <div class="chart-content">
                <div class="status-chart">
                  @for (item of stats().bookingsByStatus; track item.status) {
                    <div class="status-item">
                      <div class="status-indicator" [class]="'status-' + item.status.toLowerCase()"></div>
                      <div class="status-info">
                        <div class="status-label">{{ item.status }}</div>
                        <div class="status-count">{{ item.count }} bookings</div>
                      </div>
                      <div class="status-percentage">
                        {{ getPercentage(item.count, getTotalBookings()) }}%
                      </div>
                    </div>
                  }
                </div>
              </div>
            </div>

            <!-- Popular Destinations -->
            <div class="chart-card">
              <div class="chart-header">
                <h3>Popular Destinations</h3>
                <a routerLink="/admin/trips" class="view-all">View All Trips</a>
              </div>
              <div class="chart-content">
                <div class="destinations-list">
                  @for (destination of stats().popularDestinations; track destination.name; let i = $index) {
                    <div class="destination-item">
                      <div class="destination-rank">{{ i + 1 }}</div>
                      <div class="destination-info">
                        <div class="destination-name">{{ destination.name }}</div>
                        <div class="destination-bookings">{{ destination.bookings }} bookings</div>
                      </div>
                      <div class="destination-bar">
                        <div class="bar-fill" [style.width.%]="getDestinationPercentage(destination.bookings)"></div>
                      </div>
                    </div>
                  }
                </div>
              </div>
            </div>

            <!-- Recent Activity -->
            <div class="chart-card recent-activity">
              <div class="chart-header">
                <h3>Recent Bookings</h3>
                <a routerLink="/admin/bookings" class="view-all">View All</a>
              </div>
              <div class="chart-content">
                <div class="activity-list">
                  @for (booking of stats().recentBookings; track booking.id) {
                    <div class="activity-item">
                      <div class="activity-avatar">
                        {{ getInitials(booking.user.firstName, booking.user.lastName) }}
                      </div>
                      <div class="activity-info">
                        <div class="activity-title">
                          {{ booking.user.firstName }} {{ booking.user.lastName }} booked {{ booking.trip.name }}
                        </div>
                        <div class="activity-meta">
                          {{ booking.createdAt | date:'short' }} • \${{ booking.totalAmount }}
                        </div>
                      </div>
                      <div class="activity-status" [class]="'status-' + booking.status">
                        {{ getBookingStatusText(booking.status) }}
                      </div>
                    </div>
                  }
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="quick-actions-section">
            <h2>Quick Actions</h2>
            <div class="actions-grid">
              <a routerLink="/admin/trips" class="action-card">
                <div class="action-icon">🎒</div>
                <div class="action-content">
                  <h3>Manage Trips</h3>
                  <p>Add, edit, or remove travel packages</p>
                </div>
                <div class="action-arrow">→</div>
              </a>

              <a routerLink="/admin/users" class="action-card">
                <div class="action-icon">👥</div>
                <div class="action-content">
                  <h3>User Management</h3>
                  <p>View and manage user accounts</p>
                </div>
                <div class="action-arrow">→</div>
              </a>

              <a routerLink="/admin/bookings" class="action-card">
                <div class="action-icon">📋</div>
                <div class="action-content">
                  <h3>Booking Management</h3>
                  <p>Process and track bookings</p>
                </div>
                <div class="action-arrow">→</div>
              </a>

              <a routerLink="/admin/blogs" class="action-card">
                <div class="action-icon">📝</div>
                <div class="action-content">
                  <h3>Content Management</h3>
                  <p>Create and publish blog posts</p>
                </div>
                <div class="action-arrow">→</div>
              </a>
            </div>
          </div>

          <!-- System Status -->
          <div class="system-status">
            <h2>System Status</h2>
            <div class="status-grid">
              <div class="status-item">
                <div class="status-indicator online"></div>
                <div class="status-label">API Server</div>
                <div class="status-value">Online</div>
              </div>
              <div class="status-item">
                <div class="status-indicator online"></div>
                <div class="status-label">Database</div>
                <div class="status-value">Healthy</div>
              </div>
              <div class="status-item">
                <div class="status-indicator warning"></div>
                <div class="status-label">Payment Gateway</div>
                <div class="status-value">Slow Response</div>
              </div>
              <div class="status-item">
                <div class="status-indicator online"></div>
                <div class="status-label">Email Service</div>
                <div class="status-value">Active</div>
              </div>
            </div>
          </div>
        }
      </div>
    </div>
  `,
  styleUrls: ['./admin-dashboard.component.scss']
})
export class AdminDashboardComponent implements OnInit {
  private readonly adminService = inject(AdminService);
  private readonly authService = inject(AuthService);

  // Signals
  stats = signal<DashboardStats>({
    totalUsers: 0,
    totalBookings: 0,
    totalRevenue: 0,
    totalTrips: 0,
    pendingBookings: 0,
    activeUsers: 0,
    popularDestinations: [],
    recentBookings: [],
    monthlyRevenue: [],
    bookingsByStatus: []
  });

  isLoading = signal<boolean>(false);
  lastUpdated = new Date();
  revenueTimeframe = signal<string>('6months');

  // Computed properties
  currentUser = this.authService.currentUser;

  ngOnInit(): void {
    this.loadDashboardData();
  }

  async loadDashboardData(): Promise<void> {
    this.isLoading.set(true);

    try {
      // Simulate API call - replace with actual service calls
      await this.delay(1000);

      const mockStats: DashboardStats = {
        totalUsers: 1234,
        totalBookings: 567,
        totalRevenue: 125000,
        totalTrips: 45,
        pendingBookings: 23,
        activeUsers: 89,
        popularDestinations: [
          { name: 'Bali, Indonesia', bookings: 45 },
          { name: 'Tokyo, Japan', bookings: 38 },
          { name: 'Paris, France', bookings: 32 },
          { name: 'New York, USA', bookings: 28 },
          { name: 'London, UK', bookings: 25 }
        ],
        recentBookings: [
          {
            id: 1,
            user: { firstName: 'John', lastName: 'Doe' },
            trip: { name: 'Bali Adventure' },
            totalAmount: 1200,
            status: 2,
            createdAt: new Date()
          },
          {
            id: 2,
            user: { firstName: 'Jane', lastName: 'Smith' },
            trip: { name: 'Tokyo Explorer' },
            totalAmount: 1800,
            status: 1,
            createdAt: new Date()
          }
        ],
        monthlyRevenue: [
          { month: 'Jan', revenue: 18000 },
          { month: 'Feb', revenue: 22000 },
          { month: 'Mar', revenue: 19000 },
          { month: 'Apr', revenue: 25000 },
          { month: 'May', revenue: 21000 },
          { month: 'Jun', revenue: 28000 }
        ],
        bookingsByStatus: [
          { status: 'Confirmed', count: 234 },
          { status: 'Pending', count: 45 },
          { status: 'Cancelled', count: 12 },
          { status: 'Completed', count: 276 }
        ]
      };

      this.stats.set(mockStats);
      this.lastUpdated = new Date();
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  refreshData(): void {
    this.loadDashboardData();
  }

  updateRevenueChart(): void {
    // TODO: Update chart based on timeframe
    console.log('Updating revenue chart for:', this.revenueTimeframe());
  }

  getBarHeight(value: number, data: Array<{revenue: number}>): number {
    const max = Math.max(...data.map(item => item.revenue));
    return (value / max) * 100;
  }

  getPercentage(value: number, total: number): number {
    return Math.round((value / total) * 100);
  }

  getTotalBookings(): number {
    return this.stats().bookingsByStatus.reduce((sum, item) => sum + item.count, 0);
  }

  getDestinationPercentage(bookings: number): number {
    const max = Math.max(...this.stats().popularDestinations.map(d => d.bookings));
    return (bookings / max) * 100;
  }

  getInitials(firstName: string, lastName: string): string {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  }

  getBookingStatusText(status: number): string {
    const statusMap = {
      1: 'Pending',
      2: 'Confirmed',
      3: 'Cancelled',
      4: 'Completed'
    };
    return statusMap[status as keyof typeof statusMap] || 'Unknown';
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
