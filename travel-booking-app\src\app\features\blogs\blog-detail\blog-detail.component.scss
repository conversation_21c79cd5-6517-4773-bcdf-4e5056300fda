.blog-detail-container {
  min-height: 100vh;
  background: #f8f9fa;
}

// Blog Header
.blog-header {
  background: white;
  padding: 40px 0 60px;

  .breadcrumb {
    margin-bottom: 20px;
    font-size: 0.9rem;
    color: #718096;

    a {
      color: #667eea;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    span {
      margin: 0 8px;
    }
  }

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.3;
    color: #2d3748;
    margin-bottom: 25px;
    max-width: 800px;

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  .blog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e2e8f0;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }

    .author-info {
      display: flex;
      flex-direction: column;
      gap: 5px;

      .author {
        font-weight: 600;
        color: #2d3748;
      }

      .publish-date {
        color: #718096;
        font-size: 0.9rem;
      }
    }

    .blog-stats {
      display: flex;
      gap: 20px;
      font-size: 0.9rem;
      color: #718096;

      span {
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }
  }

  .featured-image {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

    img {
      width: 100%;
      height: 400px;
      object-fit: cover;

      @media (max-width: 768px) {
        height: 250px;
      }
    }
  }
}

// Blog Content
.blog-content {
  padding: 60px 0;

  .container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 60px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 40px;
    }
  }

  .content-wrapper {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

    @media (max-width: 768px) {
      padding: 25px;
    }
  }

  .article-content {
    line-height: 1.8;
    color: #2d3748;
    font-size: 1.1rem;

    // Style the HTML content
    h2, h3, h4 {
      margin: 30px 0 15px;
      color: #2d3748;
      font-weight: 600;
    }

    h2 {
      font-size: 1.8rem;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
    }

    h3 {
      font-size: 1.4rem;
    }

    h4 {
      font-size: 1.2rem;
    }

    p {
      margin-bottom: 20px;
    }

    ul, ol {
      margin: 20px 0;
      padding-left: 30px;

      li {
        margin-bottom: 8px;
      }
    }

    blockquote {
      border-left: 4px solid #667eea;
      padding: 20px;
      margin: 30px 0;
      background: #f7fafc;
      border-radius: 0 8px 8px 0;
      font-style: italic;
      color: #4a5568;
    }

    img {
      max-width: 100%;
      height: auto;
      border-radius: 8px;
      margin: 20px 0;
    }

    code {
      background: #edf2f7;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
    }

    pre {
      background: #2d3748;
      color: white;
      padding: 20px;
      border-radius: 8px;
      overflow-x: auto;
      margin: 20px 0;

      code {
        background: none;
        padding: 0;
        color: inherit;
      }
    }
  }

  .tags-section {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #e2e8f0;

    h3 {
      font-size: 1.2rem;
      margin-bottom: 15px;
      color: #2d3748;
    }

    .tags {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;

      .tag {
        background: #edf2f7;
        color: #4a5568;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.9rem;
        font-weight: 500;
      }
    }
  }

  .share-section {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #e2e8f0;

    h3 {
      font-size: 1.2rem;
      margin-bottom: 15px;
      color: #2d3748;
    }

    .share-buttons {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;

      .share-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;

        &.facebook {
          background: #1877f2;
          color: white;

          &:hover {
            background: #166fe5;
          }
        }

        &.twitter {
          background: #1da1f2;
          color: white;

          &:hover {
            background: #0d8bd9;
          }
        }

        &.linkedin {
          background: #0077b5;
          color: white;

          &:hover {
            background: #005885;
          }
        }

        &.copy {
          background: #edf2f7;
          color: #4a5568;

          &:hover {
            background: #e2e8f0;
          }
        }
      }
    }
  }
}

// Sidebar
.blog-sidebar {
  .author-card,
  .related-articles {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;

    h3 {
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 20px;
      color: #2d3748;
    }
  }

  .author-card {
    .author-details {
      .author-name {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 10px;
      }

      .author-bio {
        color: #718096;
        line-height: 1.5;
        font-size: 0.95rem;
      }
    }
  }

  .related-articles {
    .related-list {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .related-item {
      display: flex;
      gap: 15px;
      text-decoration: none;
      color: inherit;
      transition: all 0.3s ease;
      padding: 10px;
      border-radius: 8px;

      &:hover {
        background: #f7fafc;
        transform: translateX(5px);
      }

      .related-image {
        flex-shrink: 0;
        width: 80px;
        height: 60px;
        border-radius: 6px;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .related-content {
        flex: 1;

        h4 {
          font-size: 0.95rem;
          font-weight: 600;
          line-height: 1.3;
          margin-bottom: 5px;
          color: #2d3748;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .related-date {
          font-size: 0.8rem;
          color: #718096;
        }
      }
    }
  }
}

// Navigation
.blog-navigation {
  background: white;
  padding: 30px 0;
  border-top: 1px solid #e2e8f0;

  .nav-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 15px;
    }
  }
}

// Error State
.error-state {
  text-align: center;
  padding: 100px 20px;
  color: #718096;

  h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #4a5568;
  }

  p {
    margin-bottom: 30px;
    font-size: 1.1rem;
  }
}

// Loading Skeleton
.loading-skeleton {
  .skeleton-header {
    height: 400px;
    background: #f7fafc;
    animation: pulse 1.5s ease-in-out infinite alternate;
  }

  .skeleton-content {
    padding: 60px 20px;
    max-width: 1200px;
    margin: 0 auto;

    &::before {
      content: '';
      display: block;
      height: 600px;
      background: #f7fafc;
      border-radius: 15px;
      animation: pulse 1.5s ease-in-out infinite alternate;
    }
  }
}

// Common Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover {
      background: #5a67d8;
    }
  }

  &.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;

    &:hover {
      background: #667eea;
      color: white;
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  100% { opacity: 0.4; }
}
