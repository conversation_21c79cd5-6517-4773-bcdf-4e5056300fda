# Travel Booking App

A modern, responsive travel booking platform built with Angular 20, featuring zoneless change detection, comprehensive animations, and a beautiful Material Design-inspired UI. This frontend application is designed to work with the TravelTourism.API backend.

## 🚀 Features

### Core Features
- **Trip Discovery**: Browse and search through curated travel experiences
- **User Authentication**: Secure login/registration with JWT tokens
- **Booking Management**: Complete booking flow with payment integration
- **Travel Blog**: Inspiring travel stories and destination guides
- **User Dashboard**: Personal dashboard for managing bookings and preferences
- **Admin Panel**: Comprehensive admin interface for content management

### Technical Features
- **Angular 20**: Latest Angular with zoneless change detection
- **Standalone Components**: Modern Angular architecture
- **Progressive Web App**: PWA capabilities with offline support
- **Advanced Animations**: Smooth transitions, parallax effects, and micro-interactions
- **Performance Optimized**: Lazy loading, code splitting, and caching strategies
- **Responsive Design**: Mobile-first responsive design
- **SEO Optimized**: Meta tags, structured data, and social media integration

## 🛠️ Tech Stack

- **Frontend**: Angular 20, TypeScript, SCSS
- **State Management**: Angular Signals + Custom State Service
- **HTTP Client**: Angular HTTP Client with interceptors
- **Routing**: Angular Router with guards and lazy loading
- **Forms**: Reactive Forms with validation
- **PWA**: Service Worker, Web App Manifest
- **Animations**: Custom animation service with Web Animations API
- **Testing**: Jasmine, Karma, Angular Testing Utilities

## 📁 Project Structure

```
src/
├── app/
│   ├── core/                    # Core functionality
│   │   ├── guards/             # Route guards (auth, admin)
│   │   ├── interceptors/       # HTTP interceptors (auth, error)
│   │   ├── models/             # TypeScript interfaces and types
│   │   └── services/           # Core services (API, auth, state)
│   ├── features/               # Feature modules (lazy-loaded)
│   │   ├── auth/              # Authentication (login, register)
│   │   ├── trips/             # Trip browsing and details
│   │   ├── blogs/             # Blog system
│   │   ├── bookings/          # Booking management
│   │   ├── dashboard/         # User dashboard
│   │   ├── profile/           # User profile management
│   │   └── admin/             # Admin panel
│   ├── shared/                # Shared components and utilities
│   │   ├── components/        # Reusable UI components
│   │   └── directives/        # Custom directives (animations)
│   └── environments/          # Environment configurations
├── assets/                    # Static assets
└── styles/                    # Global styles and themes
```

## 🚀 Getting Started

### Prerequisites
- **Node.js**: v18 or higher
- **npm**: v8 or higher (or yarn v1.22+)
- **Angular CLI**: v20 or higher
- **Backend API**: TravelTourism.API running on `https://localhost:7001`

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd travel-booking-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   # Update API URL and other configurations
   # Edit src/environments/environment.ts
   ```

4. **Start the development server**
   ```bash
   ng serve
   ```

5. **Open your browser**
   Navigate to `http://localhost:4200/`

### Environment Configuration

Update `src/environments/environment.ts`:

```typescript
export const environment = {
  production: false,
  apiUrl: 'https://localhost:7001/api/v1',
  
  // External service keys (optional)
  googleMapsApiKey: 'your-google-maps-key',
  stripePublishableKey: 'your-stripe-key',
  
  // Feature flags
  enablePWA: true,
  enableAnalytics: false,
  enableAnimations: true,
  
  // App configuration
  appName: 'Travel Booking App',
  appVersion: '1.0.0',
  supportEmail: '<EMAIL>'
};
```

## 🏗️ Development

### Code Scaffolding

```bash
# Generate a new feature component
ng generate component features/feature-name/component-name

# Generate a service
ng generate service core/services/service-name

# Generate a guard
ng generate guard core/guards/guard-name
```

### Building for Production

```bash
# Build for production
ng build --configuration production

# Analyze bundle size
ng build --stats-json
npx webpack-bundle-analyzer dist/travel-booking-app/stats.json
```

### Running Tests

```bash
# Unit tests
ng test

# Unit tests with coverage
ng test --code-coverage

# E2E tests (if configured)
ng e2e
```

## 🔧 API Integration

This application is designed to work with the **TravelTourism.API** backend. Ensure the API is running and accessible at the configured URL.

### API Endpoints Used

- **Authentication**: `/api/v1/auth/*`
- **Trips**: `/api/v1/trips/*`
- **Bookings**: `/api/v1/bookings/*`
- **Blogs**: `/api/v1/blogs/*`
- **Admin**: `/api/v1/admin/*`
- **Users**: `/api/v1/users/*`

### API Models Compatibility

The Angular models in `src/app/core/models/` are designed to match the DTOs from the TravelTourism.API:

- `Trip` ↔ `TripDto`
- `Booking` ↔ `BookingDto`
- `Blog` ↔ `BlogDto`
- `User` ↔ `UserDto`
- `AuthResult` ↔ `AuthResultDto`

## 🎨 Styling and Theming

### Design System

The app uses a custom design system with:

- **Colors**: Primary (#667eea), Secondary (#764ba2), Success (#38a169), etc.
- **Typography**: Inter font family with responsive scale
- **Spacing**: 8px base unit with consistent spacing scale
- **Breakpoints**: Mobile-first responsive design
- **Components**: Consistent button styles, form controls, cards

## 🚀 Performance Optimization

### Implemented Optimizations

1. **Lazy Loading**: All feature modules are lazy-loaded
2. **Code Splitting**: Automatic code splitting by route
3. **Tree Shaking**: Unused code elimination
4. **Image Optimization**: Responsive images with lazy loading
5. **Caching**: HTTP response caching and browser caching
6. **Bundle Analysis**: Webpack bundle analyzer integration
7. **Performance Monitoring**: Core Web Vitals tracking

## 📱 Progressive Web App (PWA)

### PWA Features

- **Service Worker**: Automatic caching and offline support
- **Web App Manifest**: Installable app experience
- **Push Notifications**: Real-time notifications (configurable)
- **Background Sync**: Offline data synchronization
- **Install Prompt**: Custom app installation prompt

## 🧪 Testing Strategy

### Test Coverage

- **Unit Tests**: Services, components, pipes, directives
- **Integration Tests**: Component interactions
- **E2E Tests**: Critical user journeys
- **Performance Tests**: Core Web Vitals monitoring

### Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run e2e
```

## 🚀 Deployment

### Production Build

```bash
# Build for production
ng build --configuration production

# The build artifacts will be stored in the `dist/` directory
```

### Deployment Options

#### Static Hosting (Netlify, Vercel, GitHub Pages)

```bash
# Build the app
ng build --configuration production

# Deploy the dist/ folder to your hosting provider
```

#### Docker Deployment

```dockerfile
FROM node:18-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN ng build --configuration production

FROM nginx:alpine
COPY --from=build /app/dist/travel-booking-app /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 🔒 Security

### Security Features

- **JWT Token Management**: Secure token storage and refresh
- **Route Guards**: Authentication and authorization guards
- **HTTP Interceptors**: Automatic token attachment and error handling
- **CSRF Protection**: Cross-site request forgery protection
- **Content Security Policy**: CSP headers for XSS protection

## 📊 Monitoring and Analytics

### Performance Monitoring

- **Core Web Vitals**: LCP, FID, CLS tracking
- **Bundle Size**: Webpack bundle analyzer
- **Runtime Performance**: Performance API integration
- **Error Tracking**: Global error handler

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes**
4. **Add tests** for new functionality
5. **Run tests**: `npm test`
6. **Commit changes**: `git commit -m 'Add amazing feature'`
7. **Push to branch**: `git push origin feature/amazing-feature`
8. **Open a Pull Request**

### Code Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: Angular ESLint rules
- **Prettier**: Code formatting
- **Conventional Commits**: Commit message format
- **Angular Style Guide**: Official Angular style guide

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Open an issue on GitHub
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- **Angular Team**: For the amazing framework
- **Community**: For the open-source libraries and tools
- **Contributors**: Everyone who has contributed to this project

---

**Happy Coding! 🚀**
