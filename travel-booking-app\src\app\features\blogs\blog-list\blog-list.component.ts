import { Component, OnInit, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { BlogService } from '../../../core/services';
import { Blog, BlogCategory, BlogFilterDto, PaginationParameters } from '../../../core/models';

@Component({
  selector: 'app-blog-list',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <div class="blog-list-container">
      <!-- Header Section -->
      <section class="page-header">
        <div class="container">
          <h1>Travel Stories & Guides</h1>
          <p>Discover inspiring travel stories, expert tips, and destination guides from fellow travelers</p>
        </div>
      </section>

      <!-- Filters Section -->
      <section class="filters-section">
        <div class="container">
          <div class="filters-row">
            <!-- Search -->
            <div class="search-box">
              <input 
                type="text" 
                placeholder="Search articles..."
                [(ngModel)]="searchTerm"
                (input)="onSearchChange()"
                class="search-input"
              />
            </div>

            <!-- Category Filter -->
            <div class="category-filter">
              <select [(ngModel)]="selectedCategory" (change)="onFilterChange()">
                <option value="">All Categories</option>
                @for (category of categories(); track category.id) {
                  <option [value]="category.id">{{ category.name }}</option>
                }
              </select>
            </div>

            <!-- Sort -->
            <div class="sort-filter">
              <select [(ngModel)]="sortBy" (change)="onFilterChange()">
                <option value="publishedAt">Latest</option>
                <option value="title">Title</option>
                <option value="viewCount">Most Popular</option>
              </select>
            </div>
          </div>

          <div class="results-info">
            <span>{{ totalResults() }} articles found</span>
          </div>
        </div>
      </section>

      <!-- Featured Blogs -->
      @if (featuredBlogs().length > 0 && currentPage() === 1) {
        <section class="featured-section">
          <div class="container">
            <h2>Featured Articles</h2>
            <div class="featured-grid">
              @for (blog of featuredBlogs(); track blog.id) {
                <article class="featured-blog" [routerLink]="['/blogs', blog.slug]">
                  <div class="blog-image">
                    <img [src]="blog.featuredImageUrl" [alt]="blog.title" />
                  </div>
                  <div class="blog-content">
                    <div class="blog-meta">
                      <span class="category">{{ blog.category.name }}</span>
                      <span class="reading-time">{{ blog.readingTime }} min read</span>
                    </div>
                    <h3>{{ blog.title }}</h3>
                    <p>{{ blog.excerpt }}</p>
                    <div class="blog-author">
                      <span>By {{ blog.author.firstName }} {{ blog.author.lastName }}</span>
                      <span class="publish-date">{{ formatDate(blog.publishedAt) }}</span>
                    </div>
                  </div>
                </article>
              }
            </div>
          </div>
        </section>
      }

      <!-- Blog List -->
      <section class="blog-list-section">
        <div class="container">
          @if (isLoading()) {
            <div class="loading-grid">
              @for (item of [1,2,3,4,5,6]; track item) {
                <div class="blog-card-skeleton"></div>
              }
            </div>
          } @else if (blogs().length === 0) {
            <div class="no-results">
              <h3>No articles found</h3>
              <p>Try adjusting your search terms or filters</p>
            </div>
          } @else {
            <div class="blogs-grid">
              @for (blog of blogs(); track blog.id) {
                <article class="blog-card" [routerLink]="['/blogs', blog.slug]">
                  <div class="blog-image">
                    <img [src]="blog.featuredImageUrl" [alt]="blog.title" />
                  </div>
                  <div class="blog-content">
                    <div class="blog-meta">
                      <span class="category">{{ blog.category.name }}</span>
                      <span class="reading-time">{{ blog.readingTime }} min read</span>
                    </div>
                    <h3>{{ blog.title }}</h3>
                    <p>{{ blog.excerpt }}</p>
                    <div class="blog-footer">
                      <div class="blog-author">
                        <span>By {{ blog.author.firstName }} {{ blog.author.lastName }}</span>
                      </div>
                      <div class="blog-stats">
                        <span class="views">{{ blog.viewCount }} views</span>
                        <span class="date">{{ formatDate(blog.publishedAt) }}</span>
                      </div>
                    </div>
                  </div>
                </article>
              }
            </div>

            <!-- Pagination -->
            @if (totalPages() > 1) {
              <div class="pagination">
                <button 
                  class="btn btn-outline"
                  [disabled]="currentPage() === 1"
                  (click)="goToPage(currentPage() - 1)"
                >
                  Previous
                </button>
                
                @for (page of getPageNumbers(); track page) {
                  <button 
                    class="btn"
                    [class.btn-primary]="page === currentPage()"
                    [class.btn-outline]="page !== currentPage()"
                    (click)="goToPage(page)"
                  >
                    {{ page }}
                  </button>
                }
                
                <button 
                  class="btn btn-outline"
                  [disabled]="currentPage() === totalPages()"
                  (click)="goToPage(currentPage() + 1)"
                >
                  Next
                </button>
              </div>
            }
          }
        </div>
      </section>
    </div>
  `,
  styleUrls: ['./blog-list.component.scss']
})
export class BlogListComponent implements OnInit {
  private readonly blogService = inject(BlogService);

  // Filter signals
  searchTerm = signal<string>('');
  selectedCategory = signal<string>('');
  sortBy = signal<string>('publishedAt');

  // Data signals
  blogs = signal<Blog[]>([]);
  featuredBlogs = signal<Blog[]>([]);
  categories = signal<BlogCategory[]>([]);
  isLoading = signal<boolean>(false);
  currentPage = signal<number>(1);
  totalResults = signal<number>(0);
  totalPages = computed(() => Math.ceil(this.totalResults() / 12));

  private searchTimeout: any;

  ngOnInit(): void {
    this.loadCategories();
    this.loadFeaturedBlogs();
    this.loadBlogs();
  }

  private async loadCategories(): Promise<void> {
    try {
      const categories = await this.blogService.getBlogCategories().toPromise();
      this.categories.set(categories || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  }

  private async loadFeaturedBlogs(): Promise<void> {
    try {
      const featured = await this.blogService.getFeaturedBlogs().toPromise();
      this.featuredBlogs.set(featured?.slice(0, 3) || []);
    } catch (error) {
      console.error('Error loading featured blogs:', error);
    }
  }

  private async loadBlogs(): Promise<void> {
    this.isLoading.set(true);
    
    try {
      const filters = this.buildFilters();
      const pagination: PaginationParameters = {
        page: this.currentPage(),
        pageSize: 12,
        sortBy: this.sortBy(),
        sortOrder: 'desc'
      };

      const result = await this.blogService.getBlogs(pagination, filters).toPromise();
      
      if (result) {
        this.blogs.set(result.items);
        this.totalResults.set(result.totalCount);
      }
    } catch (error) {
      console.error('Error loading blogs:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  private buildFilters(): BlogFilterDto {
    const filters: BlogFilterDto = {
      isPublished: true
    };

    if (this.searchTerm()) filters.search = this.searchTerm();
    if (this.selectedCategory()) filters.categoryId = Number(this.selectedCategory());

    return filters;
  }

  onSearchChange(): void {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.currentPage.set(1);
      this.loadBlogs();
    }, 500);
  }

  onFilterChange(): void {
    this.currentPage.set(1);
    this.loadBlogs();
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
      this.loadBlogs();
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  getPageNumbers(): number[] {
    const total = this.totalPages();
    const current = this.currentPage();
    const pages: number[] = [];

    if (total <= 7) {
      for (let i = 1; i <= total; i++) {
        pages.push(i);
      }
    } else {
      if (current <= 4) {
        for (let i = 1; i <= 5; i++) pages.push(i);
        pages.push(-1);
        pages.push(total);
      } else if (current >= total - 3) {
        pages.push(1);
        pages.push(-1);
        for (let i = total - 4; i <= total; i++) pages.push(i);
      } else {
        pages.push(1);
        pages.push(-1);
        for (let i = current - 1; i <= current + 1; i++) pages.push(i);
        pages.push(-1);
        pages.push(total);
      }
    }

    return pages;
  }

  formatDate(date: Date | undefined): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
}
