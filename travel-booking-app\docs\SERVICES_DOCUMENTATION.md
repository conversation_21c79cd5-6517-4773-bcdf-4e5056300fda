# Services Documentation - Travel Booking App

## Service Architecture Overview

The Travel Booking App uses a layered service architecture with clear separation of concerns:

```
Presentation Layer (Components)
        ↓
Business Logic Layer (Feature Services)
        ↓
Data Access Layer (API Service)
        ↓
HTTP Client (Angular HttpClient)
        ↓
Backend API (TravelTourism.API)
```

## Core Services

### ApiService
**File:** `src/app/core/services/api.service.ts`
**Purpose:** Base HTTP client service with common functionality
**Type:** Singleton Service

**Key Features:**
- Centralized HTTP operations
- Error handling
- Request/response transformation
- Pagination support
- File upload capabilities

**Methods:**
```typescript
// Basic HTTP operations
get<T>(endpoint: string, params?: any): Observable<T>
post<T>(endpoint: string, data: any): Observable<T>
put<T>(endpoint: string, data: any): Observable<T>
delete<T>(endpoint: string): Observable<T>

// Paginated requests
getPaged<T>(endpoint: string, pagination: PaginationParameters, filters?: any): Observable<PagedResult<T>>

// File operations
uploadFile<T>(endpoint: string, file: File): Observable<T>
downloadFile(endpoint: string, filename: string): Observable<Blob>

// Raw response (for exports)
getRaw(endpoint: string, params?: any): Observable<Blob>
```

**Configuration:**
```typescript
private readonly baseUrl = 'https://localhost:7115/api/v1';
private readonly defaultHeaders = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
};
```

**Error Handling:**
```typescript
private handleError(error: HttpErrorResponse): Observable<never> {
  let errorMessage = 'An unknown error occurred';
  
  if (error.error instanceof ErrorEvent) {
    // Client-side error
    errorMessage = error.error.message;
  } else {
    // Server-side error
    errorMessage = error.error?.message || `Error Code: ${error.status}`;
  }
  
  return throwError(() => new Error(errorMessage));
}
```

**Usage Example:**
```typescript
// In a feature service
constructor(private apiService: ApiService) {}

getTrips(pagination: PaginationParameters, filters?: TripFilterDto): Observable<PagedResult<Trip>> {
  return this.apiService.getPaged<Trip>('trips', pagination, filters);
}
```

---

### AuthService
**File:** `src/app/core/services/auth.service.ts`
**Purpose:** Authentication and authorization management
**Type:** Singleton Service

**Key Features:**
- JWT token management
- User authentication state
- Token refresh handling
- Role-based access control
- Persistent login state

**Signals:**
```typescript
private readonly _currentUser = signal<User | null>(null);
private readonly _isAuthenticated = signal<boolean>(false);

// Read-only accessors
readonly currentUser = this._currentUser.asReadonly();
readonly isAuthenticated = this._isAuthenticated.asReadonly();

// Computed properties
readonly isAdmin = computed(() => this.currentUser()?.role === 'Admin');
readonly userRole = computed(() => this.currentUser()?.role || 'Guest');
```

**Methods:**
```typescript
// Authentication operations
login(credentials: LoginDto): Observable<AuthResultDto>
register(userData: RegisterDto): Observable<AuthResultDto>
logout(): void
refreshToken(): Observable<AuthResultDto>

// Password management
forgotPassword(email: ForgotPasswordDto): Observable<MessageResponse>
resetPassword(resetData: ResetPasswordDto): Observable<MessageResponse>
changePassword(passwordData: ChangePasswordDto): Observable<MessageResponse>

// Token management
getToken(): string | null
isTokenExpired(token?: string): boolean
setTokens(token: string, refreshToken: string): void
clearTokens(): void

// Role checking
hasRole(role: string): boolean
hasAnyRole(roles: string[]): boolean
```

**Token Management:**
```typescript
private readonly TOKEN_KEY = 'auth_token';
private readonly REFRESH_TOKEN_KEY = 'refresh_token';
private readonly USER_KEY = 'current_user';

private setTokens(token: string, refreshToken: string): void {
  localStorage.setItem(this.TOKEN_KEY, token);
  localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
}

private isTokenExpired(token: string): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000 < Date.now();
  } catch {
    return true;
  }
}
```

**Auto-Refresh Logic:**
```typescript
private setupTokenRefresh(): void {
  const token = this.getToken();
  if (token && !this.isTokenExpired(token)) {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expirationTime = payload.exp * 1000;
    const refreshTime = expirationTime - (5 * 60 * 1000); // 5 minutes before expiry
    
    setTimeout(() => {
      this.refreshToken().subscribe();
    }, refreshTime - Date.now());
  }
}
```

---

### TripService
**File:** `src/app/core/services/trip.service.ts`
**Purpose:** Trip data management and operations
**Type:** Singleton Service

**Key Features:**
- Trip CRUD operations
- Search and filtering
- Category management
- Image handling
- Admin operations

**Methods:**
```typescript
// Public trip operations
getTrips(pagination: PaginationParameters, filters?: TripFilterDto): Observable<PagedResult<Trip>>
getTripById(id: number): Observable<TripDetail>
getFeaturedTrips(limit?: number): Observable<Trip[]>
searchTrips(searchTerm: string, filters?: TripFilterDto): Observable<PagedResult<Trip>>
getTripCategories(): Observable<TripCategory[]>

// Admin operations (requires admin role)
createTrip(tripData: CreateTripDto): Observable<Trip>
updateTrip(id: number, tripData: UpdateTripDto): Observable<Trip>
deleteTrip(id: number): Observable<void>
toggleTripStatus(id: number): Observable<Trip>

// Image management
uploadTripImage(tripId: number, file: File): Observable<{imageUrl: string}>
deleteTripImage(tripId: number, imageId: number): Observable<void>
updateImageOrder(tripId: number, imageOrders: {id: number, order: number}[]): Observable<void>

// Statistics and reporting
getTripStats(tripId: number): Observable<TripStats>
exportTrips(filters?: TripFilterDto): Observable<Blob>
```

**Caching Strategy:**
```typescript
private readonly cache = new Map<string, {data: any, timestamp: number}>();
private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

private getCachedData<T>(key: string): T | null {
  const cached = this.cache.get(key);
  if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
    return cached.data;
  }
  return null;
}

private setCachedData(key: string, data: any): void {
  this.cache.set(key, { data, timestamp: Date.now() });
}
```

**Filter Building:**
```typescript
private buildFilterParams(filters: TripFilterDto): HttpParams {
  let params = new HttpParams();
  
  if (filters.search) params = params.set('search', filters.search);
  if (filters.categoryId) params = params.set('categoryId', filters.categoryId.toString());
  if (filters.minPrice) params = params.set('minPrice', filters.minPrice.toString());
  if (filters.maxPrice) params = params.set('maxPrice', filters.maxPrice.toString());
  if (filters.difficulty) params = params.set('difficulty', filters.difficulty.toString());
  if (filters.isFeatured !== undefined) params = params.set('isFeatured', filters.isFeatured.toString());
  
  return params;
}
```

---

### BookingService
**File:** `src/app/core/services/booking.service.ts`
**Purpose:** Booking management and operations
**Type:** Singleton Service

**Key Features:**
- Booking CRUD operations
- Payment integration
- Status management
- User booking history
- Admin booking oversight

**Methods:**
```typescript
// User booking operations
getUserBookings(pagination: PaginationParameters, filters?: BookingFilterDto): Observable<PagedResult<Booking>>
getBookingById(id: number): Observable<Booking>
createBooking(bookingData: CreateBookingDto): Observable<Booking>
updateBooking(id: number, updateData: UpdateBookingDto): Observable<Booking>
cancelBooking(id: number, reason?: CancelBookingDto): Observable<Booking>

// Payment operations
processPayment(bookingId: number, paymentData: PaymentDto): Observable<PaymentResult>
getPaymentStatus(bookingId: number): Observable<PaymentStatus>
requestRefund(bookingId: number, refundData: RefundRequestDto): Observable<RefundResult>

// Document generation
generateBookingVoucher(bookingId: number): Observable<Blob>
generateInvoice(bookingId: number): Observable<Blob>
generateItinerary(bookingId: number): Observable<Blob>

// Admin operations
getAllBookings(pagination: PaginationParameters, filters?: AdminBookingFilterDto): Observable<PagedResult<Booking>>
updateBookingStatus(id: number, status: BookingStatus): Observable<Booking>
getBookingStatistics(dateRange?: DateRange): Observable<BookingStats>
exportBookings(filters?: BookingFilterDto): Observable<Blob>
```

**Booking Status Management:**
```typescript
enum BookingStatus {
  Pending = 1,
  Confirmed = 2,
  Cancelled = 3,
  Completed = 4
}

getStatusText(status: BookingStatus): string {
  const statusMap = {
    [BookingStatus.Pending]: 'Pending',
    [BookingStatus.Confirmed]: 'Confirmed',
    [BookingStatus.Cancelled]: 'Cancelled',
    [BookingStatus.Completed]: 'Completed'
  };
  return statusMap[status] || 'Unknown';
}

getStatusClass(status: BookingStatus): string {
  const classMap = {
    [BookingStatus.Pending]: 'status-pending',
    [BookingStatus.Confirmed]: 'status-confirmed',
    [BookingStatus.Cancelled]: 'status-cancelled',
    [BookingStatus.Completed]: 'status-completed'
  };
  return classMap[status] || 'status-unknown';
}
```

**Payment Integration:**
```typescript
private processStripePayment(paymentData: PaymentDto): Observable<PaymentResult> {
  return from(this.stripe.confirmCardPayment(paymentData.clientSecret, {
    payment_method: {
      card: paymentData.cardElement,
      billing_details: paymentData.billingDetails
    }
  })).pipe(
    map(result => ({
      success: result.error ? false : true,
      paymentIntentId: result.paymentIntent?.id,
      error: result.error?.message
    }))
  );
}
```

---

### StateService
**File:** `src/app/core/services/state.service.ts`
**Purpose:** Global application state management
**Type:** Singleton Service

**Key Features:**
- Centralized state management
- Signal-based reactivity
- Persistent state
- Cross-component communication
- Loading state management

**State Signals:**
```typescript
// User state
private readonly _currentUser = signal<User | null>(null);
private readonly _isAuthenticated = signal<boolean>(false);

// UI state
private readonly _isLoading = signal<boolean>(false);
private readonly _notifications = signal<Notification[]>([]);
private readonly _sidebarOpen = signal<boolean>(false);

// Data state
private readonly _selectedTrip = signal<Trip | null>(null);
private readonly _searchResults = signal<Trip[]>([]);
private readonly _filters = signal<FilterState>({});

// Computed state
readonly userRole = computed(() => this._currentUser()?.role || 'Guest');
readonly isAdmin = computed(() => this.userRole() === 'Admin');
readonly hasNotifications = computed(() => this._notifications().length > 0);
readonly isDataLoaded = computed(() => !this._isLoading() && this._searchResults().length > 0);
```

**State Management Methods:**
```typescript
// User state
setCurrentUser(user: User | null): void {
  this._currentUser.set(user);
  this._isAuthenticated.set(!!user);
  this.persistUserState(user);
}

// UI state
setLoading(loading: boolean): void {
  this._isLoading.set(loading);
}

addNotification(notification: Notification): void {
  this._notifications.update(notifications => [...notifications, notification]);
  this.autoRemoveNotification(notification.id);
}

removeNotification(id: string): void {
  this._notifications.update(notifications => 
    notifications.filter(n => n.id !== id)
  );
}

// Data state
setSelectedTrip(trip: Trip | null): void {
  this._selectedTrip.set(trip);
}

updateFilters(filters: Partial<FilterState>): void {
  this._filters.update(current => ({ ...current, ...filters }));
}

clearFilters(): void {
  this._filters.set({});
}
```

**Persistence:**
```typescript
private readonly STORAGE_KEYS = {
  USER: 'current_user',
  FILTERS: 'search_filters',
  PREFERENCES: 'user_preferences'
};

private persistUserState(user: User | null): void {
  if (user) {
    localStorage.setItem(this.STORAGE_KEYS.USER, JSON.stringify(user));
  } else {
    localStorage.removeItem(this.STORAGE_KEYS.USER);
  }
}

private loadPersistedState(): void {
  const userData = localStorage.getItem(this.STORAGE_KEYS.USER);
  if (userData) {
    try {
      const user = JSON.parse(userData);
      this._currentUser.set(user);
      this._isAuthenticated.set(true);
    } catch (error) {
      console.error('Error loading persisted user state:', error);
    }
  }
}
```

---

### NotificationService
**File:** `src/app/core/services/notification.service.ts`
**Purpose:** Toast notification management
**Type:** Singleton Service

**Key Features:**
- Multiple notification types
- Auto-dismiss functionality
- Queue management
- Custom actions
- Animation support

**Methods:**
```typescript
showSuccess(message: string, title?: string, action?: NotificationAction): void
showError(message: string, title?: string, action?: NotificationAction): void
showWarning(message: string, title?: string, action?: NotificationAction): void
showInfo(message: string, title?: string, action?: NotificationAction): void

// Custom notification
show(notification: Notification): void
dismiss(id: string): void
dismissAll(): void

// Bulk operations
showMultiple(notifications: Notification[]): void
```

**Notification Types:**
```typescript
interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  action?: NotificationAction;
  persistent?: boolean;
  timestamp: Date;
}

interface NotificationAction {
  label: string;
  handler: () => void;
}
```

**Auto-Dismiss Logic:**
```typescript
private autoRemoveNotification(id: string, duration: number = 5000): void {
  setTimeout(() => {
    this.dismiss(id);
  }, duration);
}

private generateId(): string {
  return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
```

---

## Specialized Services

### AnimationService
**File:** `src/app/core/services/animation.service.ts`
**Purpose:** Animation utilities and effects management

**Features:**
- Scroll-based animations
- Parallax effects
- Tilt interactions
- Stagger animations
- Performance optimization

### PerformanceService
**File:** `src/app/core/services/performance.service.ts`
**Purpose:** Performance monitoring and optimization

**Features:**
- Core Web Vitals tracking
- Bundle size monitoring
- Memory usage tracking
- Network performance
- Performance budgets

### PWAService
**File:** `src/app/core/services/pwa.service.ts`
**Purpose:** Progressive Web App functionality

**Features:**
- Service worker management
- Install prompt handling
- Update notifications
- Offline support
- Background sync

### SEOService
**File:** `src/app/core/services/seo.service.ts`
**Purpose:** Search engine optimization

**Features:**
- Dynamic meta tags
- Structured data
- Open Graph tags
- Twitter Cards
- Canonical URLs

## Service Testing Strategy

### Unit Testing
```typescript
describe('AuthService', () => {
  let service: AuthService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [AuthService]
    });
    service = TestBed.inject(AuthService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  it('should login successfully', () => {
    const mockResponse = { user: mockUser, token: 'mock-token' };
    
    service.login({ email: '<EMAIL>', password: 'password' })
      .subscribe(result => {
        expect(result).toEqual(mockResponse);
      });

    const req = httpMock.expectOne('api/v1/auth/login');
    expect(req.request.method).toBe('POST');
    req.flush(mockResponse);
  });
});
```

### Integration Testing
- Service interaction testing
- HTTP interceptor testing
- Error handling testing
- State management testing
