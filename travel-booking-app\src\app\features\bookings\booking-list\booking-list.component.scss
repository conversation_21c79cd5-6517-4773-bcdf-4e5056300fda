.booking-list-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.page-header {
  text-align: center;
  padding: 40px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 40px;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
  }

  p {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

// Filters Section
.filters-section {
  background: white;
  padding: 25px 30px;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  .filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 15px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .filter-group {
    label {
      display: block;
      font-weight: 600;
      margin-bottom: 8px;
      color: #2d3748;
      font-size: 0.9rem;
    }

    select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      font-size: 0.9rem;
      background: white;
      transition: border-color 0.2s;

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
  }

  .results-info {
    color: #718096;
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    padding-top: 15px;
    border-top: 1px solid #e2e8f0;
  }
}

// Bookings Section
.bookings-section {
  .bookings-grid {
    display: grid;
    gap: 25px;
    margin-bottom: 40px;
  }

  .booking-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: grid;
    grid-template-columns: 250px 1fr;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .booking-image {
      position: relative;
      height: 200px;
      overflow: hidden;

      @media (max-width: 768px) {
        height: 180px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .booking-status {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        color: white;

        &.status-1 { background: #f6ad55; } // Pending
        &.status-2 { background: #38a169; } // Confirmed
        &.status-3 { background: #e53e3e; } // Cancelled
        &.status-4 { background: #4299e1; } // Completed
      }
    }

    .booking-content {
      padding: 25px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .booking-header {
        margin-bottom: 20px;

        h3 {
          font-size: 1.3rem;
          font-weight: 600;
          color: #2d3748;
          margin-bottom: 5px;
        }

        .booking-id {
          font-size: 0.9rem;
          color: #718096;
          font-weight: 500;
        }
      }

      .booking-details {
        margin-bottom: 20px;

        .detail-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 0.9rem;

          .label {
            color: #718096;
            font-weight: 500;
          }

          .value {
            color: #2d3748;
            font-weight: 600;
          }
        }
      }

      .booking-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        @media (max-width: 480px) {
          flex-direction: column;
          gap: 15px;
          align-items: flex-start;
        }

        .booking-price {
          .amount {
            font-size: 1.5rem;
            font-weight: 700;
            color: #38a169;
          }

          .currency {
            font-size: 0.9rem;
            color: #718096;
            margin-left: 5px;
          }
        }

        .booking-actions {
          display: flex;
          gap: 10px;

          .btn-sm {
            padding: 8px 16px;
            font-size: 0.9rem;
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 80px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 20px;
    }

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 10px;
    }

    p {
      color: #718096;
      margin-bottom: 30px;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

// Loading States
.loading-grid {
  display: grid;
  gap: 25px;

  .booking-card-skeleton {
    background: white;
    border-radius: 15px;
    height: 200px;
    animation: pulse 1.5s ease-in-out infinite alternate;
  }
}

// Pagination
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 40px;

  .btn {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// Button Styles
.btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  text-align: center;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover:not(:disabled) {
      background: #5a67d8;
    }
  }

  &.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;

    &:hover:not(:disabled) {
      background: #667eea;
      color: white;
    }
  }

  &.btn-danger {
    background: #e53e3e;
    color: white;

    &:hover:not(:disabled) {
      background: #c53030;
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  100% { opacity: 0.4; }
}
