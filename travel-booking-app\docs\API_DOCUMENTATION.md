# API Documentation - Travel Booking App

## Base URL
```
https://localhost:7115/api/v1
```

## Authentication
All authenticated endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Response Format
All API responses follow a consistent format:

### Success Response
```json
{
  "data": <response_data>,
  "success": true,
  "message": "Success message",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Response
```json
{
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "details": {}
  },
  "success": false,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Paginated Response
```json
{
  "items": [<array_of_items>],
  "totalCount": 100,
  "pageNumber": 1,
  "pageSize": 10,
  "totalPages": 10,
  "hasPreviousPage": false,
  "hasNextPage": true
}
```

## Authentication Endpoints

### POST /auth/login
Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "user": {
    "id": 1,
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "role": "User",
    "isEmailVerified": true,
    "profilePictureUrl": null
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh_token_here",
  "expiresAt": "2024-01-15T11:30:00Z"
}
```

**Angular Service Usage:**
```typescript
this.authService.login({ email: '<EMAIL>', password: 'password123' })
  .subscribe({
    next: (result) => {
      // Handle successful login
      this.router.navigate(['/dashboard']);
    },
    error: (error) => {
      // Handle login error
      this.notificationService.showError('Invalid credentials');
    }
  });
```

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "phoneNumber": "+**********",
  "dateOfBirth": "1990-01-01T00:00:00Z"
}
```

**Response:** Same as login response

**Angular Service Usage:**
```typescript
const registerData = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  password: 'password123',
  confirmPassword: 'password123',
  phoneNumber: '+**********',
  dateOfBirth: new Date('1990-01-01')
};

this.authService.register(registerData)
  .subscribe({
    next: (result) => {
      this.notificationService.showSuccess('Registration successful!');
      this.router.navigate(['/dashboard']);
    },
    error: (error) => {
      this.notificationService.showError('Registration failed');
    }
  });
```

### POST /auth/refresh-token
Refresh JWT token using refresh token.

**Request Body:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

**Response:** Same as login response

### POST /auth/forgot-password
Request password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "Password reset email sent successfully"
}
```

### POST /auth/reset-password
Reset password using reset token.

**Request Body:**
```json
{
  "token": "reset_token_from_email",
  "newPassword": "newpassword123",
  "confirmPassword": "newpassword123"
}
```

**Response:**
```json
{
  "message": "Password reset successfully"
}
```

## Trip Endpoints

### GET /trips
Get paginated list of trips with filtering and sorting.

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `pageSize` (int): Items per page (default: 10)
- `sortBy` (string): Sort field (name, price, duration, createdAt)
- `sortOrder` (string): Sort direction (asc, desc)
- `search` (string): Search term for trip name/description
- `categoryId` (int): Filter by category ID
- `destinationCityId` (int): Filter by destination city
- `minPrice` (decimal): Minimum price filter
- `maxPrice` (decimal): Maximum price filter
- `minDuration` (int): Minimum duration in days
- `maxDuration` (int): Maximum duration in days
- `difficulty` (int): Difficulty level (1=Easy, 2=Moderate, 3=Hard)
- `isFeatured` (bool): Filter featured trips
- `availableFrom` (date): Available from date
- `availableTo` (date): Available to date

**Example Request:**
```
GET /trips?page=1&pageSize=12&categoryId=1&minPrice=500&maxPrice=2000&sortBy=price&sortOrder=asc
```

**Response:**
```json
{
  "items": [
    {
      "id": 1,
      "name": "Bali Adventure",
      "shortDescription": "Amazing adventure in Bali",
      "price": 1200.00,
      "discountPrice": 1000.00,
      "duration": 7,
      "maxCapacity": 20,
      "minAge": 18,
      "maxAge": 65,
      "difficulty": 2,
      "mainImageUrl": "https://example.com/bali-main.jpg",
      "isFeatured": true,
      "availableFrom": "2024-01-01T00:00:00Z",
      "availableTo": "2024-12-31T23:59:59Z",
      "category": {
        "id": 1,
        "name": "Adventure",
        "description": "Adventure trips",
        "iconUrl": "adventure-icon.png"
      },
      "destinationCity": {
        "id": 1,
        "name": "Ubud",
        "country": {
          "id": 1,
          "name": "Indonesia",
          "code": "ID"
        }
      },
      "departureCity": {
        "id": 2,
        "name": "Jakarta",
        "country": {
          "id": 1,
          "name": "Indonesia",
          "code": "ID"
        }
      },
      "images": [
        {
          "id": 1,
          "imageUrl": "https://example.com/bali-1.jpg",
          "caption": "Beautiful Bali landscape",
          "isMain": true,
          "displayOrder": 1
        }
      ],
      "effectivePrice": 1000.00,
      "hasDiscount": true,
      "discountPercentage": 16.67
    }
  ],
  "totalCount": 25,
  "pageNumber": 1,
  "pageSize": 12,
  "totalPages": 3,
  "hasPreviousPage": false,
  "hasNextPage": true
}
```

**Angular Service Usage:**
```typescript
const pagination = { page: 1, pageSize: 12, sortBy: 'price', sortOrder: 'asc' };
const filters = { categoryId: 1, minPrice: 500, maxPrice: 2000 };

this.tripService.getTrips(pagination, filters)
  .subscribe({
    next: (result) => {
      this.trips.set(result.items);
      this.totalCount.set(result.totalCount);
      this.totalPages.set(result.totalPages);
    },
    error: (error) => {
      this.notificationService.showError('Failed to load trips');
    }
  });
```

### GET /trips/{id}
Get detailed information about a specific trip.

**Path Parameters:**
- `id` (int): Trip ID

**Response:**
```json
{
  "id": 1,
  "name": "Bali Adventure",
  "shortDescription": "Amazing adventure in Bali",
  "description": "Full detailed description of the trip...",
  "price": 1200.00,
  "discountPrice": 1000.00,
  "duration": 7,
  "maxCapacity": 20,
  "minAge": 18,
  "maxAge": 65,
  "difficulty": 2,
  "mainImageUrl": "https://example.com/bali-main.jpg",
  "isFeatured": true,
  "availableFrom": "2024-01-01T00:00:00Z",
  "availableTo": "2024-12-31T23:59:59Z",
  "includesAccommodation": true,
  "includesTransport": true,
  "includesMeals": false,
  "includesGuide": true,
  "category": { /* category object */ },
  "destinationCity": { /* city object */ },
  "departureCity": { /* city object */ },
  "images": [ /* array of images */ ],
  "itineraries": [
    {
      "id": 1,
      "day": 1,
      "title": "Arrival in Bali",
      "description": "Arrive at Ngurah Rai International Airport",
      "activities": "Airport transfer, hotel check-in, welcome dinner",
      "meals": "Dinner",
      "accommodation": "4-star hotel in Ubud"
    }
  ],
  "effectivePrice": 1000.00,
  "hasDiscount": true,
  "discountPercentage": 16.67,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-10T00:00:00Z"
}
```

**Angular Service Usage:**
```typescript
this.tripService.getTripById(1)
  .subscribe({
    next: (trip) => {
      this.selectedTrip.set(trip);
      this.seoService.updateTripPage(trip);
    },
    error: (error) => {
      this.notificationService.showError('Trip not found');
      this.router.navigate(['/trips']);
    }
  });
```

### GET /trips/featured
Get featured trips for homepage display.

**Query Parameters:**
- `limit` (int): Maximum number of trips to return (default: 6)

**Response:**
```json
[
  {
    "id": 1,
    "name": "Bali Adventure",
    "shortDescription": "Amazing adventure in Bali",
    "price": 1200.00,
    "discountPrice": 1000.00,
    "duration": 7,
    "difficulty": 2,
    "mainImageUrl": "https://example.com/bali-main.jpg",
    "category": { /* category object */ },
    "destinationCity": { /* city object */ },
    "effectivePrice": 1000.00,
    "hasDiscount": true,
    "discountPercentage": 16.67
  }
]
```

**Angular Service Usage:**
```typescript
this.tripService.getFeaturedTrips()
  .subscribe({
    next: (trips) => {
      this.featuredTrips.set(trips);
    },
    error: (error) => {
      console.error('Error loading featured trips:', error);
    }
  });
```

### GET /trips/categories
Get all trip categories.

**Response:**
```json
[
  {
    "id": 1,
    "name": "Adventure",
    "description": "Thrilling adventure trips",
    "iconUrl": "adventure-icon.png"
  },
  {
    "id": 2,
    "name": "Cultural",
    "description": "Cultural exploration trips",
    "iconUrl": "cultural-icon.png"
  }
]
```

**Angular Service Usage:**
```typescript
this.tripService.getTripCategories()
  .subscribe({
    next: (categories) => {
      this.categories.set(categories);
    },
    error: (error) => {
      console.error('Error loading categories:', error);
    }
  });
```

## Booking Endpoints

### GET /bookings
Get user's bookings with pagination and filtering.

**Authentication:** Required
**Query Parameters:**
- `page` (int): Page number (default: 1)
- `pageSize` (int): Items per page (default: 10)
- `sortBy` (string): Sort field (createdAt, travelDate, totalAmount)
- `sortOrder` (string): Sort direction (asc, desc)
- `status` (int): Filter by booking status (1=Pending, 2=Confirmed, 3=Cancelled, 4=Completed)
- `tripId` (int): Filter by trip ID
- `dateFrom` (date): Filter bookings from date
- `dateTo` (date): Filter bookings to date

**Response:**
```json
{
  "items": [
    {
      "id": 1,
      "bookingReference": "TRV-2024-001",
      "status": 2,
      "statusText": "Confirmed",
      "travelDate": "2024-06-01T00:00:00Z",
      "numberOfAdults": 2,
      "numberOfChildren": 0,
      "numberOfInfants": 0,
      "totalAmount": 2000.00,
      "paidAmount": 2000.00,
      "paymentStatus": 2,
      "paymentStatusText": "Paid",
      "specialRequests": "Vegetarian meals",
      "emergencyContactName": "Jane Doe",
      "emergencyContactPhone": "+1234567891",
      "trip": {
        "id": 1,
        "name": "Bali Adventure",
        "mainImageUrl": "https://example.com/bali-main.jpg",
        "duration": 7,
        "destinationCity": {
          "name": "Ubud",
          "country": { "name": "Indonesia" }
        }
      },
      "passengers": [
        {
          "id": 1,
          "firstName": "John",
          "lastName": "Doe",
          "dateOfBirth": "1990-01-01T00:00:00Z",
          "passportNumber": "A12345678",
          "nationality": "US",
          "dietaryRequirements": "Vegetarian"
        }
      ],
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ],
  "totalCount": 5,
  "pageNumber": 1,
  "pageSize": 10,
  "totalPages": 1,
  "hasPreviousPage": false,
  "hasNextPage": false
}
```

**Angular Service Usage:**
```typescript
const pagination = { page: 1, pageSize: 10, sortBy: 'createdAt', sortOrder: 'desc' };
const filters = { status: 2 }; // Confirmed bookings only

this.bookingService.getUserBookings(pagination, filters)
  .subscribe({
    next: (result) => {
      this.bookings.set(result.items);
      this.totalCount.set(result.totalCount);
    },
    error: (error) => {
      this.notificationService.showError('Failed to load bookings');
    }
  });
```

### GET /bookings/{id}
Get detailed information about a specific booking.

**Authentication:** Required
**Path Parameters:**
- `id` (int): Booking ID

**Response:**
```json
{
  "id": 1,
  "bookingReference": "TRV-2024-001",
  "status": 2,
  "statusText": "Confirmed",
  "travelDate": "2024-06-01T00:00:00Z",
  "numberOfAdults": 2,
  "numberOfChildren": 0,
  "numberOfInfants": 0,
  "baseAmount": 1800.00,
  "discountAmount": 200.00,
  "taxAmount": 180.00,
  "totalAmount": 2000.00,
  "paidAmount": 2000.00,
  "paymentStatus": 2,
  "paymentStatusText": "Paid",
  "paymentMethod": "Credit Card",
  "paymentReference": "PAY-2024-001",
  "specialRequests": "Vegetarian meals for all passengers",
  "emergencyContactName": "Jane Doe",
  "emergencyContactPhone": "+1234567891",
  "cancellationReason": null,
  "cancellationDate": null,
  "trip": {
    "id": 1,
    "name": "Bali Adventure",
    "shortDescription": "Amazing adventure in Bali",
    "mainImageUrl": "https://example.com/bali-main.jpg",
    "duration": 7,
    "category": { "name": "Adventure" },
    "destinationCity": {
      "name": "Ubud",
      "country": { "name": "Indonesia" }
    },
    "departureCity": {
      "name": "Jakarta",
      "country": { "name": "Indonesia" }
    }
  },
  "passengers": [
    {
      "id": 1,
      "firstName": "John",
      "lastName": "Doe",
      "dateOfBirth": "1990-01-01T00:00:00Z",
      "passportNumber": "A12345678",
      "nationality": "US",
      "dietaryRequirements": "Vegetarian"
    },
    {
      "id": 2,
      "firstName": "Jane",
      "lastName": "Doe",
      "dateOfBirth": "1992-05-15T00:00:00Z",
      "passportNumber": "A87654321",
      "nationality": "US",
      "dietaryRequirements": "None"
    }
  ],
  "createdAt": "2024-01-15T10:30:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

**Angular Service Usage:**
```typescript
this.bookingService.getBookingById(1)
  .subscribe({
    next: (booking) => {
      this.selectedBooking.set(booking);
    },
    error: (error) => {
      this.notificationService.showError('Booking not found');
      this.router.navigate(['/bookings']);
    }
  });
```

### POST /bookings
Create a new booking.

**Authentication:** Required
**Request Body:**
```json
{
  "tripId": 1,
  "travelDate": "2024-06-01T00:00:00Z",
  "numberOfAdults": 2,
  "numberOfChildren": 0,
  "numberOfInfants": 0,
  "specialRequests": "Vegetarian meals for all passengers",
  "emergencyContactName": "Jane Doe",
  "emergencyContactPhone": "+1234567891",
  "passengers": [
    {
      "firstName": "John",
      "lastName": "Doe",
      "dateOfBirth": "1990-01-01T00:00:00Z",
      "passportNumber": "A12345678",
      "nationality": "US",
      "dietaryRequirements": "Vegetarian"
    },
    {
      "firstName": "Jane",
      "lastName": "Doe",
      "dateOfBirth": "1992-05-15T00:00:00Z",
      "passportNumber": "A87654321",
      "nationality": "US",
      "dietaryRequirements": "None"
    }
  ]
}
```

**Response:**
```json
{
  "id": 1,
  "bookingReference": "TRV-2024-001",
  "status": 1,
  "statusText": "Pending",
  "totalAmount": 2000.00,
  "paymentStatus": 1,
  "paymentStatusText": "Pending",
  "trip": { /* trip object */ },
  "passengers": [ /* passengers array */ ],
  "createdAt": "2024-01-15T10:30:00Z"
}
```

**Angular Service Usage:**
```typescript
const bookingData = {
  tripId: 1,
  travelDate: new Date('2024-06-01'),
  numberOfAdults: 2,
  numberOfChildren: 0,
  numberOfInfants: 0,
  specialRequests: 'Vegetarian meals',
  emergencyContactName: 'Jane Doe',
  emergencyContactPhone: '+1234567891',
  passengers: [
    {
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: new Date('1990-01-01'),
      passportNumber: 'A12345678',
      nationality: 'US',
      dietaryRequirements: 'Vegetarian'
    }
  ]
};

this.bookingService.createBooking(bookingData)
  .subscribe({
    next: (booking) => {
      this.notificationService.showSuccess('Booking created successfully!');
      this.router.navigate(['/bookings', booking.id]);
    },
    error: (error) => {
      this.notificationService.showError('Failed to create booking');
    }
  });
```

### PUT /bookings/{id}/cancel
Cancel a booking.

**Authentication:** Required
**Path Parameters:**
- `id` (int): Booking ID

**Request Body:**
```json
{
  "reason": "Change of plans"
}
```

**Response:**
```json
{
  "id": 1,
  "status": 3,
  "statusText": "Cancelled",
  "cancellationReason": "Change of plans",
  "cancellationDate": "2024-01-20T10:30:00Z",
  "refundAmount": 1800.00,
  "refundStatus": "Processing"
}
```

**Angular Service Usage:**
```typescript
this.bookingService.cancelBooking(1, { reason: 'Change of plans' })
  .subscribe({
    next: (booking) => {
      this.notificationService.showSuccess('Booking cancelled successfully');
      this.selectedBooking.set(booking);
    },
    error: (error) => {
      this.notificationService.showError('Failed to cancel booking');
    }
  });
```
