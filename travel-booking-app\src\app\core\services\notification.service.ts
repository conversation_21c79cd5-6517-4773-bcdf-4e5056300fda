import { Injectable, signal } from '@angular/core';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  duration?: number;
  persistent?: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary';
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private _notifications = signal<Notification[]>([]);
  readonly notifications = this._notifications.asReadonly();

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  private addNotification(notification: Omit<Notification, 'id'>): void {
    const duration = notification.duration ?? 5000;
    const newNotification: Notification = {
      ...notification,
      id: this.generateId(),
      duration
    };

    this._notifications.update(notifications => [...notifications, newNotification]);

    // Auto-remove notification after duration (unless persistent)
    if (!newNotification.persistent && duration > 0) {
      setTimeout(() => {
        this.removeNotification(newNotification.id);
      }, duration);
    }
  }

  showSuccess(message: string, title?: string, options?: Partial<Notification>): void {
    this.addNotification({
      type: 'success',
      title,
      message,
      ...options
    });
  }

  showError(message: string, title?: string, options?: Partial<Notification>): void {
    this.addNotification({
      type: 'error',
      title: title || 'Error',
      message,
      duration: 8000, // Longer duration for errors
      ...options
    });
  }

  showWarning(message: string, title?: string, options?: Partial<Notification>): void {
    this.addNotification({
      type: 'warning',
      title: title || 'Warning',
      message,
      ...options
    });
  }

  showInfo(message: string, title?: string, options?: Partial<Notification>): void {
    this.addNotification({
      type: 'info',
      title,
      message,
      ...options
    });
  }

  removeNotification(id: string): void {
    this._notifications.update(notifications => 
      notifications.filter(notification => notification.id !== id)
    );
  }

  clearAll(): void {
    this._notifications.set([]);
  }

  // Convenience methods for common scenarios
  showValidationError(errors: string[]): void {
    const message = errors.length === 1 
      ? errors[0] 
      : `Multiple validation errors:\n${errors.join('\n')}`;
    
    this.showError(message, 'Validation Error');
  }

  showNetworkError(): void {
    this.showError(
      'Please check your internet connection and try again.',
      'Network Error'
    );
  }

  showSaveSuccess(itemName: string = 'Item'): void {
    this.showSuccess(`${itemName} saved successfully!`);
  }

  showDeleteSuccess(itemName: string = 'Item'): void {
    this.showSuccess(`${itemName} deleted successfully!`);
  }

  showConfirmation(
    message: string,
    onConfirm: () => void,
    onCancel?: () => void,
    title: string = 'Confirm Action'
  ): void {
    const actions: NotificationAction[] = [
      {
        label: 'Confirm',
        action: () => {
          onConfirm();
          // The notification will be removed when the action is executed
        },
        style: 'primary'
      },
      {
        label: 'Cancel',
        action: () => {
          if (onCancel) onCancel();
          // The notification will be removed when the action is executed
        },
        style: 'secondary'
      }
    ];

    this.addNotification({
      type: 'warning',
      title,
      message,
      persistent: true,
      actions
    });
  }

  showLoadingNotification(message: string = 'Loading...'): string {
    const notification: Notification = {
      id: this.generateId(),
      type: 'info',
      message,
      persistent: true
    };

    this._notifications.update(notifications => [...notifications, notification]);
    return notification.id;
  }

  updateLoadingNotification(id: string, message: string): void {
    this._notifications.update(notifications =>
      notifications.map(notification =>
        notification.id === id
          ? { ...notification, message }
          : notification
      )
    );
  }

  hideLoadingNotification(id: string): void {
    this.removeNotification(id);
  }
}
