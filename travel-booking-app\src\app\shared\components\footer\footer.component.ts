import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <!-- Company Info -->
          <div class="footer-section">
            <div class="footer-logo">
              <span class="logo-icon">✈️</span>
              <span class="logo-text">TravelBooking</span>
            </div>
            <p class="footer-description">
              Discover amazing destinations and create unforgettable memories with our curated travel experiences.
            </p>
            <div class="social-links">
              <a href="#" class="social-link" title="Facebook">📘</a>
              <a href="#" class="social-link" title="Twitter">🐦</a>
              <a href="#" class="social-link" title="Instagram">📷</a>
              <a href="#" class="social-link" title="YouTube">📺</a>
            </div>
          </div>

          <!-- Quick Links -->
          <div class="footer-section">
            <h3>Quick Links</h3>
            <ul class="footer-links">
              <li><a routerLink="/home">Home</a></li>
              <li><a routerLink="/trips">Browse Trips</a></li>
              <li><a routerLink="/blogs">Travel Blog</a></li>
              <li><a href="/about">About Us</a></li>
              <li><a href="/contact">Contact</a></li>
            </ul>
          </div>

          <!-- Support -->
          <div class="footer-section">
            <h3>Support</h3>
            <ul class="footer-links">
              <li><a href="/help">Help Center</a></li>
              <li><a href="/faq">FAQ</a></li>
              <li><a href="/booking-policy">Booking Policy</a></li>
              <li><a href="/cancellation">Cancellation</a></li>
              <li><a href="/travel-insurance">Travel Insurance</a></li>
            </ul>
          </div>

          <!-- Legal -->
          <div class="footer-section">
            <h3>Legal</h3>
            <ul class="footer-links">
              <li><a href="/terms">Terms of Service</a></li>
              <li><a href="/privacy">Privacy Policy</a></li>
              <li><a href="/cookies">Cookie Policy</a></li>
              <li><a href="/disclaimer">Disclaimer</a></li>
            </ul>
          </div>

          <!-- Newsletter -->
          <div class="footer-section newsletter">
            <h3>Stay Updated</h3>
            <p>Subscribe to our newsletter for travel tips and exclusive deals.</p>
            <form class="newsletter-form" (ngSubmit)="subscribeNewsletter()">
              <div class="input-group">
                <input 
                  type="email" 
                  placeholder="Enter your email"
                  [(ngModel)]="emailAddress"
                  required
                />
                <button type="submit" class="btn btn-primary">Subscribe</button>
              </div>
            </form>
          </div>
        </div>

        <!-- Footer Bottom -->
        <div class="footer-bottom">
          <div class="footer-bottom-content">
            <div class="copyright">
              <p>&copy; {{ currentYear }} TravelBooking. All rights reserved.</p>
            </div>
            <div class="footer-bottom-links">
              <a href="/sitemap">Sitemap</a>
              <a href="/accessibility">Accessibility</a>
              <a href="/careers">Careers</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  `,
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent {
  currentYear = new Date().getFullYear();
  emailAddress = '';

  subscribeNewsletter(): void {
    if (this.emailAddress) {
      // TODO: Implement newsletter subscription
      console.log('Subscribing email:', this.emailAddress);
      this.emailAddress = '';
    }
  }
}
