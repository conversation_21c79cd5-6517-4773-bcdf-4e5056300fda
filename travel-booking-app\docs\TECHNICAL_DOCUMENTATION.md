# Travel Booking App - Technical Documentation

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Project Structure](#project-structure)
3. [Core Technologies](#core-technologies)
4. [API Integration](#api-integration)
5. [State Management](#state-management)
6. [Authentication System](#authentication-system)
7. [Routing and Navigation](#routing-and-navigation)
8. [Components Architecture](#components-architecture)
9. [Services Documentation](#services-documentation)
10. [Models and Interfaces](#models-and-interfaces)
11. [Styling and Theming](#styling-and-theming)
12. [Performance Optimizations](#performance-optimizations)
13. [PWA Features](#pwa-features)
14. [Testing Strategy](#testing-strategy)
15. [Build and Deployment](#build-and-deployment)

## Architecture Overview

### Application Architecture

The Travel Booking App follows a **modular, feature-based architecture** using Angular 20's latest features:

```
┌─────────────────────────────────────────┐
│                 Browser                 │
├─────────────────────────────────────────┤
│            Angular App (SPA)            │
│  ┌─────────────────────────────────────┐ │
│  │         Presentation Layer          │ │
│  │  - Components                       │ │
│  │  - Directives                       │ │
│  │  - Pipes                           │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │         Business Logic Layer        │ │
│  │  - Services                        │ │
│  │  - State Management                │ │
│  │  - Guards                          │ │
│  └─────────────────────────────────────┘ │
│  ┌─────────────────────────────────────┐ │
│  │         Data Access Layer           │ │
│  │  - HTTP Client                     │ │
│  │  - Interceptors                    │ │
│  │  - API Services                    │ │
│  └─────────────────────────────────────┘ │
├─────────────────────────────────────────┤
│              HTTP/HTTPS                 │
├─────────────────────────────────────────┤
│         TravelTourism.API               │
│         (Backend Server)                │
└─────────────────────────────────────────┘
```

### Key Architectural Principles

1. **Standalone Components**: All components are standalone, reducing bundle size
2. **Signal-Based Reactivity**: Using Angular Signals for reactive state management
3. **Zoneless Change Detection**: Improved performance with zoneless architecture
4. **Lazy Loading**: Feature modules loaded on-demand
5. **Dependency Injection**: Hierarchical DI for service management
6. **Reactive Programming**: RxJS for asynchronous operations

## Project Structure

```
src/
├── app/
│   ├── core/                           # Core functionality (singleton services)
│   │   ├── guards/                     # Route guards
│   │   │   ├── auth.guard.ts          # Authentication guard
│   │   │   └── admin.guard.ts         # Admin authorization guard
│   │   ├── interceptors/               # HTTP interceptors
│   │   │   ├── auth.interceptor.ts    # JWT token interceptor
│   │   │   └── error.interceptor.ts   # Global error handler
│   │   ├── models/                     # TypeScript interfaces
│   │   │   ├── auth.model.ts          # Authentication models
│   │   │   ├── user.model.ts          # User-related models
│   │   │   ├── trip.model.ts          # Trip-related models
│   │   │   ├── booking.model.ts       # Booking models
│   │   │   ├── blog.model.ts          # Blog models
│   │   │   └── common.model.ts        # Shared models
│   │   └── services/                   # Core services
│   │       ├── api.service.ts         # Base HTTP service
│   │       ├── auth.service.ts        # Authentication service
│   │       ├── trip.service.ts        # Trip management service
│   │       ├── booking.service.ts     # Booking service
│   │       ├── blog.service.ts        # Blog service
│   │       ├── state.service.ts       # Global state management
│   │       ├── notification.service.ts # Toast notifications
│   │       ├── animation.service.ts   # Animation utilities
│   │       ├── performance.service.ts # Performance monitoring
│   │       ├── pwa.service.ts         # PWA functionality
│   │       └── seo.service.ts         # SEO optimization
│   ├── features/                       # Feature modules (lazy-loaded)
│   │   ├── auth/                      # Authentication feature
│   │   │   ├── login/                 # Login component
│   │   │   └── register/              # Registration component
│   │   ├── home/                      # Home page
│   │   ├── trips/                     # Trip browsing and details
│   │   │   ├── trips.component.ts     # Trip listing
│   │   │   └── trip-detail/           # Trip detail page
│   │   ├── blogs/                     # Blog system
│   │   │   ├── blogs.component.ts     # Blog listing
│   │   │   └── blog-detail/           # Blog detail page
│   │   ├── bookings/                  # Booking management
│   │   │   ├── booking-list/          # User bookings
│   │   │   └── booking-detail/        # Booking details
│   │   ├── dashboard/                 # User dashboard
│   │   ├── profile/                   # User profile management
│   │   └── admin/                     # Admin panel
│   │       ├── dashboard/             # Admin dashboard
│   │       ├── trips/                 # Trip management
│   │       ├── blogs/                 # Blog management
│   │       ├── users/                 # User management
│   │       └── bookings/              # Booking management
│   ├── shared/                        # Shared components and utilities
│   │   ├── components/                # Reusable UI components
│   │   │   ├── header/                # Navigation header
│   │   │   ├── footer/                # Page footer
│   │   │   ├── notification/          # Toast notifications
│   │   │   └── pwa-install/           # PWA install prompt
│   │   └── directives/                # Custom directives
│   │       └── animate.directive.ts   # Animation directives
│   ├── environments/                  # Environment configurations
│   │   ├── environment.ts             # Development config
│   │   └── environment.prod.ts        # Production config
│   ├── app.ts                         # Root component
│   ├── app.routes.ts                  # Application routing
│   └── main.ts                        # Application bootstrap
├── assets/                            # Static assets
│   ├── images/                        # Image files
│   ├── icons/                         # Icon files
│   └── fonts/                         # Font files
├── styles/                            # Global styles
│   ├── _variables.scss                # SCSS variables
│   ├── _mixins.scss                   # SCSS mixins
│   ├── _components.scss               # Component styles
│   └── styles.scss                    # Main stylesheet
└── index.html                         # Main HTML file
```

## Core Technologies

### Angular 20 Features Used

1. **Zoneless Change Detection**
   ```typescript
   // main.ts
   bootstrapApplication(AppComponent, {
     providers: [
       provideExperimentalZonelessChangeDetection(),
       // other providers
     ]
   });
   ```

2. **Standalone Components**
   ```typescript
   @Component({
     selector: 'app-home',
     standalone: true,
     imports: [CommonModule, RouterModule],
     template: `...`
   })
   export class HomeComponent { }
   ```

3. **Angular Signals**
   ```typescript
   export class HomeComponent {
     featuredTrips = signal<Trip[]>([]);
     isLoading = signal<boolean>(false);
     
     // Computed signals
     tripCount = computed(() => this.featuredTrips().length);
   }
   ```

4. **Control Flow Syntax**
   ```html
   @if (isLoading()) {
     <div class="loading">Loading...</div>
   } @else if (trips().length === 0) {
     <div class="empty">No trips found</div>
   } @else {
     @for (trip of trips(); track trip.id) {
       <div class="trip-card">{{ trip.name }}</div>
     }
   }
   ```

### TypeScript Configuration

```json
{
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "exactOptionalPropertyTypes": true
  }
}
```

### SCSS Architecture

```scss
// styles/_variables.scss
$primary-color: #667eea;
$secondary-color: #764ba2;
$success-color: #38a169;
$warning-color: #f6ad55;
$error-color: #e53e3e;

// Breakpoints
$mobile: 480px;
$tablet: 768px;
$desktop: 1024px;
$large-desktop: 1200px;

// Typography
$font-family-base: 'Inter', sans-serif;
$font-size-base: 1rem;
$line-height-base: 1.6;
```

## API Integration

### Base API Service

The `ApiService` provides a centralized HTTP client with error handling and request/response transformation:

```typescript
@Injectable({ providedIn: 'root' })
export class ApiService {
  private readonly baseUrl = 'https://localhost:7115/api/v1';
  private readonly http = inject(HttpClient);

  // Generic GET request
  get<T>(endpoint: string, params?: any): Observable<T> {
    return this.http.get<T>(`${this.baseUrl}/${endpoint}`, { params })
      .pipe(catchError(this.handleError));
  }

  // Generic POST request
  post<T>(endpoint: string, data: any): Observable<T> {
    return this.http.post<T>(`${this.baseUrl}/${endpoint}`, data)
      .pipe(catchError(this.handleError));
  }

  // Paginated GET request
  getPaged<T>(endpoint: string, pagination: PaginationParameters, filters?: any): Observable<PagedResult<T>> {
    const params = this.buildParams(pagination, filters);
    return this.http.get<PagedResult<T>>(`${this.baseUrl}/${endpoint}`, { params })
      .pipe(catchError(this.handleError));
  }
}
```

### HTTP Interceptors

#### Authentication Interceptor
```typescript
@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    const token = localStorage.getItem('auth_token');
    
    if (token) {
      const authReq = req.clone({
        headers: req.headers.set('Authorization', `Bearer ${token}`)
      });
      return next.handle(authReq);
    }
    
    return next.handle(req);
  }
}
```

#### Error Interceptor
```typescript
@Injectable()
export class ErrorInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          // Handle unauthorized
          this.authService.logout();
        } else if (error.status === 403) {
          // Handle forbidden
          this.router.navigate(['/']);
        }
        return throwError(() => error);
      })
    );
  }
}
```

## State Management

### Signal-Based State Management

The application uses Angular Signals for reactive state management:

```typescript
@Injectable({ providedIn: 'root' })
export class StateService {
  // User state
  private readonly _currentUser = signal<User | null>(null);
  private readonly _isAuthenticated = signal<boolean>(false);
  
  // UI state
  private readonly _isLoading = signal<boolean>(false);
  private readonly _notifications = signal<Notification[]>([]);
  
  // Trip state
  private readonly _featuredTrips = signal<Trip[]>([]);
  private readonly _selectedTrip = signal<Trip | null>(null);
  
  // Computed signals
  readonly userRole = computed(() => this._currentUser()?.role || 'Guest');
  readonly isAdmin = computed(() => this.userRole() === 'Admin');
  readonly notificationCount = computed(() => this._notifications().length);
  
  // Read-only accessors
  readonly currentUser = this._currentUser.asReadonly();
  readonly isAuthenticated = this._isAuthenticated.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  
  // State mutations
  setCurrentUser(user: User | null): void {
    this._currentUser.set(user);
    this._isAuthenticated.set(!!user);
  }
  
  setLoading(loading: boolean): void {
    this._isLoading.set(loading);
  }
  
  addNotification(notification: Notification): void {
    this._notifications.update(notifications => [...notifications, notification]);
  }
}
```

### Local Storage Integration

```typescript
export class StateService {
  private readonly STORAGE_KEYS = {
    USER: 'current_user',
    TOKEN: 'auth_token',
    REFRESH_TOKEN: 'refresh_token',
    PREFERENCES: 'user_preferences'
  };

  saveToStorage(key: string, data: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  loadFromStorage<T>(key: string): T | null {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error loading from localStorage:', error);
      return null;
    }
  }
}
```

## Authentication System

### JWT Token Management

```typescript
@Injectable({ providedIn: 'root' })
export class AuthService {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  
  login(credentials: LoginDto): Observable<AuthResultDto> {
    return this.apiService.post<AuthResultDto>('auth/login', credentials)
      .pipe(
        tap(result => {
          this.setTokens(result.token, result.refreshToken);
          this.setCurrentUser(result.user);
        })
      );
  }
  
  refreshToken(): Observable<AuthResultDto> {
    const refreshToken = localStorage.getItem(this.REFRESH_TOKEN_KEY);
    return this.apiService.post<AuthResultDto>('auth/refresh-token', { refreshToken })
      .pipe(
        tap(result => {
          this.setTokens(result.token, result.refreshToken);
        })
      );
  }
  
  private setTokens(token: string, refreshToken: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }
  
  private isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 < Date.now();
    } catch {
      return true;
    }
  }
}
```

### Route Guards

#### Authentication Guard
```typescript
@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(): boolean {
    if (this.authService.isAuthenticated()) {
      return true;
    }
    
    this.router.navigate(['/auth/login']);
    return false;
  }
}
```

#### Admin Guard
```typescript
@Injectable({ providedIn: 'root' })
export class AdminGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(): boolean {
    if (this.authService.isAdmin()) {
      return true;
    }
    
    this.router.navigate(['/']);
    return false;
  }
}
```

## Routing and Navigation

### Route Configuration

```typescript
export const routes: Routes = [
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  { 
    path: 'home', 
    loadComponent: () => import('./features/home/<USER>').then(m => m.HomeComponent)
  },
  {
    path: 'auth',
    children: [
      { 
        path: 'login', 
        loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
      },
      { 
        path: 'register', 
        loadComponent: () => import('./features/auth/register/register.component').then(m => m.RegisterComponent)
      }
    ]
  },
  {
    path: 'trips',
    children: [
      { 
        path: '', 
        loadComponent: () => import('./features/trips/trips.component').then(m => m.TripsComponent)
      },
      { 
        path: ':id', 
        loadComponent: () => import('./features/trips/trip-detail/trip-detail.component').then(m => m.TripDetailComponent)
      }
    ]
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'admin',
    canActivate: [AuthGuard, AdminGuard],
    children: [
      { 
        path: '', 
        loadComponent: () => import('./features/admin/dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent)
      },
      { 
        path: 'trips', 
        loadComponent: () => import('./features/admin/trips/admin-trips.component').then(m => m.AdminTripsComponent)
      }
    ]
  },
  { path: '**', redirectTo: '/home' }
];
```

### Navigation Service

```typescript
@Injectable({ providedIn: 'root' })
export class NavigationService {
  constructor(private router: Router) {}

  navigateToTrip(tripId: number): void {
    this.router.navigate(['/trips', tripId]);
  }

  navigateToBooking(bookingId: number): void {
    this.router.navigate(['/bookings', bookingId]);
  }

  navigateWithParams(route: string[], params: any): void {
    this.router.navigate(route, { queryParams: params });
  }

  goBack(): void {
    window.history.back();
  }
}

## API Requests Documentation

### Authentication Endpoints

#### POST /auth/login
**Purpose**: User authentication
**Request Body**:
```typescript
interface LoginDto {
  email: string;
  password: string;
}
```
**Response**:
```typescript
interface AuthResultDto {
  user: User;
  token: string;
  refreshToken: string;
  expiresAt: Date;
}
```
**Usage**:
```typescript
this.authService.login({ email: '<EMAIL>', password: 'password123' })
  .subscribe(result => {
    // Handle successful login
  });
```

#### POST /auth/register
**Purpose**: User registration
**Request Body**:
```typescript
interface RegisterDto {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phoneNumber: string;
  dateOfBirth: Date;
}
```
**Response**: Same as login (`AuthResultDto`)

#### POST /auth/refresh-token
**Purpose**: Refresh JWT token
**Request Body**:
```typescript
interface RefreshTokenDto {
  refreshToken: string;
}
```
**Response**: `AuthResultDto`

#### POST /auth/forgot-password
**Purpose**: Request password reset
**Request Body**:
```typescript
interface ForgotPasswordDto {
  email: string;
}
```
**Response**:
```typescript
interface MessageResponse {
  message: string;
}
```

#### POST /auth/reset-password
**Purpose**: Reset user password
**Request Body**:
```typescript
interface ResetPasswordDto {
  token: string;
  newPassword: string;
  confirmPassword: string;
}
```
**Response**: `MessageResponse`

### Trip Endpoints

#### GET /trips
**Purpose**: Get paginated trips with filtering
**Query Parameters**:
```typescript
interface TripFilterDto {
  search?: string;
  categoryId?: number;
  destinationCityId?: number;
  minPrice?: number;
  maxPrice?: number;
  minDuration?: number;
  maxDuration?: number;
  difficulty?: number;
  isFeatured?: boolean;
  availableFrom?: Date;
  availableTo?: Date;
}

interface PaginationParameters {
  page: number;
  pageSize: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
```
**Response**:
```typescript
interface PagedResult<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
}
```
**Usage**:
```typescript
const pagination = { page: 1, pageSize: 10, sortBy: 'name', sortOrder: 'asc' };
const filters = { categoryId: 1, minPrice: 500 };

this.tripService.getTrips(pagination, filters)
  .subscribe(result => {
    this.trips.set(result.items);
    this.totalCount.set(result.totalCount);
  });
```

#### GET /trips/{id}
**Purpose**: Get trip details by ID
**Path Parameters**: `id` (number)
**Response**: `TripDetail`
**Usage**:
```typescript
this.tripService.getTripById(1)
  .subscribe(trip => {
    this.selectedTrip.set(trip);
  });
```

#### GET /trips/featured
**Purpose**: Get featured trips
**Response**: `Trip[]`
**Usage**:
```typescript
this.tripService.getFeaturedTrips()
  .subscribe(trips => {
    this.featuredTrips.set(trips);
  });
```

#### GET /trips/categories
**Purpose**: Get trip categories
**Response**: `TripCategory[]`
**Usage**:
```typescript
this.tripService.getTripCategories()
  .subscribe(categories => {
    this.categories.set(categories);
  });
```

### Booking Endpoints

#### GET /bookings
**Purpose**: Get user's bookings
**Query Parameters**: `PaginationParameters` + `BookingFilterDto`
```typescript
interface BookingFilterDto {
  status?: BookingStatus;
  tripId?: number;
  dateFrom?: Date;
  dateTo?: Date;
}
```
**Response**: `PagedResult<Booking>`
**Usage**:
```typescript
const pagination = { page: 1, pageSize: 10 };
const filters = { status: BookingStatus.Confirmed };

this.bookingService.getUserBookings(pagination, filters)
  .subscribe(result => {
    this.bookings.set(result.items);
  });
```

#### GET /bookings/{id}
**Purpose**: Get booking details
**Path Parameters**: `id` (number)
**Response**: `Booking`
**Usage**:
```typescript
this.bookingService.getBookingById(1)
  .subscribe(booking => {
    this.selectedBooking.set(booking);
  });
```

#### POST /bookings
**Purpose**: Create new booking
**Request Body**:
```typescript
interface CreateBookingDto {
  tripId: number;
  travelDate: Date;
  numberOfAdults: number;
  numberOfChildren: number;
  numberOfInfants: number;
  specialRequests?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  passengers: CreateBookingPassengerDto[];
}

interface CreateBookingPassengerDto {
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  passportNumber?: string;
  nationality?: string;
  dietaryRequirements?: string;
}
```
**Response**: `Booking`
**Usage**:
```typescript
const bookingData: CreateBookingDto = {
  tripId: 1,
  travelDate: new Date('2024-06-01'),
  numberOfAdults: 2,
  numberOfChildren: 0,
  numberOfInfants: 0,
  passengers: [
    {
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: new Date('1990-01-01'),
      passportNumber: 'A12345678'
    }
  ]
};

this.bookingService.createBooking(bookingData)
  .subscribe(booking => {
    this.router.navigate(['/bookings', booking.id]);
  });
```

#### PUT /bookings/{id}/cancel
**Purpose**: Cancel booking
**Path Parameters**: `id` (number)
**Request Body**:
```typescript
interface CancelBookingDto {
  reason?: string;
}
```
**Response**: `Booking`
**Usage**:
```typescript
this.bookingService.cancelBooking(1, { reason: 'Change of plans' })
  .subscribe(booking => {
    this.notificationService.showSuccess('Booking cancelled successfully');
  });
```

### Blog Endpoints

#### GET /blogs
**Purpose**: Get paginated blogs
**Query Parameters**: `PaginationParameters` + `BlogFilterDto`
```typescript
interface BlogFilterDto {
  search?: string;
  categoryId?: number;
  authorId?: number;
  isPublished?: boolean;
  tags?: string[];
}
```
**Response**: `PagedResult<Blog>`
**Usage**:
```typescript
const pagination = { page: 1, pageSize: 6 };
const filters = { isPublished: true, categoryId: 1 };

this.blogService.getBlogs(pagination, filters)
  .subscribe(result => {
    this.blogs.set(result.items);
  });
```

#### GET /blogs/{id}
**Purpose**: Get blog details
**Path Parameters**: `id` (number)
**Response**: `Blog`
**Usage**:
```typescript
this.blogService.getBlogById(1)
  .subscribe(blog => {
    this.selectedBlog.set(blog);
    this.seoService.updateBlogPage(blog);
  });
```

#### GET /blogs/recent
**Purpose**: Get recent blogs
**Query Parameters**: `limit` (number, optional)
**Response**: `Blog[]`
**Usage**:
```typescript
this.blogService.getRecentBlogs(5)
  .subscribe(blogs => {
    this.recentBlogs.set(blogs);
  });
```

#### GET /blogs/categories
**Purpose**: Get blog categories
**Response**: `BlogCategory[]`
**Usage**:
```typescript
this.blogService.getBlogCategories()
  .subscribe(categories => {
    this.blogCategories.set(categories);
  });
```

### User Management Endpoints

#### GET /users/profile
**Purpose**: Get current user profile
**Response**: `User`
**Usage**:
```typescript
this.userService.getProfile()
  .subscribe(user => {
    this.currentUser.set(user);
  });
```

#### PUT /users/profile
**Purpose**: Update user profile
**Request Body**:
```typescript
interface UpdateUserProfileDto {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  dateOfBirth?: Date;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emailNotifications?: boolean;
  smsNotifications?: boolean;
  newsletter?: boolean;
}
```
**Response**: `User`
**Usage**:
```typescript
const updateData: UpdateUserProfileDto = {
  firstName: 'John',
  lastName: 'Doe Updated',
  phoneNumber: '+1234567890'
};

this.userService.updateProfile(updateData)
  .subscribe(user => {
    this.currentUser.set(user);
    this.notificationService.showSuccess('Profile updated successfully');
  });
```

#### POST /users/change-password
**Purpose**: Change user password
**Request Body**:
```typescript
interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}
```
**Response**: `MessageResponse`
**Usage**:
```typescript
const passwordData: ChangePasswordDto = {
  currentPassword: 'oldpassword',
  newPassword: 'newpassword123',
  confirmPassword: 'newpassword123'
};

this.userService.changePassword(passwordData)
  .subscribe(() => {
    this.notificationService.showSuccess('Password changed successfully');
  });
```

### Admin Endpoints

#### GET /admin/dashboard/stats
**Purpose**: Get admin dashboard statistics
**Response**:
```typescript
interface DashboardStats {
  totalUsers: number;
  totalBookings: number;
  totalRevenue: number;
  totalTrips: number;
  pendingBookings: number;
  activeUsers: number;
  popularDestinations: Array<{name: string; bookings: number}>;
  recentBookings: Array<any>;
  monthlyRevenue: Array<{month: string; revenue: number}>;
  bookingsByStatus: Array<{status: string; count: number}>;
}
```
**Usage**:
```typescript
this.adminService.getDashboardStats()
  .subscribe(stats => {
    this.dashboardStats.set(stats);
  });
```

#### POST /admin/trips
**Purpose**: Create new trip (Admin only)
**Request Body**:
```typescript
interface CreateTripDto {
  name: string;
  shortDescription: string;
  description: string;
  price: number;
  discountPrice?: number;
  duration: number;
  maxCapacity: number;
  minAge?: number;
  maxAge?: number;
  difficulty: number;
  categoryId: number;
  destinationCityId: number;
  departureCityId: number;
  mainImageUrl: string;
  isFeatured: boolean;
  availableFrom: Date;
  availableTo: Date;
  includesAccommodation: boolean;
  includesTransport: boolean;
  includesMeals: boolean;
  includesGuide: boolean;
  itineraries: CreateTripItinerary[];
}
```
**Response**: `Trip`
**Usage**:
```typescript
const tripData: CreateTripDto = {
  name: 'Amazing Bali Adventure',
  shortDescription: 'Explore the beauty of Bali',
  description: 'Full description...',
  price: 1200,
  duration: 7,
  maxCapacity: 20,
  difficulty: 2,
  categoryId: 1,
  destinationCityId: 1,
  departureCityId: 2,
  mainImageUrl: 'bali-main.jpg',
  isFeatured: true,
  availableFrom: new Date('2024-01-01'),
  availableTo: new Date('2024-12-31'),
  includesAccommodation: true,
  includesTransport: true,
  includesMeals: false,
  includesGuide: true,
  itineraries: []
};

this.tripService.createTrip(tripData)
  .subscribe(trip => {
    this.notificationService.showSuccess('Trip created successfully');
    this.router.navigate(['/admin/trips']);
  });
```

#### PUT /admin/trips/{id}
**Purpose**: Update trip (Admin only)
**Path Parameters**: `id` (number)
**Request Body**: `UpdateTripDto` (partial `CreateTripDto`)
**Response**: `Trip`

#### DELETE /admin/trips/{id}
**Purpose**: Delete trip (Admin only)
**Path Parameters**: `id` (number)
**Response**: `void`

#### GET /admin/users
**Purpose**: Get all users (Admin only)
**Query Parameters**: `PaginationParameters` + `UserFilterDto`
**Response**: `PagedResult<User>`

#### GET /admin/bookings
**Purpose**: Get all bookings (Admin only)
**Query Parameters**: `PaginationParameters` + `BookingFilterDto`
**Response**: `PagedResult<Booking>`

## Components Architecture

### Component Hierarchy

```
AppComponent
├── HeaderComponent
│   ├── NavigationComponent
│   └── UserMenuComponent
├── RouterOutlet
│   ├── HomeComponent
│   │   ├── HeroSectionComponent
│   │   ├── FeaturedTripsComponent
│   │   └── RecentBlogsComponent
│   ├── TripsComponent
│   │   ├── TripFiltersComponent
│   │   ├── TripCardComponent (multiple)
│   │   └── PaginationComponent
│   ├── TripDetailComponent
│   │   ├── TripGalleryComponent
│   │   ├── TripInfoComponent
│   │   ├── TripItineraryComponent
│   │   └── BookingFormComponent
│   ├── BookingListComponent
│   │   ├── BookingCardComponent (multiple)
│   │   └── BookingFiltersComponent
│   ├── AdminDashboardComponent
│   │   ├── StatsCardsComponent
│   │   ├── ChartsComponent
│   │   └── RecentActivityComponent
│   └── AdminTripsComponent
│       ├── TripTableComponent
│       ├── TripFormModalComponent
│       └── TripActionsComponent
├── FooterComponent
├── NotificationComponent
└── PWAInstallComponent
```

### Core Components

#### AppComponent
**Purpose**: Root application component
**Key Features**:
- Application shell
- Global error boundary
- PWA install prompt
- Notification system

```typescript
@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    HeaderComponent,
    FooterComponent,
    NotificationComponent,
    PWAInstallComponent
  ],
  template: `
    <div class="app-container">
      <app-header />
      <main class="main-content">
        <router-outlet />
      </main>
      <app-footer />
      <app-notification />
      <app-pwa-install />
    </div>
  `
})
export class AppComponent {
  title = 'Travel Booking App';
}
```

#### HeaderComponent
**Purpose**: Navigation header with user menu
**Key Features**:
- Responsive navigation
- User authentication status
- Search functionality
- Mobile menu toggle

```typescript
@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <header class="header">
      <nav class="navbar">
        <div class="nav-brand">
          <a routerLink="/" class="brand-logo">
            <img src="/assets/images/logo.png" alt="Travel Booking App" />
          </a>
        </div>

        <div class="nav-menu" [class.active]="isMenuOpen()">
          <a routerLink="/trips" routerLinkActive="active">Trips</a>
          <a routerLink="/blogs" routerLinkActive="active">Blog</a>

          @if (authService.isAuthenticated()) {
            <div class="user-menu">
              <button class="user-avatar" (click)="toggleUserMenu()">
                <img [src]="authService.currentUser()?.profilePictureUrl || '/assets/images/default-avatar.png'"
                     [alt]="authService.currentUser()?.firstName" />
              </button>

              @if (isUserMenuOpen()) {
                <div class="dropdown-menu">
                  <a routerLink="/dashboard">Dashboard</a>
                  <a routerLink="/profile">Profile</a>
                  <a routerLink="/bookings">My Bookings</a>
                  @if (authService.isAdmin()) {
                    <a routerLink="/admin">Admin Panel</a>
                  }
                  <hr>
                  <button (click)="logout()">Logout</button>
                </div>
              }
            </div>
          } @else {
            <a routerLink="/auth/login" class="btn btn-outline">Login</a>
            <a routerLink="/auth/register" class="btn btn-primary">Sign Up</a>
          }
        </div>

        <button class="mobile-toggle" (click)="toggleMobileMenu()">
          <span></span>
          <span></span>
          <span></span>
        </button>
      </nav>
    </header>
  `
})
export class HeaderComponent {
  readonly authService = inject(AuthService);
  private readonly router = inject(Router);

  isMenuOpen = signal<boolean>(false);
  isUserMenuOpen = signal<boolean>(false);

  toggleMobileMenu(): void {
    this.isMenuOpen.update(open => !open);
  }

  toggleUserMenu(): void {
    this.isUserMenuOpen.update(open => !open);
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/']);
  }
}
```

#### HomeComponent
**Purpose**: Landing page with featured content
**Key Features**:
- Hero section with search
- Featured trips display
- Recent blog posts
- Call-to-action sections

```typescript
@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    AnimateDirective,
    ParallaxDirective,
    StaggerDirective,
    TiltDirective,
    RevealOnScrollDirective
  ],
  template: `
    <div class="home-container">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title" appAnimate="fadeIn" [animationDelay]="200">
              Discover Your Next Adventure
            </h1>
            <p class="hero-subtitle" appAnimate="fadeIn" [animationDelay]="400">
              Explore breathtaking destinations and create unforgettable memories
            </p>
            <div class="hero-actions" appAnimate="fadeIn" [animationDelay]="600">
              <button class="btn btn-primary" routerLink="/trips">
                Explore Trips
              </button>
              <button class="btn btn-outline" routerLink="/blogs">
                Travel Stories
              </button>
            </div>
          </div>
          <div class="hero-image" appAnimate="slideInRight" [animationDelay]="300">
            <img src="/assets/images/hero-travel.jpg" alt="Travel Adventure" />
          </div>
        </div>
      </section>

      <!-- Search Section -->
      <section class="search-section" appRevealOnScroll>
        <div class="container">
          <div class="search-card">
            <h2>Where do you want to go?</h2>
            <form (ngSubmit)="onSearch()" class="search-form">
              <div class="search-inputs">
                <input
                  type="text"
                  [(ngModel)]="searchQuery"
                  name="search"
                  placeholder="Search destinations..."
                  class="search-input"
                />
                <button type="submit" class="btn btn-primary">
                  Search Trips
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      <!-- Featured Trips -->
      <section class="featured-trips">
        <div class="container">
          <div class="section-header" appRevealOnScroll>
            <h2>Featured Destinations</h2>
            <p>Handpicked adventures for the ultimate travel experience</p>
          </div>

          @if (isLoading()) {
            <div class="loading-grid">
              @for (item of [1,2,3,4]; track item) {
                <div class="trip-card-skeleton loading-skeleton"></div>
              }
            </div>
          } @else if (featuredTrips().length === 0) {
            <div class="empty-state">
              <div class="empty-icon">🎒</div>
              <h3>No featured trips available</h3>
              <p>Check back soon for amazing travel opportunities!</p>
            </div>
          } @else {
            <div class="trips-grid" appStagger="scaleIn" [staggerDelay]="150" staggerSelector=".trip-card">
              @for (trip of featuredTrips(); track trip.id) {
                <div class="trip-card stagger-item" [routerLink]="['/trips', trip.id]" appTilt>
                  <div class="trip-image">
                    <img [src]="trip.mainImageUrl" [alt]="trip.name" />
                    @if (trip.hasDiscount) {
                      <div class="discount-badge">
                        {{ trip.discountPercentage }}% OFF
                      </div>
                    }
                  </div>
                  <div class="trip-content">
                    <div class="trip-category">{{ trip.category.name }}</div>
                    <h3 class="trip-title">{{ trip.name }}</h3>
                    <p class="trip-description">{{ trip.shortDescription }}</p>
                    <div class="trip-meta">
                      <span class="duration">{{ trip.duration }} days</span>
                      <span class="difficulty">{{ getDifficultyText(trip.difficulty) }}</span>
                    </div>
                    <div class="trip-footer">
                      <div class="trip-price">
                        @if (trip.hasDiscount) {
                          <span class="original-price">\${{ trip.price | number:'1.0-0' }}</span>
                        }
                        <span class="current-price">\${{ trip.effectivePrice | number:'1.0-0' }}</span>
                      </div>
                      <button class="btn btn-primary btn-sm">View Details</button>
                    </div>
                  </div>
                </div>
              }
            </div>
          }

          <div class="section-footer">
            <a routerLink="/trips" class="btn btn-outline btn-lg">View All Trips</a>
          </div>
        </div>
      </section>

      <!-- Recent Blogs -->
      <section class="recent-blogs">
        <div class="container">
          <div class="section-header" appRevealOnScroll>
            <h2>Travel Stories</h2>
            <p>Get inspired by amazing travel experiences</p>
          </div>

          @if (recentBlogs().length > 0) {
            <div class="blogs-grid" appStagger="slideInUp" [staggerDelay]="100" staggerSelector=".blog-card">
              @for (blog of recentBlogs(); track blog.id) {
                <article class="blog-card stagger-item" [routerLink]="['/blogs', blog.id]">
                  <div class="blog-image">
                    <img [src]="blog.featuredImageUrl" [alt]="blog.title" />
                  </div>
                  <div class="blog-content">
                    <div class="blog-meta">
                      <span class="blog-category">{{ blog.category.name }}</span>
                      <span class="blog-date">{{ blog.publishedAt | date:'mediumDate' }}</span>
                    </div>
                    <h3 class="blog-title">{{ blog.title }}</h3>
                    <p class="blog-excerpt">{{ blog.excerpt }}</p>
                    <div class="blog-footer">
                      <div class="blog-author">
                        <span>By {{ blog.author.name }}</span>
                      </div>
                      <div class="blog-read-time">
                        {{ blog.readTime }} min read
                      </div>
                    </div>
                  </div>
                </article>
              }
            </div>
          }

          <div class="section-footer">
            <a routerLink="/blogs" class="btn btn-outline btn-lg">Read More Stories</a>
          </div>
        </div>
      </section>
    </div>
  `
})
export class HomeComponent implements OnInit {
  private readonly tripService = inject(TripService);
  private readonly blogService = inject(BlogService);
  private readonly stateService = inject(StateService);
  private readonly router = inject(Router);

  // Signals
  featuredTrips = signal<Trip[]>([]);
  recentBlogs = signal<Blog[]>([]);
  isLoading = signal<boolean>(false);
  searchQuery = signal<string>('');

  ngOnInit(): void {
    this.loadFeaturedTrips();
    this.loadRecentBlogs();
  }

  private async loadFeaturedTrips(): Promise<void> {
    this.isLoading.set(true);

    try {
      const trips = await this.tripService.getFeaturedTrips().toPromise();
      this.featuredTrips.set(trips || []);
    } catch (error) {
      console.error('Error loading featured trips:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  private async loadRecentBlogs(): Promise<void> {
    try {
      const blogs = await this.blogService.getRecentBlogs(3).toPromise();
      this.recentBlogs.set(blogs || []);
    } catch (error) {
      console.error('Error loading recent blogs:', error);
    }
  }

  onSearch(): void {
    const query = this.searchQuery().trim();
    if (query) {
      this.router.navigate(['/trips'], { queryParams: { search: query } });
    } else {
      this.router.navigate(['/trips']);
    }
  }

  getDifficultyText(difficulty: number): string {
    const difficultyMap = { 1: 'Easy', 2: 'Moderate', 3: 'Hard' };
    return difficultyMap[difficulty as keyof typeof difficultyMap] || 'Unknown';
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(price);
  }

  getDiscountPercentage(originalPrice: number, discountPrice: number): number {
    return Math.round(((originalPrice - discountPrice) / originalPrice) * 100);
  }

  truncateText(text: string, maxLength: number): string {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  viewTripDetail(tripId: number): void {
    this.router.navigate(['/trips', tripId]);
  }

  viewBlogDetail(blogId: number): void {
    this.router.navigate(['/blogs', blogId]);
  }

  viewAllTrips(): void {
    this.router.navigate(['/trips']);
  }

  viewAllBlogs(): void {
    this.router.navigate(['/blogs']);
  }
}
```
