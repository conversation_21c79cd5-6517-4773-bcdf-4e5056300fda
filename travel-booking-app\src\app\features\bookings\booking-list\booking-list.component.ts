import { Component, inject, OnInit, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { BookingService, AuthService } from '../../../core/services';
import { Booking, BookingFilterDto, PaginationParameters } from '../../../core/models';

@Component({
  selector: 'app-booking-list',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <div class="booking-list-container">
      <div class="container">
        <div class="page-header">
          <h1>My Bookings</h1>
          <p>Manage your travel bookings and view trip details</p>
        </div>

        <!-- Filters -->
        <div class="filters-section">
          <div class="filters-row">
            <div class="filter-group">
              <label>Status</label>
              <select [(ngModel)]="selectedStatus" (change)="onFilterChange()">
                <option value="">All Bookings</option>
                <option value="1">Pending</option>
                <option value="2">Confirmed</option>
                <option value="3">Cancelled</option>
                <option value="4">Completed</option>
              </select>
            </div>

            <div class="filter-group">
              <label>Sort By</label>
              <select [(ngModel)]="sortBy" (change)="onFilterChange()">
                <option value="createdAt">Booking Date</option>
                <option value="travelDate">Travel Date</option>
                <option value="totalAmount">Amount</option>
              </select>
            </div>

            <div class="filter-group">
              <label>Order</label>
              <select [(ngModel)]="sortOrder" (change)="onFilterChange()">
                <option value="desc">Newest First</option>
                <option value="asc">Oldest First</option>
              </select>
            </div>
          </div>

          <div class="results-info">
            <span>{{ totalResults() }} bookings found</span>
          </div>
        </div>

        <!-- Bookings List -->
        <div class="bookings-section">
          @if (isLoading()) {
            <div class="loading-grid">
              @for (item of [1,2,3]; track item) {
                <div class="booking-card-skeleton"></div>
              }
            </div>
          } @else if (bookings().length === 0) {
            <div class="empty-state">
              <div class="empty-icon">🎒</div>
              <h3>No bookings found</h3>
              <p>You haven't made any bookings yet. Start exploring amazing destinations!</p>
              <a routerLink="/trips" class="btn btn-primary">Browse Trips</a>
            </div>
          } @else {
            <div class="bookings-grid">
              @for (booking of bookings(); track booking.id) {
                <div class="booking-card">
                  <div class="booking-image">
                    <img [src]="booking.trip.mainImageUrl" [alt]="booking.trip.name" />
                    <div class="booking-status" [class]="'status-' + booking.status">
                      {{ getStatusText(booking.status) }}
                    </div>
                  </div>

                  <div class="booking-content">
                    <div class="booking-header">
                      <h3>{{ booking.trip.name }}</h3>
                      <div class="booking-id">Booking #{{ booking.bookingNumber }}</div>
                    </div>

                    <div class="booking-details">
                      <div class="detail-item">
                        <span class="label">Travel Date:</span>
                        <span class="value">{{ booking.travelDate | date:'mediumDate' }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">Duration:</span>
                        <span class="value">{{ booking.trip.duration }} days</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">Travelers:</span>
                        <span class="value">{{ booking.totalPeople }} {{ booking.totalPeople === 1 ? 'person' : 'people' }}</span>
                      </div>
                      <div class="detail-item">
                        <span class="label">Booked on:</span>
                        <span class="value">{{ booking.bookingDate | date:'shortDate' }}</span>
                      </div>
                    </div>

                    <div class="booking-footer">
                      <div class="booking-price">
                        <span class="amount">\${{ booking.totalAmount | number:'1.2-2' }}</span>
                        <span class="currency">USD</span>
                      </div>

                      <div class="booking-actions">
                        <a [routerLink]="['/bookings', booking.id]" class="btn btn-outline btn-sm">
                          View Details
                        </a>
                        @if (booking.status === 2 && canCancelBooking(booking)) {
                          <button class="btn btn-danger btn-sm" (click)="cancelBooking(booking.id)">
                            Cancel
                          </button>
                        }
                      </div>
                    </div>
                  </div>
                </div>
              }
            </div>

            <!-- Pagination -->
            @if (totalPages() > 1) {
              <div class="pagination">
                <button
                  class="btn btn-outline"
                  [disabled]="currentPage() === 1"
                  (click)="goToPage(currentPage() - 1)"
                >
                  Previous
                </button>

                @for (page of getPageNumbers(); track page) {
                  <button
                    class="btn"
                    [class.btn-primary]="page === currentPage()"
                    [class.btn-outline]="page !== currentPage()"
                    (click)="goToPage(page)"
                  >
                    {{ page }}
                  </button>
                }

                <button
                  class="btn btn-outline"
                  [disabled]="currentPage() === totalPages()"
                  (click)="goToPage(currentPage() + 1)"
                >
                  Next
                </button>
              </div>
            }
          }
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./booking-list.component.scss']
})
export class BookingListComponent implements OnInit {
  private readonly bookingService = inject(BookingService);
  private readonly authService = inject(AuthService);

  // Filter signals
  selectedStatus = signal<string>('');
  sortBy = signal<string>('createdAt');
  sortOrder = signal<string>('desc');

  // Data signals
  bookings = signal<Booking[]>([]);
  isLoading = signal<boolean>(false);
  currentPage = signal<number>(1);
  totalResults = signal<number>(0);
  totalPages = computed(() => Math.ceil(this.totalResults() / 10));

  ngOnInit(): void {
    this.loadBookings();
  }

  private async loadBookings(): Promise<void> {
    this.isLoading.set(true);

    try {
      const filters = this.buildFilters();
      const pagination: PaginationParameters = {
        page: this.currentPage(),
        pageSize: 10,
        sortBy: this.sortBy(),
        sortOrder: this.sortOrder() as 'asc' | 'desc'
      };

      const result = await this.bookingService.getUserBookings(pagination, filters).toPromise();

      if (result) {
        this.bookings.set(result.items);
        this.totalResults.set(result.totalCount);
      }
    } catch (error) {
      console.error('Error loading bookings:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  private buildFilters(): BookingFilterDto {
    const filters: BookingFilterDto = {};

    if (this.selectedStatus()) {
      filters.status = Number(this.selectedStatus());
    }

    return filters;
  }

  onFilterChange(): void {
    this.currentPage.set(1);
    this.loadBookings();
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
      this.loadBookings();
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  getPageNumbers(): number[] {
    const total = this.totalPages();
    const current = this.currentPage();
    const pages: number[] = [];

    if (total <= 7) {
      for (let i = 1; i <= total; i++) {
        pages.push(i);
      }
    } else {
      if (current <= 4) {
        for (let i = 1; i <= 5; i++) pages.push(i);
        pages.push(-1);
        pages.push(total);
      } else if (current >= total - 3) {
        pages.push(1);
        pages.push(-1);
        for (let i = total - 4; i <= total; i++) pages.push(i);
      } else {
        pages.push(1);
        pages.push(-1);
        for (let i = current - 1; i <= current + 1; i++) pages.push(i);
        pages.push(-1);
        pages.push(total);
      }
    }

    return pages;
  }

  getStatusText(status: number): string {
    const statusMap = {
      1: 'Pending',
      2: 'Confirmed',
      3: 'Cancelled',
      4: 'Completed'
    };
    return statusMap[status as keyof typeof statusMap] || 'Unknown';
  }

  canCancelBooking(booking: Booking): boolean {
    const travelDate = new Date(booking.travelDate);
    const now = new Date();
    const daysDifference = Math.ceil((travelDate.getTime() - now.getTime()) / (1000 * 3600 * 24));

    // Can cancel if travel date is more than 7 days away
    return daysDifference > 7;
  }

  async cancelBooking(bookingId: number): Promise<void> {
    if (confirm('Are you sure you want to cancel this booking? This action cannot be undone.')) {
      try {
        await this.bookingService.cancelBooking(bookingId).toPromise();
        this.loadBookings(); // Reload the list
      } catch (error) {
        console.error('Error cancelling booking:', error);
      }
    }
  }
}
