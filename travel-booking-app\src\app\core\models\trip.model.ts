export interface Trip {
  id: number;
  name: string;
  shortDescription: string;
  description?: string;
  price: number;
  discountPrice?: number;
  duration: number;
  maxCapacity: number;
  minAge?: number;
  maxAge?: number;
  difficulty: TripDifficulty;
  mainImageUrl: string;
  isFeatured: boolean;
  isActive?: boolean;
  availableFrom: Date;
  availableTo: Date;
  category: TripCategory;
  destinationCity: City;
  departureCity: City;
  images: TripImage[];
  itinerary?: TripItinerary[];
  includesAccommodation?: boolean;
  includesTransport?: boolean;
  includesMeals?: boolean;
  includesGuide?: boolean;
  createdAt?: Date;
  updatedAt?: Date;

  // Computed properties from API
  effectivePrice: number;
  hasDiscount: boolean;
  discountPercentage: number;
}

export interface TripDetail extends Trip {
  description: string;
  includesAccommodation: boolean;
  includesTransport: boolean;
  includesMeals: boolean;
  includesGuide: boolean;
  itineraries: TripItinerary[];
  createdAt: Date;
  updatedAt?: Date;
}

export interface TripCategory {
  id: number;
  name: string;
  description?: string;
  iconUrl?: string;
  isActive: boolean;
}

export interface TripImage {
  id: number;
  tripId: number;
  imageUrl: string;
  caption?: string;
  isMain: boolean;
  displayOrder: number;
}

export interface TripItinerary {
  id: number;
  tripId: number;
  day: number;
  title: string;
  description: string;
  activities?: string;
  meals?: string;
  accommodation?: string;
}

export interface City {
  id: number;
  name: string;
  country: Country;
  stateProvince?: string;
  latitude?: number;
  longitude?: number;
  timeZone?: string;
  isPopular: boolean;
  isActive: boolean;
}

export interface Country {
  id: number;
  name: string;
  code: string;
  currency: string;
  currencyCode: string;
  currencySymbol: string;
  flagUrl?: string;
  isActive: boolean;
}

export interface CreateTripDto {
  name: string;
  description: string;
  shortDescription: string;
  price: number;
  discountPrice?: number;
  duration: number;
  maxCapacity: number;
  minAge?: number;
  maxAge?: number;
  difficulty: TripDifficulty;
  categoryId: number;
  destinationCityId: number;
  departureCityId: number;
  includesAccommodation: boolean;
  includesTransport: boolean;
  includesMeals: boolean;
  includesGuide: boolean;
  mainImageUrl: string;
  availableFrom: Date;
  availableTo: Date;
}

export interface UpdateTripDto extends CreateTripDto {
  id: number;
}

export interface TripFilterDto {
  search?: string;
  searchTerm?: string;
  categoryId?: number;
  destinationCityId?: number;
  minPrice?: number;
  maxPrice?: number;
  duration?: number;
  difficulty?: TripDifficulty;
  availableFrom?: Date;
  availableTo?: Date;
  isFeatured?: boolean;
  isActive?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export enum TripDifficulty {
  Easy = 1,
  Moderate = 2,
  Hard = 3
}

export enum TripStatus {
  Draft = 1,
  Active = 2,
  Inactive = 3,
  Archived = 4
}
