.trip-detail-container {
  min-height: 100vh;
}

// Hero Section
.trip-hero {
  position: relative;
  height: 60vh;
  min-height: 400px;
  overflow: hidden;

  .hero-image {
    position: relative;
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .hero-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0.7));
      display: flex;
      align-items: flex-end;
      padding-bottom: 60px;

      .hero-content {
        color: white;

        h1 {
          font-size: 3rem;
          font-weight: 700;
          margin-bottom: 20px;
          text-shadow: 2px 2px 4px rgba(0,0,0,0.5);

          @media (max-width: 768px) {
            font-size: 2rem;
          }
        }

        .trip-meta {
          display: flex;
          gap: 20px;
          flex-wrap: wrap;

          span {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// Main Content
.trip-content {
  padding: 60px 0;

  .content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 40px;
    }
  }
}

// Trip Details
.trip-details {
  .description-section,
  .itinerary-section,
  .included-section,
  .gallery-section {
    margin-bottom: 50px;

    h2 {
      font-size: 1.8rem;
      font-weight: 600;
      margin-bottom: 25px;
      color: #2d3748;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
    }

    p {
      line-height: 1.7;
      color: #4a5568;
      font-size: 1.1rem;
    }
  }

  .itinerary-list {
    .itinerary-day {
      display: flex;
      margin-bottom: 30px;
      padding-bottom: 30px;
      border-bottom: 1px solid #e2e8f0;

      &:last-child {
        border-bottom: none;
      }

      .day-number {
        flex-shrink: 0;
        width: 80px;
        height: 80px;
        background: #667eea;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 25px;
      }

      .day-content {
        flex: 1;

        h3 {
          font-size: 1.3rem;
          font-weight: 600;
          margin-bottom: 10px;
          color: #2d3748;
        }

        p {
          margin-bottom: 15px;
        }

        .day-activities,
        .day-meals,
        .day-accommodation {
          margin-bottom: 8px;
          font-size: 0.95rem;
          color: #4a5568;

          strong {
            color: #2d3748;
          }
        }
      }
    }
  }

  .included-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;

    .included-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 15px;
      border-radius: 10px;
      background: #f7fafc;
      opacity: 0.5;
      transition: all 0.3s ease;

      &.included {
        opacity: 1;
        background: #e6fffa;
        border: 1px solid #38a169;

        .icon {
          filter: grayscale(0);
        }
      }

      .icon {
        font-size: 1.5rem;
        filter: grayscale(1);
      }

      span:last-child {
        font-weight: 500;
        color: #2d3748;
      }
    }
  }

  .image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;

    .gallery-item {
      border-radius: 10px;
      overflow: hidden;
      aspect-ratio: 4/3;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}

// Booking Sidebar
.booking-sidebar {
  .booking-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 20px;

    .price-section {
      text-align: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #e2e8f0;

      .original-price {
        text-decoration: line-through;
        color: #a0aec0;
        font-size: 1.1rem;
        margin-bottom: 5px;
      }

      .current-price {
        font-size: 2.5rem;
        font-weight: 700;
        color: #38a169;
        margin-bottom: 5px;
      }

      .discount-badge {
        background: #e53e3e;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-block;
        margin-bottom: 10px;
      }

      .price-note {
        color: #718096;
        font-size: 0.9rem;
      }
    }

    .trip-info {
      margin-bottom: 30px;

      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f7fafc;

        .label {
          color: #718096;
          font-weight: 500;
        }

        .value {
          color: #2d3748;
          font-weight: 600;
        }
      }
    }

    .booking-actions {
      margin-bottom: 20px;

      .btn-large {
        width: 100%;
        padding: 15px;
        font-size: 1.1rem;
        margin-bottom: 15px;
      }
    }

    .contact-info {
      text-align: center;
      font-size: 0.9rem;
      color: #718096;

      a {
        color: #667eea;
        text-decoration: none;
        font-weight: 600;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// Error State
.error-state {
  text-align: center;
  padding: 100px 20px;
  color: #718096;

  h2 {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #4a5568;
  }

  p {
    margin-bottom: 30px;
    font-size: 1.1rem;
  }
}

// Loading Skeleton
.loading-skeleton {
  .skeleton-header {
    height: 60vh;
    background: #f7fafc;
    animation: pulse 1.5s ease-in-out infinite alternate;
  }

  .skeleton-content {
    padding: 60px 20px;
    max-width: 1200px;
    margin: 0 auto;

    &::before {
      content: '';
      display: block;
      height: 400px;
      background: #f7fafc;
      border-radius: 10px;
      animation: pulse 1.5s ease-in-out infinite alternate;
    }
  }
}

// Common Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  text-align: center;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover {
      background: #5a67d8;
      transform: translateY(-2px);
    }
  }

  &.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;

    &:hover {
      background: #667eea;
      color: white;
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  100% { opacity: 0.4; }
}
