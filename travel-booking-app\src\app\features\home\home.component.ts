import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { TripService, BlogService, StateService } from '../../core/services';
import { Trip, Blog } from '../../core/models';
import {
  AnimateDirective,
  StaggerDirective,
  TiltDirective,
  RevealOnScrollDirective
} from '../../shared/directives/animate.directive';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    AnimateDirective,
    StaggerDirective,
    TiltDirective,
    RevealOnScrollDirective
  ],
  template: `
    <div class="home-container">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-title" appAnimate="fadeIn" [animationDelay]="200">Discover Your Next Adventure</h1>
            <p class="hero-subtitle" appAnimate="fadeIn" [animationDelay]="400">
              Explore breathtaking destinations, create unforgettable memories,
              and embark on journeys that will change your perspective forever.
            </p>
            <div class="hero-actions" appAnimate="fadeIn" [animationDelay]="600">
              <button class="btn btn-primary" routerLink="/trips">
                Explore Trips
              </button>
              <button class="btn btn-outline" routerLink="/blogs">
                Travel Stories
              </button>
            </div>
          </div>
          <div class="hero-image" appAnimate="slideInRight" [animationDelay]="300">
            <img src="/assets/images/hero-travel.jpg" alt="Travel Adventure" />
          </div>
        </div>
      </section>

      <!-- Featured Trips Section -->
      <section class="featured-trips">
        <div class="container">
          <div class="section-header" appRevealOnScroll>
            <h2>Featured Destinations</h2>
            <p>Handpicked adventures for the ultimate travel experience</p>
          </div>
          
          @if (isLoading()) {
            <div class="loading-skeleton">
              @for (item of [1,2,3,4]; track item) {
                <div class="trip-card-skeleton"></div>
              }
            </div>
          } @else {
            <div class="trips-grid" appStagger="scaleIn" [staggerDelay]="150" staggerSelector=".trip-card">
              @for (trip of featuredTrips(); track trip.id) {
                <div class="trip-card stagger-item" [routerLink]="['/trips', trip.id]" appTilt>
                  <div class="trip-image">
                    <img [src]="trip.mainImageUrl" [alt]="trip.name" />
                    @if (trip.discountPrice) {
                      <div class="discount-badge">
                        {{ calculateDiscount(trip.price, trip.discountPrice) }}% OFF
                      </div>
                    }
                  </div>
                  <div class="trip-content">
                    <h3>{{ trip.name }}</h3>
                    <p>{{ trip.shortDescription }}</p>
                    <div class="trip-meta">
                      <span class="duration">{{ trip.duration }} days</span>
                      <span class="difficulty">{{ getDifficultyText(trip.difficulty) }}</span>
                    </div>
                    <div class="trip-price">
                      @if (trip.discountPrice) {
                        <span class="original-price">\${{ trip.price }}</span>
                        <span class="current-price">\${{ trip.discountPrice }}</span>
                      } @else {
                        <span class="current-price">\${{ trip.price }}</span>
                      }
                    </div>
                  </div>
                </div>
              }
            </div>
          }
          
          <div class="section-footer">
            <button class="btn btn-outline" routerLink="/trips">
              View All Trips
            </button>
          </div>
        </div>
      </section>

      <!-- Featured Blogs Section -->
      <section class="featured-blogs">
        <div class="container">
          <div class="section-header">
            <h2>Travel Stories</h2>
            <p>Get inspired by real travel experiences and expert tips</p>
          </div>
          
          @if (isLoading()) {
            <div class="loading-skeleton">
              @for (item of [1,2,3]; track item) {
                <div class="blog-card-skeleton"></div>
              }
            </div>
          } @else {
            <div class="blogs-grid">
              @for (blog of featuredBlogs(); track blog.id) {
                <article class="blog-card" [routerLink]="['/blogs', blog.slug]">
                  <div class="blog-image">
                    <img [src]="blog.featuredImageUrl" [alt]="blog.title" />
                  </div>
                  <div class="blog-content">
                    <div class="blog-meta">
                      <span class="category">{{ blog.category.name }}</span>
                      <span class="reading-time">{{ blog.readingTime }} min read</span>
                    </div>
                    <h3>{{ blog.title }}</h3>
                    <p>{{ blog.excerpt }}</p>
                    <div class="blog-author">
                      <span>By {{ blog.author.firstName }} {{ blog.author.lastName }}</span>
                    </div>
                  </div>
                </article>
              }
            </div>
          }
          
          <div class="section-footer">
            <button class="btn btn-outline" routerLink="/blogs">
              Read More Stories
            </button>
          </div>
        </div>
      </section>

      <!-- Newsletter Section -->
      <section class="newsletter-section">
        <div class="container">
          <div class="newsletter-content">
            <h2>Stay Updated</h2>
            <p>Get the latest travel deals and destination guides delivered to your inbox</p>
            <form class="newsletter-form" (ngSubmit)="subscribeNewsletter()">
              <input 
                type="email" 
                placeholder="Enter your email address"
                [(ngModel)]="emailAddress"
                required
              />
              <button type="submit" class="btn btn-primary">Subscribe</button>
            </form>
          </div>
        </div>
      </section>
    </div>
  `,
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {
  private readonly tripService = inject(TripService);
  private readonly blogService = inject(BlogService);
  private readonly stateService = inject(StateService);

  // Signals for reactive state
  featuredTrips = signal<Trip[]>([]);
  featuredBlogs = signal<Blog[]>([]);
  isLoading = signal<boolean>(false);
  emailAddress = signal<string>('');

  ngOnInit(): void {
    this.loadFeaturedContent();
  }

  private async loadFeaturedContent(): Promise<void> {
    this.isLoading.set(true);
    
    try {
      // Load featured trips and blogs in parallel
      const [trips, blogs] = await Promise.all([
        this.tripService.getFeaturedTrips().toPromise(),
        this.blogService.getFeaturedBlogs().toPromise()
      ]);

      this.featuredTrips.set(trips || []);
      this.featuredBlogs.set(blogs || []);
    } catch (error) {
      console.error('Error loading featured content:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  calculateDiscount(originalPrice: number, discountPrice: number): number {
    return Math.round(((originalPrice - discountPrice) / originalPrice) * 100);
  }

  getDifficultyText(difficulty: number): string {
    const difficultyMap = {
      1: 'Easy',
      2: 'Moderate', 
      3: 'Hard'
    };
    return difficultyMap[difficulty as keyof typeof difficultyMap] || 'Unknown';
  }

  subscribeNewsletter(): void {
    const email = this.emailAddress();
    if (email) {
      // TODO: Implement newsletter subscription
      console.log('Subscribing email:', email);
      this.emailAddress.set('');
    }
  }
}
