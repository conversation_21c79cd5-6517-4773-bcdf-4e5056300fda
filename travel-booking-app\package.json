{"name": "travel-booking-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:travel-booking-app": "node dist/travel-booking-app/server/server.mjs"}, "private": true, "dependencies": {"@angular/animations": "^20.0.6", "@angular/cdk": "^20.0.5", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/material": "^20.0.5", "@angular/platform-browser": "^20.0.0", "@angular/platform-server": "^20.0.0", "@angular/router": "^20.0.0", "@angular/service-worker": "^20.0.0", "@angular/ssr": "^20.0.2", "chart.js": "^4.5.0", "express": "^5.1.0", "ng2-charts": "^8.0.0", "ngx-pagination": "^6.0.3", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.0.2", "@angular/cli": "^20.0.2", "@angular/compiler-cli": "^20.0.0", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}