import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService, BookingService, TripService, StateService } from '../../core/services';
import { Booking, Trip } from '../../core/models';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="dashboard-container">
      <div class="container">
        <div class="dashboard-header">
          <h1>Welcome back, {{ currentUser()?.firstName }}!</h1>
          <p>Manage your trips, bookings, and profile</p>
        </div>

        <div class="dashboard-grid">
          <div class="dashboard-card">
            <h3>My Bookings</h3>
            <p>View and manage your trip bookings</p>
            <a routerLink="/bookings" class="btn btn-primary">View Bookings</a>
          </div>

          <div class="dashboard-card">
            <h3>Browse Trips</h3>
            <p>Discover new destinations and adventures</p>
            <a routerLink="/trips" class="btn btn-primary">Explore Trips</a>
          </div>

          <div class="dashboard-card">
            <h3>My Profile</h3>
            <p>Update your personal information</p>
            <a routerLink="/profile" class="btn btn-primary">Edit Profile</a>
          </div>

          <div class="dashboard-card">
            <h3>Travel Stories</h3>
            <p>Read inspiring travel blogs and guides</p>
            <a routerLink="/blogs" class="btn btn-primary">Read Blogs</a>
          </div>
        </div>

        @if (currentUser()?.role === 2) {
          <div class="admin-section">
            <h2>Admin Panel</h2>
            <div class="admin-grid">
              <div class="dashboard-card admin-card">
                <h3>Admin Dashboard</h3>
                <p>View analytics and manage the platform</p>
                <a routerLink="/admin" class="btn btn-secondary">Go to Admin</a>
              </div>
            </div>
          </div>
        }
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      min-height: 100vh;
      background: #f8f9fa;
      padding: 40px 0;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    .dashboard-header {
      text-align: center;
      margin-bottom: 50px;
    }
    .dashboard-header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2d3748;
      margin-bottom: 10px;
    }
    .dashboard-header p {
      color: #718096;
      font-size: 1.1rem;
    }
    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
      margin-bottom: 50px;
    }
    .dashboard-card {
      background: white;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      text-align: center;
      transition: transform 0.3s ease;
    }
    .dashboard-card:hover {
      transform: translateY(-5px);
    }
    .dashboard-card h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 10px;
    }
    .dashboard-card p {
      color: #718096;
      margin-bottom: 20px;
    }
    .admin-section {
      border-top: 1px solid #e2e8f0;
      padding-top: 40px;
    }
    .admin-section h2 {
      text-align: center;
      font-size: 2rem;
      color: #2d3748;
      margin-bottom: 30px;
    }
    .admin-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
    }
    .admin-card {
      border: 2px solid #667eea;
    }
    .btn {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
    }
    .btn-primary {
      background: #667eea;
      color: white;
    }
    .btn-primary:hover {
      background: #5a67d8;
    }
    .btn-secondary {
      background: #4a5568;
      color: white;
    }
    .btn-secondary:hover {
      background: #2d3748;
    }
  `]
})
export class DashboardComponent implements OnInit {
  private readonly authService = inject(AuthService);
  private readonly bookingService = inject(BookingService);
  private readonly tripService = inject(TripService);
  private readonly stateService = inject(StateService);

  // Signals
  currentUser = this.authService.currentUser;
  recentBookings = signal<Booking[]>([]);
  savedTrips = signal<Trip[]>([]);
  isLoading = signal<boolean>(false);
  dashboardStats = signal<any>({
    totalBookings: 0,
    upcomingTrips: 0,
    savedTrips: 0,
    totalSpent: 0
  });

  ngOnInit(): void {
    this.loadDashboardData();
  }

  private async loadDashboardData(): Promise<void> {
    this.isLoading.set(true);

    try {
      // Load recent bookings
      const bookingsResult = await this.bookingService.getUserBookings(
        { page: 1, pageSize: 5 }
      ).toPromise();

      if (bookingsResult) {
        this.recentBookings.set(bookingsResult.items);
        this.updateStats(bookingsResult.items);
      }

      // Load saved trips (placeholder - would need wishlist API)
      // const savedTrips = await this.tripService.getSavedTrips().toPromise();
      // this.savedTrips.set(savedTrips || []);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  private updateStats(bookings: Booking[]): void {
    const stats = {
      totalBookings: bookings.length,
      upcomingTrips: bookings.filter(b => new Date(b.travelDate) > new Date()).length,
      savedTrips: this.savedTrips().length,
      totalSpent: bookings.reduce((sum, b) => sum + b.totalAmount, 0)
    };
    this.dashboardStats.set(stats);
  }

  getStatusText(status: number): string {
    const statusMap = {
      1: 'Pending',
      2: 'Confirmed',
      3: 'Cancelled',
      4: 'Completed'
    };
    return statusMap[status as keyof typeof statusMap] || 'Unknown';
  }

  logout(): void {
    this.authService.logout();
  }
}
