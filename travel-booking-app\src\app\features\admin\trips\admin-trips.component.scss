.admin-trips-container {
  min-height: 100vh;
  background: #f1f5f9;
  padding: 40px 0;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

// Page Header
.page-header {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 20px;
    align-items: flex-start;
  }

  .header-content {
    h1 {
      font-size: 2rem;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 8px;
    }

    p {
      color: #64748b;
      font-size: 1.1rem;
      margin: 0;
    }
  }

  .header-actions {
    display: flex;
    gap: 15px;

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
    }
  }
}

// Filters Section
.filters-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  .search-bar {
    margin-bottom: 25px;

    .search-input {
      width: 100%;
      max-width: 400px;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.3s ease;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }

  .filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .filter-group {
    label {
      display: block;
      font-weight: 600;
      margin-bottom: 8px;
      color: #1e293b;
      font-size: 0.9rem;
    }

    select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      font-size: 0.9rem;
      background: white;
      transition: border-color 0.2s;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }

  .results-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
    color: #64748b;
    font-size: 0.9rem;
    font-weight: 500;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 15px;
      align-items: flex-start;
    }
  }
}

// Trips Section
.trips-section {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100px 20px;

    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e2e8f0;
      border-top: 4px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    p {
      color: #64748b;
      font-size: 1.1rem;
    }
  }

  .empty-state {
    text-align: center;
    padding: 100px 20px;

    .empty-icon {
      font-size: 4rem;
      margin-bottom: 20px;
    }

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 10px;
    }

    p {
      color: #64748b;
      margin-bottom: 30px;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
    }
  }

  .trips-table-container {
    overflow-x: auto;
  }

  .trips-table {
    width: 100%;
    border-collapse: collapse;

    th {
      background: #f8fafc;
      padding: 15px 20px;
      text-align: left;
      font-weight: 600;
      color: #1e293b;
      border-bottom: 1px solid #e2e8f0;
      font-size: 0.9rem;
    }

    td {
      padding: 15px 20px;
      border-bottom: 1px solid #e2e8f0;
      vertical-align: middle;
    }

    tr:hover {
      background: #f8fafc;
    }

    .trip-info {
      display: flex;
      align-items: center;
      gap: 15px;
      min-width: 250px;

      .trip-image {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .trip-details {
        .trip-name {
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 4px;
        }

        .trip-location {
          font-size: 0.8rem;
          color: #64748b;
        }
      }
    }

    .category-badge {
      background: #dbeafe;
      color: #1e40af;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .price {
      font-weight: 600;
      color: #059669;
      font-size: 1rem;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 600;

      &.active {
        background: #d1fae5;
        color: #065f46;
      }

      &.inactive {
        background: #fee2e2;
        color: #991b1b;
      }
    }

    .actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
      min-width: 200px;
    }
  }
}

// Pagination
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 30px;
  background: white;
  border-radius: 0 0 15px 15px;

  .btn {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .pagination-ellipsis {
    padding: 0 8px;
    color: #64748b;
  }
}

// Button Styles
.btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  text-align: center;

  &.btn-primary {
    background: #3b82f6;
    color: white;

    &:hover:not(:disabled) {
      background: #2563eb;
      transform: translateY(-1px);
    }
  }

  &.btn-outline {
    background: transparent;
    color: #3b82f6;
    border: 1px solid #3b82f6;

    &:hover:not(:disabled) {
      background: #3b82f6;
      color: white;
    }
  }

  &.btn-secondary {
    background: #6b7280;
    color: white;

    &:hover:not(:disabled) {
      background: #4b5563;
    }
  }

  &.btn-success {
    background: #059669;
    color: white;

    &:hover:not(:disabled) {
      background: #047857;
    }
  }

  &.btn-danger {
    background: #dc2626;
    color: white;

    &:hover:not(:disabled) {
      background: #b91c1c;
    }
  }

  &.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
