{"Version": 1, "WorkspaceRootPath": "F:\\torism and travel\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2AA79860-2F8B-4B02-A977-240E443D7FF7}|TravelTourism.API\\TravelTourism.API.csproj|f:\\torism and travel\\traveltourism.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{2AA79860-2F8B-4B02-A977-240E443D7FF7}|TravelTourism.API\\TravelTourism.API.csproj|solutionrelative:traveltourism.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{2AA79860-2F8B-4B02-A977-240E443D7FF7}|TravelTourism.API\\TravelTourism.API.csproj|f:\\torism and travel\\traveltourism.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{2AA79860-2F8B-4B02-A977-240E443D7FF7}|TravelTourism.API\\TravelTourism.API.csproj|solutionrelative:traveltourism.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{2AA79860-2F8B-4B02-A977-240E443D7FF7}|TravelTourism.API\\TravelTourism.API.csproj|f:\\torism and travel\\traveltourism.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AA79860-2F8B-4B02-A977-240E443D7FF7}|TravelTourism.API\\TravelTourism.API.csproj|solutionrelative:traveltourism.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "launchSettings.json", "DocumentMoniker": "F:\\torism and travel\\TravelTourism.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "TravelTourism.API\\Properties\\launchSettings.json", "ToolTip": "F:\\torism and travel\\TravelTourism.API\\Properties\\launchSettings.json", "RelativeToolTip": "TravelTourism.API\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABoAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-06T08:30:39.046Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "F:\\torism and travel\\TravelTourism.API\\appsettings.json", "RelativeDocumentMoniker": "TravelTourism.API\\appsettings.json", "ToolTip": "F:\\torism and travel\\TravelTourism.API\\appsettings.json", "RelativeToolTip": "TravelTourism.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-06T08:30:27.369Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "F:\\torism and travel\\TravelTourism.API\\Program.cs", "RelativeDocumentMoniker": "TravelTourism.API\\Program.cs", "ToolTip": "F:\\torism and travel\\TravelTourism.API\\Program.cs", "RelativeToolTip": "TravelTourism.API\\Program.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-06T08:30:21.689Z"}]}]}]}