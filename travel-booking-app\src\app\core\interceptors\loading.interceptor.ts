import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { finalize } from 'rxjs';
import { StateService } from '../services/state.service';

export const loadingInterceptor: HttpInterceptorFn = (req, next) => {
  const stateService = inject(StateService);

  // Skip loading indicator for certain requests
  const skipLoading = 
    req.url.includes('/auth/refresh-token') ||
    req.url.includes('/heartbeat') ||
    req.url.includes('/health') ||
    req.headers.has('X-Skip-Loading');

  if (!skipLoading) {
    stateService.setLoading(true);
  }

  return next(req).pipe(
    finalize(() => {
      if (!skipLoading) {
        stateService.setLoading(false);
      }
    })
  );
};
