import { Injectable, inject } from '@angular/core';
import { Title, Meta } from '@angular/platform-browser';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';

// Google Analytics gtag function declaration
declare let gtag: Function;

export interface SEOData {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  siteName?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  locale?: string;
  alternateLocales?: string[];
}

export interface StructuredData {
  '@context': string;
  '@type': string;
  [key: string]: any;
}

@Injectable({
  providedIn: 'root'
})
export class SEOService {
  private readonly titleService = inject(Title);
  private readonly metaService = inject(Meta);
  private readonly router = inject(Router);

  private readonly defaultSEO: SEOData = {
    title: 'Travel Booking App - Discover Amazing Destinations',
    description: 'Explore breathtaking destinations and create unforgettable memories with our curated travel experiences. Book your next adventure today!',
    keywords: 'travel, booking, destinations, adventure, vacation, trips, tourism',
    image: '/assets/images/og-default.jpg',
    type: 'website',
    siteName: 'Travel Booking App',
    locale: 'en_US'
  };

  constructor() {
    this.initializeRouterTracking();
  }

  private initializeRouterTracking(): void {
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.updateCanonicalUrl(event.url);
        this.trackPageView(event.url);
      });
  }

  updateSEO(seoData: Partial<SEOData>): void {
    const data = { ...this.defaultSEO, ...seoData };

    // Update title
    if (data.title) {
      this.titleService.setTitle(data.title);
    }

    // Update meta tags
    this.updateMetaTags(data);

    // Update Open Graph tags
    this.updateOpenGraphTags(data);

    // Update Twitter Card tags
    this.updateTwitterCardTags(data);

    // Update JSON-LD structured data
    this.updateStructuredData(data);
  }

  private updateMetaTags(data: SEOData): void {
    // Basic meta tags
    this.updateOrCreateMetaTag('description', data.description);
    this.updateOrCreateMetaTag('keywords', data.keywords);
    this.updateOrCreateMetaTag('author', data.author);

    // Robots meta tag
    this.updateOrCreateMetaTag('robots', 'index, follow');

    // Viewport (for mobile)
    this.updateOrCreateMetaTag('viewport', 'width=device-width, initial-scale=1');

    // Language
    if (data.locale) {
      this.updateOrCreateMetaTag('language', data.locale.split('_')[0]);
    }

    // Publication dates
    if (data.publishedTime) {
      this.updateOrCreateMetaTag('article:published_time', data.publishedTime);
    }
    if (data.modifiedTime) {
      this.updateOrCreateMetaTag('article:modified_time', data.modifiedTime);
    }
  }

  private updateOpenGraphTags(data: SEOData): void {
    this.updateOrCreateMetaProperty('og:title', data.title);
    this.updateOrCreateMetaProperty('og:description', data.description);
    this.updateOrCreateMetaProperty('og:image', this.getAbsoluteUrl(data.image));
    this.updateOrCreateMetaProperty('og:url', this.getAbsoluteUrl(data.url));
    this.updateOrCreateMetaProperty('og:type', data.type);
    this.updateOrCreateMetaProperty('og:site_name', data.siteName);
    this.updateOrCreateMetaProperty('og:locale', data.locale);

    // Alternate locales
    if (data.alternateLocales) {
      data.alternateLocales.forEach(locale => {
        this.addMetaProperty('og:locale:alternate', locale);
      });
    }

    // Article specific
    if (data.type === 'article') {
      this.updateOrCreateMetaProperty('article:author', data.author);
      this.updateOrCreateMetaProperty('article:section', data.section);
      this.updateOrCreateMetaProperty('article:published_time', data.publishedTime);
      this.updateOrCreateMetaProperty('article:modified_time', data.modifiedTime);

      if (data.tags) {
        data.tags.forEach(tag => {
          this.addMetaProperty('article:tag', tag);
        });
      }
    }
  }

  private updateTwitterCardTags(data: SEOData): void {
    this.updateOrCreateMetaName('twitter:card', 'summary_large_image');
    this.updateOrCreateMetaName('twitter:title', data.title);
    this.updateOrCreateMetaName('twitter:description', data.description);
    this.updateOrCreateMetaName('twitter:image', this.getAbsoluteUrl(data.image));
    this.updateOrCreateMetaName('twitter:site', '@travelbookingapp'); // Replace with your Twitter handle
    this.updateOrCreateMetaName('twitter:creator', '@travelbookingapp');
  }

  private updateStructuredData(data: SEOData): void {
    // Website structured data
    const websiteStructuredData: StructuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      'name': data.siteName || 'Travel Booking App',
      'url': this.getAbsoluteUrl('/'),
      'description': data.description,
      'potentialAction': {
        '@type': 'SearchAction',
        'target': this.getAbsoluteUrl('/trips?search={search_term_string}'),
        'query-input': 'required name=search_term_string'
      }
    };

    this.addStructuredData('website', websiteStructuredData);

    // Organization structured data
    const organizationStructuredData: StructuredData = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      'name': 'Travel Booking App',
      'url': this.getAbsoluteUrl('/'),
      'logo': this.getAbsoluteUrl('/assets/images/logo.png'),
      'description': 'Your trusted partner for amazing travel experiences',
      'contactPoint': {
        '@type': 'ContactPoint',
        'telephone': '******-123-4567',
        'contactType': 'customer service',
        'availableLanguage': ['English']
      },
      'sameAs': [
        'https://facebook.com/travelbookingapp',
        'https://twitter.com/travelbookingapp',
        'https://instagram.com/travelbookingapp'
      ]
    };

    this.addStructuredData('organization', organizationStructuredData);
  }

  // Specific SEO methods for different page types
  updateHomePage(): void {
    this.updateSEO({
      title: 'Travel Booking App - Discover Amazing Destinations',
      description: 'Explore breathtaking destinations and create unforgettable memories with our curated travel experiences. Book your next adventure today!',
      type: 'website',
      url: '/'
    });
  }

  updateTripPage(trip: any): void {
    this.updateSEO({
      title: `${trip.name} - Travel Booking App`,
      description: trip.shortDescription || trip.description,
      keywords: `${trip.name}, travel, booking, ${trip.destinationCity?.name}, ${trip.category?.name}`,
      image: trip.mainImageUrl,
      type: 'article',
      url: `/trips/${trip.id}`,
      section: 'Travel',
      tags: [trip.category?.name, trip.destinationCity?.name, 'travel', 'booking']
    });

    // Trip-specific structured data
    const tripStructuredData: StructuredData = {
      '@context': 'https://schema.org',
      '@type': 'TouristTrip',
      'name': trip.name,
      'description': trip.description,
      'image': this.getAbsoluteUrl(trip.mainImageUrl),
      'url': this.getAbsoluteUrl(`/trips/${trip.id}`),
      'offers': {
        '@type': 'Offer',
        'price': trip.price,
        'priceCurrency': 'USD',
        'availability': 'https://schema.org/InStock'
      },
      'provider': {
        '@type': 'Organization',
        'name': 'Travel Booking App'
      },
      'duration': `P${trip.duration}D`,
      'touristType': this.getDifficultyText(trip.difficulty)
    };

    this.addStructuredData('trip', tripStructuredData);
  }

  updateBlogPage(blog: any): void {
    this.updateSEO({
      title: `${blog.title} - Travel Blog`,
      description: blog.excerpt || blog.content?.substring(0, 160),
      keywords: `${blog.title}, travel blog, ${blog.category?.name}`,
      image: blog.featuredImageUrl,
      type: 'article',
      url: `/blogs/${blog.id}`,
      author: blog.author?.name,
      publishedTime: blog.publishedAt,
      modifiedTime: blog.updatedAt,
      section: 'Travel Blog',
      tags: blog.tags?.map((tag: any) => tag.name) || []
    });

    // Blog article structured data
    const articleStructuredData: StructuredData = {
      '@context': 'https://schema.org',
      '@type': 'BlogPosting',
      'headline': blog.title,
      'description': blog.excerpt,
      'image': this.getAbsoluteUrl(blog.featuredImageUrl),
      'url': this.getAbsoluteUrl(`/blogs/${blog.id}`),
      'datePublished': blog.publishedAt,
      'dateModified': blog.updatedAt,
      'author': {
        '@type': 'Person',
        'name': blog.author?.name
      },
      'publisher': {
        '@type': 'Organization',
        'name': 'Travel Booking App',
        'logo': {
          '@type': 'ImageObject',
          'url': this.getAbsoluteUrl('/assets/images/logo.png')
        }
      },
      'mainEntityOfPage': {
        '@type': 'WebPage',
        '@id': this.getAbsoluteUrl(`/blogs/${blog.id}`)
      }
    };

    this.addStructuredData('article', articleStructuredData);
  }

  // Utility methods
  private updateOrCreateMetaTag(name: string, content?: string): void {
    if (!content) return;
    
    const existingTag = this.metaService.getTag(`name="${name}"`);
    if (existingTag) {
      this.metaService.updateTag({ name, content });
    } else {
      this.metaService.addTag({ name, content });
    }
  }

  private updateOrCreateMetaProperty(property: string, content?: string): void {
    if (!content) return;
    
    const existingTag = this.metaService.getTag(`property="${property}"`);
    if (existingTag) {
      this.metaService.updateTag({ property, content });
    } else {
      this.metaService.addTag({ property, content });
    }
  }

  private updateOrCreateMetaName(name: string, content?: string): void {
    if (!content) return;
    
    const existingTag = this.metaService.getTag(`name="${name}"`);
    if (existingTag) {
      this.metaService.updateTag({ name, content });
    } else {
      this.metaService.addTag({ name, content });
    }
  }

  private addMetaProperty(property: string, content: string): void {
    this.metaService.addTag({ property, content });
  }

  private updateCanonicalUrl(url: string): void {
    const canonicalUrl = this.getAbsoluteUrl(url);
    
    let link: HTMLLinkElement | null = document.querySelector('link[rel="canonical"]');
    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      document.head.appendChild(link);
    }
    link.setAttribute('href', canonicalUrl);
  }

  private addStructuredData(id: string, data: StructuredData): void {
    // Remove existing structured data with the same id
    const existingScript = document.getElementById(`structured-data-${id}`);
    if (existingScript) {
      existingScript.remove();
    }

    // Add new structured data
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.id = `structured-data-${id}`;
    script.textContent = JSON.stringify(data);
    document.head.appendChild(script);
  }

  private getAbsoluteUrl(path?: string): string {
    if (!path) return window.location.origin;
    if (path.startsWith('http')) return path;
    return `${window.location.origin}${path.startsWith('/') ? path : '/' + path}`;
  }

  private getDifficultyText(difficulty: number): string {
    const difficultyMap = { 1: 'Beginner', 2: 'Intermediate', 3: 'Advanced' };
    return difficultyMap[difficulty as keyof typeof difficultyMap] || 'Unknown';
  }

  private trackPageView(url: string): void {
    // Google Analytics 4 tracking (if implemented)
    if (typeof gtag !== 'undefined') {
      gtag('config', 'GA_MEASUREMENT_ID', {
        page_path: url
      });
    }

    // Other analytics tracking can be added here
  }

  // Sitemap generation helper
  generateSitemapUrls(): string[] {
    // This would typically fetch from your API
    const staticUrls = [
      '/',
      '/trips',
      '/blogs',
      '/auth/login',
      '/auth/register'
    ];

    // Add dynamic URLs (trips, blogs, etc.)
    // This would be populated from your actual data
    return staticUrls;
  }
}
