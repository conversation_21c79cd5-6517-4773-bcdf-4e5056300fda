import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';

export interface Category {
  id: number;
  name: string;
  description?: string;
  iconUrl?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface TripCategory extends Category {
  tripCount?: number;
}

export interface BlogCategory extends Category {
  blogCount?: number;
}

export interface CreateCategoryDto {
  name: string;
  description?: string;
  iconUrl?: string;
}

export interface UpdateCategoryDto extends CreateCategoryDto {
  id: number;
  isActive?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CategoryService {
  private readonly apiService = inject(ApiService);

  // Trip Categories
  getTripCategories(): Observable<TripCategory[]> {
    return this.apiService.get<TripCategory[]>('trips/categories');
  }

  getTripCategoryById(id: number): Observable<TripCategory> {
    return this.apiService.get<TripCategory>(`trips/categories/${id}`);
  }

  createTripCategory(categoryData: CreateCategoryDto): Observable<TripCategory> {
    return this.apiService.post<TripCategory>('admin/trip-categories', categoryData);
  }

  updateTripCategory(id: number, categoryData: UpdateCategoryDto): Observable<TripCategory> {
    return this.apiService.put<TripCategory>(`admin/trip-categories/${id}`, categoryData);
  }

  deleteTripCategory(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/trip-categories/${id}`);
  }

  toggleTripCategoryStatus(id: number): Observable<TripCategory> {
    return this.apiService.put<TripCategory>(`admin/trip-categories/${id}/toggle-status`, {});
  }

  // Blog Categories
  getBlogCategories(): Observable<BlogCategory[]> {
    return this.apiService.get<BlogCategory[]>('blogs/categories');
  }

  getBlogCategoryById(id: number): Observable<BlogCategory> {
    return this.apiService.get<BlogCategory>(`blogs/categories/${id}`);
  }

  createBlogCategory(categoryData: CreateCategoryDto): Observable<BlogCategory> {
    return this.apiService.post<BlogCategory>('admin/blog-categories', categoryData);
  }

  updateBlogCategory(id: number, categoryData: UpdateCategoryDto): Observable<BlogCategory> {
    return this.apiService.put<BlogCategory>(`admin/blog-categories/${id}`, categoryData);
  }

  deleteBlogCategory(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/blog-categories/${id}`);
  }

  toggleBlogCategoryStatus(id: number): Observable<BlogCategory> {
    return this.apiService.put<BlogCategory>(`admin/blog-categories/${id}/toggle-status`, {});
  }

  // General category operations
  getCategories(): Observable<Category[]> {
    return this.apiService.get<Category[]>('categories');
  }

  searchCategories(searchTerm: string, type?: 'trip' | 'blog'): Observable<Category[]> {
    const params = { search: searchTerm, type };
    return this.apiService.get<Category[]>('categories/search', params);
  }

  getCategoryStats(): Observable<any> {
    return this.apiService.get('admin/categories/stats');
  }

  // Bulk operations
  bulkUpdateCategories(categoryIds: number[], updateData: Partial<Category>): Observable<void> {
    return this.apiService.put<void>('admin/categories/bulk-update', { categoryIds, updateData });
  }

  bulkDeleteCategories(categoryIds: number[]): Observable<void> {
    return this.apiService.delete<void>('admin/categories/bulk-delete', { categoryIds });
  }

  // Export functionality
  exportCategories(type?: 'trip' | 'blog'): Observable<Blob> {
    const params = type ? { type } : {};
    return this.apiService.getRaw<Blob>('admin/categories/export', params);
  }
}
