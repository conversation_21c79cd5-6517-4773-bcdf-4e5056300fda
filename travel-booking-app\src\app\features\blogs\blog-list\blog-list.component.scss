.blog-list-container {
  min-height: 100vh;
  background: #f8f9fa;
}

// Page Header
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
  text-align: center;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
  }

  p {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
  }
}

// Filters Section
.filters-section {
  background: white;
  padding: 25px 0;
  border-bottom: 1px solid #e2e8f0;

  .filters-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .search-box,
  .category-filter,
  .sort-filter {
    input, select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      font-size: 0.9rem;
      transition: border-color 0.2s;

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }
  }

  .results-info {
    color: #718096;
    font-size: 0.9rem;
    font-weight: 500;
  }
}

// Featured Section
.featured-section {
  padding: 50px 0;
  background: white;

  h2 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 30px;
    color: #2d3748;
    text-align: center;
  }

  .featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
  }

  .featured-blog {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .blog-image {
      height: 220px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
    }

    &:hover .blog-image img {
      transform: scale(1.05);
    }

    .blog-content {
      padding: 25px;

      .blog-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;

        .category {
          background: #667eea;
          color: white;
          padding: 4px 12px;
          border-radius: 15px;
          font-size: 0.8rem;
          font-weight: 500;
        }

        .reading-time {
          color: #718096;
          font-size: 0.9rem;
        }
      }

      h3 {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 12px;
        color: #2d3748;
        line-height: 1.4;
      }

      p {
        color: #718096;
        margin-bottom: 20px;
        line-height: 1.6;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .blog-author {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.9rem;
        color: #4a5568;

        .publish-date {
          color: #718096;
        }
      }
    }
  }
}

// Blog List Section
.blog-list-section {
  padding: 50px 0;

  .blogs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
  }

  .blog-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 12px 25px rgba(0, 0, 0, 0.1);
    }

    .blog-image {
      height: 180px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
    }

    &:hover .blog-image img {
      transform: scale(1.05);
    }

    .blog-content {
      padding: 20px;

      .blog-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .category {
          background: #edf2f7;
          color: #4a5568;
          padding: 3px 10px;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .reading-time {
          color: #718096;
          font-size: 0.8rem;
        }
      }

      h3 {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 10px;
        color: #2d3748;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      p {
        color: #718096;
        margin-bottom: 15px;
        line-height: 1.5;
        font-size: 0.9rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .blog-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.8rem;

        .blog-author {
          color: #4a5568;
          font-weight: 500;
        }

        .blog-stats {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 2px;

          .views {
            color: #718096;
          }

          .date {
            color: #a0aec0;
          }
        }
      }
    }
  }

  .no-results {
    text-align: center;
    padding: 60px 20px;
    color: #718096;

    h3 {
      font-size: 1.5rem;
      margin-bottom: 10px;
      color: #4a5568;
    }
  }
}

// Pagination
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 40px;

  .btn {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// Loading States
.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 30px;

  .blog-card-skeleton {
    background: white;
    border-radius: 12px;
    height: 350px;
    animation: pulse 1.5s ease-in-out infinite alternate;
  }
}

// Common Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover {
      background: #5a67d8;
    }
  }

  &.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;

    &:hover {
      background: #667eea;
      color: white;
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  100% { opacity: 0.4; }
}
