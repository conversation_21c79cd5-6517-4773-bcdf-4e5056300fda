export interface Booking {
  id: number;
  bookingNumber: string;
  user: User;
  trip: Trip;
  bookingDate: Date;
  travelDate: Date;
  numberOfAdults: number;
  numberOfChildren: number;
  numberOfInfants: number;
  totalPeople: number;
  pricePerPerson: number;
  totalAmount: number;
  discountAmount: number;
  finalAmount: number;
  status: BookingStatus;
  paymentStatus: PaymentStatus;
  specialRequests?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  cancellationReason?: string;
  cancelledAt?: Date;
  payment?: BookingPayment;
  createdAt: Date;
  updatedAt?: Date;
}

export interface BookingPayment {
  id: number;
  bookingId: number;
  paymentMethod: string;
  transactionId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentDate?: Date;
  failureReason?: string;
  refundAmount?: number;
  refundDate?: Date;
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreateBookingDto {
  tripId: number;
  travelDate: Date;
  numberOfAdults: number;
  numberOfChildren: number;
  numberOfInfants: number;
  specialRequests?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
}

export interface UpdateBookingDto {
  id: number;
  travelDate?: Date;
  numberOfAdults?: number;
  numberOfChildren?: number;
  numberOfInfants?: number;
  specialRequests?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
}

export interface BookingFilterDto {
  userId?: number;
  tripId?: number;
  status?: BookingStatus;
  paymentStatus?: PaymentStatus;
  bookingDateFrom?: Date;
  bookingDateTo?: Date;
  travelDateFrom?: Date;
  travelDateTo?: Date;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaymentRequest {
  bookingId: number;
  paymentMethod: string;
  amount: number;
  currency: string;
}

export enum BookingStatus {
  Pending = 1,
  Confirmed = 2,
  Cancelled = 3,
  Completed = 4
}

export enum PaymentStatus {
  Pending = 1,
  Paid = 2,
  Failed = 3,
  Refunded = 4
}

// Import dependencies
import { User } from './user.model';
import { Trip } from './trip.model';
