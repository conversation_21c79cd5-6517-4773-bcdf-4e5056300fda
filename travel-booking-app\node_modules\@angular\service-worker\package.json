{"name": "@angular/service-worker", "version": "20.0.6", "description": "Angular - service worker tooling!", "author": "angular", "license": "MIT", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0"}, "exports": {"./ngsw-worker.js": {"default": "./ngsw-worker.js"}, "./safety-worker.js": {"default": "./safety-worker.js"}, "./config/schema.json": {"default": "./config/schema.json"}, "./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/service-worker.mjs"}, "./config": {"types": "./config/index.d.ts", "default": "./fesm2022/config.mjs"}}, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": "20.0.6", "rxjs": "^6.5.3 || ^7.4.0"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/service-worker"}, "bin": {"ngsw-config": "./ngsw-config.js"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": false, "module": "./fesm2022/service-worker.mjs", "typings": "./index.d.ts", "type": "module"}