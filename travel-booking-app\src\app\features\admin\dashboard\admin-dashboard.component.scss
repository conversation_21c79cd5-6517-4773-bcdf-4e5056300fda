.admin-dashboard-container {
  min-height: 100vh;
  background: #f1f5f9;
  padding: 40px 0;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

// Header
.admin-header {
  text-align: center;
  margin-bottom: 40px;
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 15px;
  }

  p {
    color: #64748b;
    font-size: 1.1rem;
    margin-bottom: 20px;
  }

  .last-updated {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #64748b;
    font-size: 0.9rem;

    .refresh-btn {
      background: none;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      padding: 6px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #f1f5f9;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .spinning {
        animation: spin 1s linear infinite;
      }
    }
  }
}

// Loading State
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }

  p {
    color: #64748b;
    font-size: 1.1rem;
  }
}

// Metrics Grid
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.metric-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  }

  .metric-icon {
    font-size: 2.5rem;
    padding: 15px;
    border-radius: 12px;
  }

  &.revenue .metric-icon {
    background: rgba(34, 197, 94, 0.1);
  }

  &.bookings .metric-icon {
    background: rgba(59, 130, 246, 0.1);
  }

  &.users .metric-icon {
    background: rgba(168, 85, 247, 0.1);
  }

  &.trips .metric-icon {
    background: rgba(245, 158, 11, 0.1);
  }

  .metric-content {
    flex: 1;

    .metric-value {
      font-size: 2rem;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 5px;
    }

    .metric-label {
      color: #64748b;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .metric-change {
      font-size: 0.8rem;
      font-weight: 600;

      &.positive {
        color: #22c55e;
      }

      &.negative {
        color: #ef4444;
      }

      &.neutral {
        color: #64748b;
      }
    }
  }
}

// Analytics Grid
.analytics-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 25px;
  margin-bottom: 40px;

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}

.chart-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  &.recent-activity {
    grid-column: 1 / -1;

    @media (max-width: 1200px) {
      grid-column: auto;
    }
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 1px solid #e2e8f0;

    h3 {
      font-size: 1.2rem;
      font-weight: 600;
      color: #1e293b;
      margin: 0;
    }

    .chart-controls select {
      padding: 6px 12px;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      background: white;
      font-size: 0.9rem;
    }

    .view-all {
      color: #3b82f6;
      text-decoration: none;
      font-weight: 500;
      font-size: 0.9rem;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .chart-content {
    padding: 30px;
  }
}

// Simple Chart
.simple-chart {
  display: flex;
  align-items: end;
  gap: 15px;
  height: 200px;

  .chart-bar {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;

    .bar {
      width: 100%;
      background: linear-gradient(to top, #3b82f6, #60a5fa);
      border-radius: 4px 4px 0 0;
      min-height: 10px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(to top, #2563eb, #3b82f6);
      }
    }

    .bar-label {
      font-size: 0.8rem;
      color: #64748b;
      font-weight: 500;
    }

    .bar-value {
      font-size: 0.7rem;
      color: #1e293b;
      font-weight: 600;
    }
  }
}

// Status Chart
.status-chart {
  display: flex;
  flex-direction: column;
  gap: 15px;

  .status-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;

    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;

      &.status-confirmed { background: #22c55e; }
      &.status-pending { background: #f59e0b; }
      &.status-cancelled { background: #ef4444; }
      &.status-completed { background: #3b82f6; }
    }

    .status-info {
      flex: 1;

      .status-label {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 2px;
      }

      .status-count {
        font-size: 0.8rem;
        color: #64748b;
      }
    }

    .status-percentage {
      font-weight: 600;
      color: #1e293b;
    }
  }
}

// Destinations List
.destinations-list {
  display: flex;
  flex-direction: column;
  gap: 15px;

  .destination-item {
    display: flex;
    align-items: center;
    gap: 15px;

    .destination-rank {
      width: 30px;
      height: 30px;
      background: #3b82f6;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.9rem;
    }

    .destination-info {
      flex: 1;

      .destination-name {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 2px;
      }

      .destination-bookings {
        font-size: 0.8rem;
        color: #64748b;
      }
    }

    .destination-bar {
      width: 100px;
      height: 6px;
      background: #e2e8f0;
      border-radius: 3px;
      overflow: hidden;

      .bar-fill {
        height: 100%;
        background: #3b82f6;
        border-radius: 3px;
        transition: width 0.3s ease;
      }
    }
  }
}

// Activity List
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;

  .activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;

    .activity-avatar {
      width: 40px;
      height: 40px;
      background: #3b82f6;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 0.9rem;
    }

    .activity-info {
      flex: 1;

      .activity-title {
        font-weight: 500;
        color: #1e293b;
        margin-bottom: 4px;
      }

      .activity-meta {
        font-size: 0.8rem;
        color: #64748b;
      }
    }

    .activity-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.7rem;
      font-weight: 600;

      &.status-1 { background: #fef3c7; color: #92400e; }
      &.status-2 { background: #d1fae5; color: #065f46; }
      &.status-3 { background: #fee2e2; color: #991b1b; }
      &.status-4 { background: #dbeafe; color: #1e40af; }
    }
  }
}

// Quick Actions Section
.quick-actions-section {
  margin-bottom: 40px;

  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 25px;
    text-align: center;
  }

  .actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
  }

  .action-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 15px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .action-icon {
      font-size: 2rem;
    }

    .action-content {
      flex: 1;

      h3 {
        font-size: 1rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 5px;
      }

      p {
        font-size: 0.8rem;
        color: #64748b;
        margin: 0;
      }
    }

    .action-arrow {
      color: #3b82f6;
      font-weight: 600;
    }
  }
}

// System Status
.system-status {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 25px;
    text-align: center;
  }

  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;

    .status-indicator {
      width: 12px;
      height: 12px;
      border-radius: 50%;

      &.online { background: #22c55e; }
      &.warning { background: #f59e0b; }
      &.error { background: #ef4444; }
    }

    .status-label {
      flex: 1;
      font-weight: 500;
      color: #1e293b;
    }

    .status-value {
      font-size: 0.9rem;
      color: #64748b;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
