import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { AuthService, UserService, NotificationService } from '../../core/services';
import { UserProfile } from '../../core/models';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule],
  template: `
    <div class="profile-container">
      <div class="container">
        <div class="profile-header">
          <h1>Profile Settings</h1>
          <p>Manage your personal information and preferences</p>
        </div>

        <div class="profile-content">
          <!-- Profile Picture Section -->
          <div class="profile-picture-section">
            <div class="profile-picture">
              <div class="avatar">
                {{ getUserInitials() }}
              </div>
              <button class="change-picture-btn" (click)="changePicture()">
                Change Picture
              </button>
            </div>
          </div>

          <!-- Profile Form -->
          <form [formGroup]="profileForm" (ngSubmit)="onSubmit()" class="profile-form">
            <div class="form-section">
              <h2>Personal Information</h2>

              <div class="form-row">
                <div class="form-group">
                  <label for="firstName">First Name</label>
                  <input
                    id="firstName"
                    type="text"
                    formControlName="firstName"
                    [class.error]="isFieldInvalid('firstName')"
                  />
                  @if (isFieldInvalid('firstName')) {
                    <div class="error-message">First name is required</div>
                  }
                </div>

                <div class="form-group">
                  <label for="lastName">Last Name</label>
                  <input
                    id="lastName"
                    type="text"
                    formControlName="lastName"
                    [class.error]="isFieldInvalid('lastName')"
                  />
                  @if (isFieldInvalid('lastName')) {
                    <div class="error-message">Last name is required</div>
                  }
                </div>
              </div>

              <div class="form-group">
                <label for="email">Email Address</label>
                <input
                  id="email"
                  type="email"
                  formControlName="email"
                  [class.error]="isFieldInvalid('email')"
                />
                @if (isFieldInvalid('email')) {
                  <div class="error-message">
                    @if (profileForm.get('email')?.errors?.['required']) {
                      Email is required
                    } @else if (profileForm.get('email')?.errors?.['email']) {
                      Please enter a valid email address
                    }
                  </div>
                }
              </div>

              <div class="form-group">
                <label for="phoneNumber">Phone Number</label>
                <input
                  id="phoneNumber"
                  type="tel"
                  formControlName="phoneNumber"
                />
              </div>

              <div class="form-group">
                <label for="dateOfBirth">Date of Birth</label>
                <input
                  id="dateOfBirth"
                  type="date"
                  formControlName="dateOfBirth"
                />
              </div>
            </div>

            <div class="form-section">
              <h2>Address Information</h2>

              <div class="form-group">
                <label for="address">Street Address</label>
                <input
                  id="address"
                  type="text"
                  formControlName="address"
                />
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="city">City</label>
                  <input
                    id="city"
                    type="text"
                    formControlName="city"
                  />
                </div>

                <div class="form-group">
                  <label for="state">State/Province</label>
                  <input
                    id="state"
                    type="text"
                    formControlName="state"
                  />
                </div>

                <div class="form-group">
                  <label for="zipCode">ZIP/Postal Code</label>
                  <input
                    id="zipCode"
                    type="text"
                    formControlName="zipCode"
                  />
                </div>
              </div>

              <div class="form-group">
                <label for="country">Country</label>
                <select id="country" formControlName="country">
                  <option value="">Select Country</option>
                  <option value="US">United States</option>
                  <option value="CA">Canada</option>
                  <option value="UK">United Kingdom</option>
                  <option value="AU">Australia</option>
                  <option value="DE">Germany</option>
                  <option value="FR">France</option>
                  <option value="IT">Italy</option>
                  <option value="ES">Spain</option>
                  <option value="JP">Japan</option>
                  <option value="CN">China</option>
                  <!-- Add more countries as needed -->
                </select>
              </div>
            </div>

            <div class="form-section">
              <h2>Preferences</h2>

              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" formControlName="emailNotifications" />
                  <span class="checkmark"></span>
                  Receive email notifications about new trips and offers
                </label>
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" formControlName="smsNotifications" />
                  <span class="checkmark"></span>
                  Receive SMS notifications for booking updates
                </label>
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" formControlName="newsletter" />
                  <span class="checkmark"></span>
                  Subscribe to our travel newsletter
                </label>
              </div>
            </div>

            @if (errorMessage()) {
              <div class="alert alert-error">
                {{ errorMessage() }}
              </div>
            }

            <div class="form-actions">
              <button type="button" class="btn btn-outline" routerLink="/dashboard">
                Cancel
              </button>
              <button
                type="submit"
                class="btn btn-primary"
                [disabled]="profileForm.invalid || isLoading()"
              >
                @if (isLoading()) {
                  <span class="spinner"></span>
                  Saving...
                } @else {
                  Save Changes
                }
              </button>
            </div>
          </form>

          <!-- Password Change Section -->
          <div class="password-section">
            <h2>Change Password</h2>
            <p>For security reasons, we recommend changing your password regularly.</p>
            <button class="btn btn-outline" (click)="changePassword()">
              Change Password
            </button>
          </div>

          <!-- Account Actions -->
          <div class="account-actions">
            <h2>Account Actions</h2>
            <div class="danger-zone">
              <h3>Danger Zone</h3>
              <p>These actions cannot be undone. Please be careful.</p>
              <button class="btn btn-danger" (click)="deleteAccount()">
                Delete Account
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit {
  private readonly fb = inject(FormBuilder);
  private readonly authService = inject(AuthService);
  private readonly userService = inject(UserService);
  private readonly notificationService = inject(NotificationService);

  profileForm: FormGroup;
  isLoading = signal<boolean>(false);
  errorMessage = signal<string>('');
  currentUser = this.authService.currentUser;

  constructor() {
    this.profileForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [''],
      dateOfBirth: [''],
      address: [''],
      city: [''],
      state: [''],
      zipCode: [''],
      country: [''],
      emailNotifications: [true],
      smsNotifications: [false],
      newsletter: [true]
    });
  }

  ngOnInit(): void {
    this.loadUserProfile();
  }

  private async loadUserProfile(): Promise<void> {
    this.isLoading.set(true);

    try {
      const profile = await this.userService.getUserProfile().toPromise();
      if (profile) {
        this.profileForm.patchValue(profile);
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      this.errorMessage.set('Failed to load profile data');
    } finally {
      this.isLoading.set(false);
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.profileForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getUserInitials(): string {
    const user = this.currentUser();
    if (!user) return '';
    return `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`.toUpperCase();
  }

  async onSubmit(): Promise<void> {
    if (this.profileForm.valid) {
      this.isLoading.set(true);
      this.errorMessage.set('');

      try {
        const formValue = this.profileForm.value;
        await this.userService.updateUserProfile(formValue).toPromise();

        this.notificationService.showSuccess('Profile updated successfully!');
      } catch (error: any) {
        this.errorMessage.set(
          error?.error?.message || 'An error occurred while updating your profile'
        );
      } finally {
        this.isLoading.set(false);
      }
    } else {
      Object.keys(this.profileForm.controls).forEach(key => {
        this.profileForm.get(key)?.markAsTouched();
      });
    }
  }

  changePicture(): void {
    // TODO: Implement picture upload
    this.notificationService.showInfo('Picture upload feature coming soon!');
  }

  changePassword(): void {
    // TODO: Navigate to change password page or show modal
    this.notificationService.showInfo('Password change feature coming soon!');
  }

  deleteAccount(): void {
    // TODO: Show confirmation dialog and implement account deletion
    this.notificationService.showWarning('Account deletion feature coming soon!');
  }
}
