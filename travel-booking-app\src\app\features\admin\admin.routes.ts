import { Routes } from '@angular/router';

export const adminRoutes: Routes = [
  {
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  },
  {
    path: 'dashboard',
    loadComponent: () => import('./dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent)
  },
  {
    path: 'trips',
    loadComponent: () => import('./trips/admin-trips.component').then(m => m.AdminTripsComponent)
  },
  {
    path: 'blogs',
    loadComponent: () => import('./blogs/admin-blogs.component').then(m => m.AdminBlogsComponent)
  },
  {
    path: 'users',
    loadComponent: () => import('./users/admin-users.component').then(m => m.AdminUsersComponent)
  },
  {
    path: 'bookings',
    loadComponent: () => import('./bookings/admin-bookings.component').then(m => m.AdminBookingsComponent)
  }
];
