# Deployment Guide - Travel Booking App

## Prerequisites

### Development Environment
- **Node.js**: v18.0.0 or higher
- **npm**: v8.0.0 or higher (or yarn v1.22+)
- **Angular CLI**: v20.0.0 or higher
- **Git**: Latest version

### Backend Requirements
- **TravelTourism.API**: Running on `https://localhost:7115`
- **Database**: SQL Server (configured in API)
- **Authentication**: JWT tokens configured

### External Services (Optional)
- **Google Maps API**: For location services
- **Stripe**: For payment processing
- **SendGrid**: For email notifications
- **Cloudinary**: For image optimization

## Environment Configuration

### Development Environment
**File:** `src/environments/environment.ts`

```typescript
export const environment = {
  production: false,
  apiUrl: 'https://localhost:7115/api/v1',
  
  // Feature flags
  enablePWA: true,
  enableAnalytics: false,
  enableAnimations: true,
  enableOfflineMode: true,
  
  // External services
  googleMapsApiKey: '',
  stripePublishableKey: '',
  
  // App configuration
  appName: 'Travel Booking App',
  appVersion: '1.0.0',
  supportEmail: '<EMAIL>',
  
  // Performance settings
  cacheTimeout: 300000, // 5 minutes
  requestTimeout: 30000, // 30 seconds
  maxRetries: 3,
  
  // Logging
  logLevel: 'debug',
  enableConsoleLogging: true
};
```

### Production Environment
**File:** `src/environments/environment.prod.ts`

```typescript
export const environment = {
  production: true,
  apiUrl: 'https://api.travelbooking.com/api/v1',
  
  // Feature flags
  enablePWA: true,
  enableAnalytics: true,
  enableAnimations: true,
  enableOfflineMode: true,
  
  // External services
  googleMapsApiKey: 'your-production-google-maps-key',
  stripePublishableKey: 'pk_live_your-stripe-key',
  
  // App configuration
  appName: 'Travel Booking App',
  appVersion: '1.0.0',
  supportEmail: '<EMAIL>',
  
  // Performance settings
  cacheTimeout: 600000, // 10 minutes
  requestTimeout: 30000,
  maxRetries: 3,
  
  // Logging
  logLevel: 'error',
  enableConsoleLogging: false,
  
  // Analytics
  googleAnalyticsId: 'GA_MEASUREMENT_ID',
  
  // Security
  enableCSP: true,
  enableHSTS: true
};
```

### Staging Environment
**File:** `src/environments/environment.staging.ts`

```typescript
export const environment = {
  production: false,
  apiUrl: 'https://staging-api.travelbooking.com/api/v1',
  
  // Feature flags
  enablePWA: true,
  enableAnalytics: false,
  enableAnimations: true,
  enableOfflineMode: true,
  
  // External services (test keys)
  googleMapsApiKey: 'your-staging-google-maps-key',
  stripePublishableKey: 'pk_test_your-stripe-test-key',
  
  // App configuration
  appName: 'Travel Booking App (Staging)',
  appVersion: '1.0.0-staging',
  supportEmail: '<EMAIL>',
  
  // Performance settings
  cacheTimeout: 300000,
  requestTimeout: 30000,
  maxRetries: 3,
  
  // Logging
  logLevel: 'info',
  enableConsoleLogging: true
};
```

## Build Configuration

### Angular Configuration
**File:** `angular.json`

```json
{
  "projects": {
    "travel-booking-app": {
      "architect": {
        "build": {
          "builder": "@angular-devkit/build-angular:browser",
          "options": {
            "outputPath": "dist/travel-booking-app",
            "index": "src/index.html",
            "main": "src/main.ts",
            "polyfills": "src/polyfills.ts",
            "tsConfig": "tsconfig.app.json",
            "assets": [
              "src/favicon.ico",
              "src/assets",
              "src/manifest.json"
            ],
            "styles": [
              "src/styles.scss"
            ],
            "scripts": [],
            "budgets": [
              {
                "type": "initial",
                "maximumWarning": "2mb",
                "maximumError": "5mb"
              },
              {
                "type": "anyComponentStyle",
                "maximumWarning": "6kb",
                "maximumError": "10kb"
              }
            ]
          },
          "configurations": {
            "production": {
              "fileReplacements": [
                {
                  "replace": "src/environments/environment.ts",
                  "with": "src/environments/environment.prod.ts"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "serviceWorker": true,
              "ngswConfigPath": "ngsw-config.json"
            },
            "staging": {
              "fileReplacements": [
                {
                  "replace": "src/environments/environment.ts",
                  "with": "src/environments/environment.staging.ts"
                }
              ],
              "optimization": true,
              "outputHashing": "all",
              "sourceMap": true,
              "namedChunks": false,
              "extractLicenses": true,
              "vendorChunk": false,
              "buildOptimizer": true,
              "serviceWorker": true,
              "ngswConfigPath": "ngsw-config.json"
            }
          }
        }
      }
    }
  }
}
```

### TypeScript Configuration
**File:** `tsconfig.json`

```json
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "target": "ES2022",
    "module": "ES2022",
    "useDefineForClassFields": false,
    "lib": [
      "ES2022",
      "dom"
    ]
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
```

## Build Commands

### Development Build
```bash
# Standard development build
ng build

# Development build with source maps
ng build --source-map

# Development build with specific environment
ng build --configuration development
```

### Staging Build
```bash
# Staging build
ng build --configuration staging

# Staging build with analysis
ng build --configuration staging --stats-json
npx webpack-bundle-analyzer dist/travel-booking-app/stats.json
```

### Production Build
```bash
# Production build
ng build --configuration production

# Production build with analysis
ng build --configuration production --stats-json

# Production build with specific base href
ng build --configuration production --base-href /travel-app/
```

### Build Optimization
```bash
# Build with maximum optimization
ng build --configuration production --optimization --build-optimizer

# Build with specific output path
ng build --configuration production --output-path dist/production

# Build with progress reporting
ng build --configuration production --progress
```

## Deployment Options

### 1. Static Hosting (Netlify, Vercel, GitHub Pages)

#### Netlify Deployment
**File:** `netlify.toml`

```toml
[build]
  publish = "dist/travel-booking-app"
  command = "npm run build:prod"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

#### Vercel Deployment
**File:** `vercel.json`

```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist/travel-booking-app"
      }
    }
  ],
  "routes": [
    {
      "src": "/assets/(.*)",
      "headers": {
        "cache-control": "public, max-age=31536000, immutable"
      }
    },
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        }
      ]
    }
  ]
}
```

### 2. Docker Deployment

#### Dockerfile
```dockerfile
# Multi-stage build
FROM node:18-alpine AS build

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build the application
RUN npm run build:prod

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=build /app/dist/travel-booking-app /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

#### Nginx Configuration
**File:** `nginx.conf`

```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Security headers
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # Cache static assets
        location /assets/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Cache JavaScript and CSS
        location ~* \.(js|css)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Handle Angular routing
        location / {
            try_files $uri $uri/ /index.html;
        }

        # API proxy (if needed)
        location /api/ {
            proxy_pass https://api.travelbooking.com/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

#### Docker Compose
**File:** `docker-compose.yml`

```yaml
version: '3.8'

services:
  travel-booking-app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add reverse proxy
  nginx-proxy:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl
      - ./proxy.conf:/etc/nginx/nginx.conf
    depends_on:
      - travel-booking-app
    restart: unless-stopped
```

### 3. Azure Static Web Apps

#### GitHub Actions Workflow
**File:** `.github/workflows/azure-static-web-apps.yml`

```yaml
name: Azure Static Web Apps CI/CD

on:
  push:
    branches: [ main ]
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches: [ main ]

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    steps:
    - uses: actions/checkout@v3
      with:
        submodules: true

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm run test:ci

    - name: Build And Deploy
      id: builddeploy
      uses: Azure/static-web-apps-deploy@v1
      with:
        azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN }}
        repo_token: ${{ secrets.GITHUB_TOKEN }}
        action: "upload"
        app_location: "/"
        api_location: ""
        output_location: "dist/travel-booking-app"
        app_build_command: "npm run build:prod"

  close_pull_request_job:
    if: github.event_name == 'pull_request' && github.event.action == 'closed'
    runs-on: ubuntu-latest
    name: Close Pull Request Job
    steps:
    - name: Close Pull Request
      id: closepullrequest
      uses: Azure/static-web-apps-deploy@v1
      with:
        azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN }}
        action: "close"
```

## Performance Optimization

### Bundle Analysis
```bash
# Generate bundle statistics
ng build --configuration production --stats-json

# Analyze bundle with webpack-bundle-analyzer
npx webpack-bundle-analyzer dist/travel-booking-app/stats.json

# Analyze bundle with source-map-explorer
npm install -g source-map-explorer
ng build --configuration production --source-map
source-map-explorer dist/travel-booking-app/*.js
```

### Optimization Strategies

#### 1. Lazy Loading
```typescript
// Implement lazy loading for all feature modules
const routes: Routes = [
  {
    path: 'trips',
    loadComponent: () => import('./features/trips/trips.component').then(m => m.TripsComponent)
  }
];
```

#### 2. Tree Shaking
```typescript
// Use specific imports instead of barrel imports
import { map, filter } from 'rxjs/operators';
// Instead of: import { map, filter } from 'rxjs';
```

#### 3. Image Optimization
```html
<!-- Use responsive images -->
<img 
  [src]="trip.mainImageUrl" 
  [alt]="trip.name"
  loading="lazy"
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
/>
```

#### 4. Service Worker Caching
```json
// ngsw-config.json
{
  "index": "/index.html",
  "assetGroups": [
    {
      "name": "app",
      "installMode": "prefetch",
      "resources": {
        "files": [
          "/favicon.ico",
          "/index.html",
          "/manifest.json",
          "/*.css",
          "/*.js"
        ]
      }
    },
    {
      "name": "assets",
      "installMode": "lazy",
      "updateMode": "prefetch",
      "resources": {
        "files": [
          "/assets/**",
          "/*.(eot|svg|cur|jpg|png|webp|gif|otf|ttf|woff|woff2|ani)"
        ]
      }
    }
  ],
  "dataGroups": [
    {
      "name": "api-cache",
      "urls": [
        "https://localhost:7115/api/v1/trips",
        "https://localhost:7115/api/v1/blogs"
      ],
      "cacheConfig": {
        "strategy": "freshness",
        "maxSize": 100,
        "maxAge": "1h",
        "timeout": "10s"
      }
    }
  ]
}
```

## Security Configuration

### Content Security Policy
```html
<!-- Add to index.html -->
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline' https://maps.googleapis.com; 
               style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; 
               font-src 'self' https://fonts.gstatic.com; 
               img-src 'self' data: https:; 
               connect-src 'self' https://localhost:7115 https://api.travelbooking.com;">
```

### Environment Variables Security
```bash
# Use environment variables for sensitive data
export GOOGLE_MAPS_API_KEY="your-api-key"
export STRIPE_PUBLISHABLE_KEY="your-stripe-key"
export API_BASE_URL="https://api.travelbooking.com"

# In CI/CD, use secrets management
# GitHub Secrets, Azure Key Vault, AWS Secrets Manager, etc.
```

## Monitoring and Analytics

### Error Tracking
```typescript
// Global error handler
export class GlobalErrorHandler implements ErrorHandler {
  handleError(error: any): void {
    console.error('Global error:', error);
    
    // Send to error tracking service
    if (environment.production) {
      // Sentry, LogRocket, etc.
      this.errorTrackingService.captureException(error);
    }
  }
}
```

### Performance Monitoring
```typescript
// Performance tracking
export class PerformanceService {
  trackPageLoad(pageName: string): void {
    const navigationTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    const metrics = {
      page: pageName,
      loadTime: navigationTiming.loadEventEnd - navigationTiming.loadEventStart,
      domContentLoaded: navigationTiming.domContentLoadedEventEnd - navigationTiming.domContentLoadedEventStart,
      firstContentfulPaint: this.getFirstContentfulPaint(),
      largestContentfulPaint: this.getLargestContentfulPaint()
    };
    
    // Send to analytics service
    this.analyticsService.track('page_performance', metrics);
  }
}
```

## Troubleshooting

### Common Issues

#### 1. Build Errors
```bash
# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Clear Angular cache
ng cache clean

# Update Angular CLI
npm install -g @angular/cli@latest
ng update @angular/core @angular/cli
```

#### 2. Memory Issues
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=8192"
ng build --configuration production
```

#### 3. Service Worker Issues
```bash
# Clear service worker cache
# In browser dev tools: Application > Storage > Clear storage

# Disable service worker in development
ng build --configuration development --service-worker=false
```

#### 4. CORS Issues
```typescript
// Configure proxy for development
// proxy.conf.json
{
  "/api/*": {
    "target": "https://localhost:7115",
    "secure": true,
    "changeOrigin": true,
    "logLevel": "debug"
  }
}

// Start dev server with proxy
ng serve --proxy-config proxy.conf.json
```

## Maintenance

### Regular Updates
```bash
# Check for outdated packages
npm outdated

# Update Angular
ng update @angular/core @angular/cli

# Update dependencies
npm update

# Security audit
npm audit
npm audit fix
```

### Backup Strategy
- Source code: Git repository with multiple remotes
- Build artifacts: Store in artifact repository
- Configuration: Environment-specific configs in secure storage
- Dependencies: Lock file versioning (package-lock.json)
