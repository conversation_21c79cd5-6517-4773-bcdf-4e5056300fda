import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { AuthService, NotificationService } from '../../../core/services';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule],
  template: `
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h1>Reset Password</h1>
          <p>Enter your email address and we'll send you a link to reset your password</p>
        </div>

        @if (!emailSent()) {
          <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()" class="auth-form">
            <div class="form-group">
              <label for="email">Email Address</label>
              <input
                id="email"
                type="email"
                formControlName="email"
                placeholder="Enter your email address"
                [class.error]="isFieldInvalid('email')"
              />
              @if (isFieldInvalid('email')) {
                <div class="error-message">
                  @if (forgotPasswordForm.get('email')?.errors?.['required']) {
                    Email is required
                  } @else if (forgotPasswordForm.get('email')?.errors?.['email']) {
                    Please enter a valid email address
                  }
                </div>
              }
            </div>

            @if (errorMessage()) {
              <div class="alert alert-error">
                {{ errorMessage() }}
              </div>
            }

            <button
              type="submit"
              class="btn btn-primary btn-full"
              [disabled]="forgotPasswordForm.invalid || isLoading()"
            >
              @if (isLoading()) {
                <span class="spinner"></span>
                Sending...
              } @else {
                Send Reset Link
              }
            </button>
          </form>
        } @else {
          <div class="success-message">
            <div class="success-icon">✅</div>
            <h2>Check Your Email</h2>
            <p>
              We've sent a password reset link to <strong>{{ submittedEmail() }}</strong>
            </p>
            <p class="help-text">
              Didn't receive the email? Check your spam folder or 
              <button class="link-button" (click)="resendEmail()">
                click here to resend
              </button>
            </p>
          </div>
        }

        <div class="auth-footer">
          <p>
            Remember your password?
            <a routerLink="/auth/login">Back to Sign In</a>
          </p>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['../login/login.component.scss', './forgot-password.component.scss']
})
export class ForgotPasswordComponent {
  private readonly fb = inject(FormBuilder);
  private readonly authService = inject(AuthService);
  private readonly notificationService = inject(NotificationService);

  forgotPasswordForm: FormGroup;
  isLoading = signal<boolean>(false);
  errorMessage = signal<string>('');
  emailSent = signal<boolean>(false);
  submittedEmail = signal<string>('');

  constructor() {
    this.forgotPasswordForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.forgotPasswordForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  async onSubmit(): Promise<void> {
    if (this.forgotPasswordForm.valid) {
      this.isLoading.set(true);
      this.errorMessage.set('');

      try {
        const { email } = this.forgotPasswordForm.value;
        await this.authService.forgotPassword({ email }).toPromise();

        this.submittedEmail.set(email);
        this.emailSent.set(true);
        this.notificationService.showSuccess('Password reset email sent successfully!');
      } catch (error: any) {
        this.errorMessage.set(
          error?.error?.message || 'An error occurred while sending the reset email'
        );
      } finally {
        this.isLoading.set(false);
      }
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.forgotPasswordForm.controls).forEach(key => {
        this.forgotPasswordForm.get(key)?.markAsTouched();
      });
    }
  }

  async resendEmail(): Promise<void> {
    this.isLoading.set(true);
    
    try {
      const email = this.submittedEmail();
      await this.authService.forgotPassword({ email }).toPromise();
      this.notificationService.showSuccess('Reset email sent again!');
    } catch (error: any) {
      this.notificationService.showError('Failed to resend email. Please try again.');
    } finally {
      this.isLoading.set(false);
    }
  }
}
