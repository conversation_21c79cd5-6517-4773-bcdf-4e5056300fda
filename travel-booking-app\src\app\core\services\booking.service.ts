import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  Booking, 
  CreateBookingDto, 
  UpdateBookingDto, 
  BookingFilterDto,
  PaymentRequest,
  BookingPayment 
} from '../models/booking.model';
import { PagedResult, PaginationParameters } from '../models/common.model';

@Injectable({
  providedIn: 'root'
})
export class BookingService {
  private readonly apiService = inject(ApiService);

  // User booking methods
  createBooking(bookingData: CreateBookingDto): Observable<Booking> {
    return this.apiService.post<Booking>('bookings', bookingData);
  }

  getUserBookings(pagination: PaginationParameters, filters?: BookingFilterDto): Observable<PagedResult<Booking>> {
    return this.apiService.getPaged<Booking>('users/bookings', pagination, filters);
  }

  getBookingById(id: number): Observable<Booking> {
    return this.apiService.get<Booking>(`bookings/${id}`);
  }

  updateBooking(bookingData: UpdateBookingDto): Observable<Booking> {
    return this.apiService.put<Booking>(`bookings/${bookingData.id}`, bookingData);
  }

  cancelBooking(id: number, reason?: string): Observable<Booking> {
    return this.apiService.put<Booking>(`bookings/${id}/cancel`, { reason });
  }

  // Payment methods
  processPayment(paymentData: PaymentRequest): Observable<BookingPayment> {
    return this.apiService.post<BookingPayment>(`bookings/${paymentData.bookingId}/payment`, paymentData);
  }

  getPaymentStatus(bookingId: number): Observable<BookingPayment> {
    return this.apiService.get<BookingPayment>(`bookings/${bookingId}/payment`);
  }

  // Admin booking methods
  getAdminBookings(pagination: PaginationParameters, filters?: BookingFilterDto): Observable<PagedResult<Booking>> {
    return this.apiService.getPaged<Booking>('admin/bookings', pagination, filters);
  }

  updateBookingStatus(id: number, status: number): Observable<Booking> {
    return this.apiService.put<Booking>(`admin/bookings/${id}/status`, { status });
  }

  getBookingDetails(id: number): Observable<Booking> {
    return this.apiService.get<Booking>(`admin/bookings/${id}`);
  }

  // Booking statistics
  getBookingStats(): Observable<any> {
    return this.apiService.get('admin/bookings/stats');
  }

  getMonthlyBookingStats(year: number): Observable<any> {
    return this.apiService.get(`admin/bookings/stats/monthly/${year}`);
  }

  getRevenueStats(): Observable<any> {
    return this.apiService.get('admin/bookings/stats/revenue');
  }

  // Booking reports
  generateBookingReport(filters: BookingFilterDto): Observable<any> {
    return this.apiService.post('admin/bookings/reports', filters);
  }

  exportBookings(filters?: BookingFilterDto): Observable<Blob> {
    return this.apiService.getRaw<Blob>('admin/bookings/export', filters);
  }

  // Bulk operations
  bulkUpdateBookingStatus(bookingIds: number[], status: number): Observable<void> {
    return this.apiService.put<void>('admin/bookings/bulk-status-update', { bookingIds, status });
  }

  // Refund operations
  processRefund(bookingId: number, amount: number, reason: string): Observable<BookingPayment> {
    return this.apiService.post<BookingPayment>(`admin/bookings/${bookingId}/refund`, { amount, reason });
  }

  getRefundHistory(bookingId: number): Observable<BookingPayment[]> {
    return this.apiService.get<BookingPayment[]>(`admin/bookings/${bookingId}/refunds`);
  }

  // Booking validation
  validateBookingAvailability(tripId: number, travelDate: Date, numberOfPeople: number): Observable<boolean> {
    return this.apiService.post<boolean>('bookings/validate-availability', {
      tripId,
      travelDate,
      numberOfPeople
    });
  }

  calculateBookingPrice(tripId: number, numberOfAdults: number, numberOfChildren: number, numberOfInfants: number): Observable<any> {
    return this.apiService.post('bookings/calculate-price', {
      tripId,
      numberOfAdults,
      numberOfChildren,
      numberOfInfants
    });
  }

  // Booking notifications
  sendBookingConfirmation(bookingId: number): Observable<void> {
    return this.apiService.post<void>(`admin/bookings/${bookingId}/send-confirmation`, {});
  }

  sendBookingReminder(bookingId: number): Observable<void> {
    return this.apiService.post<void>(`admin/bookings/${bookingId}/send-reminder`, {});
  }

  // Customer communication
  addBookingNote(bookingId: number, note: string): Observable<void> {
    return this.apiService.post<void>(`admin/bookings/${bookingId}/notes`, { note });
  }

  getBookingNotes(bookingId: number): Observable<any[]> {
    return this.apiService.get<any[]>(`admin/bookings/${bookingId}/notes`);
  }
}
