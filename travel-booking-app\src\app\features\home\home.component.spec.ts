import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { HomeComponent } from './home.component';
import { TripService, BlogService, StateService } from '../../core/services';
import { Trip, Blog } from '../../core/models';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { signal } from '@angular/core';

describe('HomeComponent', () => {
  let component: HomeComponent;
  let fixture: ComponentFixture<HomeComponent>;
  let tripServiceSpy: jasmine.SpyObj<TripService>;
  let blogServiceSpy: jasmine.SpyObj<BlogService>;
  let stateServiceSpy: jasmine.SpyObj<StateService>;
  let routerSpy: jasmine.SpyObj<Router>;

  const mockTrips: Trip[] = [
    {
      id: 1,
      name: 'Bali Adventure',
      shortDescription: 'Amazing adventure in Bali',
      price: 1200,
      discountPrice: 1000,
      duration: 7,
      maxCapacity: 20,
      difficulty: 2,
      mainImageUrl: 'bali-main.jpg',
      isFeatured: true,
      availableFrom: new Date('2024-01-01'),
      availableTo: new Date('2024-12-31'),
      category: { id: 1, name: 'Adventure', description: 'Adventure trips', iconUrl: 'adventure.png' },
      destinationCity: { id: 1, name: 'Ubud', country: { id: 1, name: 'Indonesia', code: 'ID' } },
      departureCity: { id: 2, name: 'Jakarta', country: { id: 1, name: 'Indonesia', code: 'ID' } },
      images: [],
      effectivePrice: 1000,
      hasDiscount: true,
      discountPercentage: 16.67
    },
    {
      id: 2,
      name: 'Tokyo Explorer',
      shortDescription: 'Explore the vibrant city of Tokyo',
      price: 1800,
      duration: 10,
      maxCapacity: 15,
      difficulty: 1,
      mainImageUrl: 'tokyo-main.jpg',
      isFeatured: true,
      availableFrom: new Date('2024-01-01'),
      availableTo: new Date('2024-12-31'),
      category: { id: 2, name: 'Cultural', description: 'Cultural trips', iconUrl: 'cultural.png' },
      destinationCity: { id: 3, name: 'Tokyo', country: { id: 2, name: 'Japan', code: 'JP' } },
      departureCity: { id: 4, name: 'Osaka', country: { id: 2, name: 'Japan', code: 'JP' } },
      images: [],
      effectivePrice: 1800,
      hasDiscount: false,
      discountPercentage: 0
    }
  ];

  const mockBlogs: Blog[] = [
    {
      id: 1,
      title: 'Best Places to Visit in Bali',
      excerpt: 'Discover the most beautiful places in Bali',
      content: 'Full content about Bali...',
      featuredImageUrl: 'bali-blog.jpg',
      isPublished: true,
      publishedAt: new Date('2024-01-15'),
      author: { id: 1, name: 'John Doe', email: '<EMAIL>' },
      category: { id: 1, name: 'Destinations', description: 'Travel destinations' },
      tags: [{ id: 1, name: 'Bali' }, { id: 2, name: 'Indonesia' }],
      readTime: 5,
      viewCount: 150,
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15')
    }
  ];

  beforeEach(async () => {
    const tripSpy = jasmine.createSpyObj('TripService', ['getFeaturedTrips', 'getTripCategories']);
    const blogSpy = jasmine.createSpyObj('BlogService', ['getRecentBlogs']);
    const stateSpy = jasmine.createSpyObj('StateService', ['setLoading']);
    const routerSpyObj = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        RouterModule.forRoot([]),
        FormsModule,
        HomeComponent
      ],
      providers: [
        { provide: TripService, useValue: tripSpy },
        { provide: BlogService, useValue: blogSpy },
        { provide: StateService, useValue: stateSpy },
        { provide: Router, useValue: routerSpyObj }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(HomeComponent);
    component = fixture.componentInstance;
    tripServiceSpy = TestBed.inject(TripService) as jasmine.SpyObj<TripService>;
    blogServiceSpy = TestBed.inject(BlogService) as jasmine.SpyObj<BlogService>;
    stateServiceSpy = TestBed.inject(StateService) as jasmine.SpyObj<StateService>;
    routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should load featured trips and recent blogs on init', () => {
      tripServiceSpy.getFeaturedTrips.and.returnValue(of(mockTrips));
      blogServiceSpy.getRecentBlogs.and.returnValue(of(mockBlogs));

      component.ngOnInit();

      expect(tripServiceSpy.getFeaturedTrips).toHaveBeenCalled();
      expect(blogServiceSpy.getRecentBlogs).toHaveBeenCalledWith(3);
      expect(component.featuredTrips()).toEqual(mockTrips);
      expect(component.recentBlogs()).toEqual(mockBlogs);
      expect(component.isLoading()).toBe(false);
    });

    it('should handle error when loading featured trips', () => {
      tripServiceSpy.getFeaturedTrips.and.returnValue(throwError(() => new Error('API Error')));
      blogServiceSpy.getRecentBlogs.and.returnValue(of(mockBlogs));

      spyOn(console, 'error');

      component.ngOnInit();

      expect(console.error).toHaveBeenCalledWith('Error loading featured trips:', jasmine.any(Error));
      expect(component.featuredTrips()).toEqual([]);
      expect(component.isLoading()).toBe(false);
    });

    it('should handle error when loading recent blogs', () => {
      tripServiceSpy.getFeaturedTrips.and.returnValue(of(mockTrips));
      blogServiceSpy.getRecentBlogs.and.returnValue(throwError(() => new Error('API Error')));

      spyOn(console, 'error');

      component.ngOnInit();

      expect(console.error).toHaveBeenCalledWith('Error loading recent blogs:', jasmine.any(Error));
      expect(component.recentBlogs()).toEqual([]);
      expect(component.isLoading()).toBe(false);
    });
  });

  describe('search functionality', () => {
    beforeEach(() => {
      tripServiceSpy.getFeaturedTrips.and.returnValue(of(mockTrips));
      blogServiceSpy.getRecentBlogs.and.returnValue(of(mockBlogs));
      component.ngOnInit();
    });

    it('should perform search when search term is provided', () => {
      component.searchQuery.set('Bali');
      component.onSearch();

      expect(routerSpy.navigate).toHaveBeenCalledWith(['/trips'], {
        queryParams: { search: 'Bali' }
      });
    });

    it('should navigate to trips page when search term is empty', () => {
      component.searchQuery.set('');
      component.onSearch();

      expect(routerSpy.navigate).toHaveBeenCalledWith(['/trips']);
    });

    it('should trim search query before searching', () => {
      component.searchQuery.set('  Bali  ');
      component.onSearch();

      expect(routerSpy.navigate).toHaveBeenCalledWith(['/trips'], {
        queryParams: { search: 'Bali' }
      });
    });
  });

  describe('template rendering', () => {
    beforeEach(() => {
      tripServiceSpy.getFeaturedTrips.and.returnValue(of(mockTrips));
      blogServiceSpy.getRecentBlogs.and.returnValue(of(mockBlogs));
      component.ngOnInit();
      fixture.detectChanges();
    });

    it('should display hero section', () => {
      const heroSection = fixture.nativeElement.querySelector('.hero-section');
      expect(heroSection).toBeTruthy();

      const heroTitle = fixture.nativeElement.querySelector('.hero-title');
      expect(heroTitle.textContent).toContain('Discover Your Next Adventure');
    });

    it('should display search bar', () => {
      const searchInput = fixture.nativeElement.querySelector('.search-input');
      expect(searchInput).toBeTruthy();
      expect(searchInput.placeholder).toContain('Where do you want to go?');
    });

    it('should display featured trips', () => {
      const tripsSection = fixture.nativeElement.querySelector('.featured-trips');
      expect(tripsSection).toBeTruthy();

      const tripCards = fixture.nativeElement.querySelectorAll('.trip-card');
      expect(tripCards.length).toBe(mockTrips.length);

      // Check first trip card content
      const firstTripCard = tripCards[0];
      expect(firstTripCard.textContent).toContain('Bali Adventure');
      expect(firstTripCard.textContent).toContain('$1,000');
    });

    it('should display recent blogs', () => {
      const blogsSection = fixture.nativeElement.querySelector('.recent-blogs');
      expect(blogsSection).toBeTruthy();

      const blogCards = fixture.nativeElement.querySelectorAll('.blog-card');
      expect(blogCards.length).toBe(mockBlogs.length);

      // Check first blog card content
      const firstBlogCard = blogCards[0];
      expect(firstBlogCard.textContent).toContain('Best Places to Visit in Bali');
    });

    it('should show loading state', () => {
      component.isLoading.set(true);
      fixture.detectChanges();

      const loadingElements = fixture.nativeElement.querySelectorAll('.loading-skeleton');
      expect(loadingElements.length).toBeGreaterThan(0);
    });

    it('should show empty state when no trips available', () => {
      component.featuredTrips.set([]);
      fixture.detectChanges();

      const emptyState = fixture.nativeElement.querySelector('.empty-state');
      expect(emptyState).toBeTruthy();
      expect(emptyState.textContent).toContain('No featured trips available');
    });
  });

  describe('utility methods', () => {
    it('should format price correctly', () => {
      expect(component.formatPrice(1200)).toBe('$1,200');
      expect(component.formatPrice(999)).toBe('$999');
      expect(component.formatPrice(1000000)).toBe('$1,000,000');
    });

    it('should get difficulty text correctly', () => {
      expect(component.getDifficultyText(1)).toBe('Easy');
      expect(component.getDifficultyText(2)).toBe('Moderate');
      expect(component.getDifficultyText(3)).toBe('Hard');
      expect(component.getDifficultyText(4)).toBe('Unknown');
    });

    it('should calculate discount percentage correctly', () => {
      expect(component.getDiscountPercentage(1200, 1000)).toBe(17);
      expect(component.getDiscountPercentage(100, 80)).toBe(20);
      expect(component.getDiscountPercentage(1000, 1000)).toBe(0);
    });

    it('should truncate text correctly', () => {
      const longText = 'This is a very long text that should be truncated';
      expect(component.truncateText(longText, 20)).toBe('This is a very long...');
      expect(component.truncateText('Short text', 20)).toBe('Short text');
    });
  });

  describe('navigation methods', () => {
    it('should navigate to trip detail', () => {
      const tripId = 1;
      component.viewTripDetail(tripId);

      expect(routerSpy.navigate).toHaveBeenCalledWith(['/trips', tripId]);
    });

    it('should navigate to blog detail', () => {
      const blogId = 1;
      component.viewBlogDetail(blogId);

      expect(routerSpy.navigate).toHaveBeenCalledWith(['/blogs', blogId]);
    });

    it('should navigate to all trips', () => {
      component.viewAllTrips();

      expect(routerSpy.navigate).toHaveBeenCalledWith(['/trips']);
    });

    it('should navigate to all blogs', () => {
      component.viewAllBlogs();

      expect(routerSpy.navigate).toHaveBeenCalledWith(['/blogs']);
    });
  });
});
