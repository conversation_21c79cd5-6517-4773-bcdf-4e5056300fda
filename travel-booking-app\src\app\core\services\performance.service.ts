import { Injectable, signal } from '@angular/core';

export interface PerformanceMetrics {
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  firstInputDelay?: number;
  cumulativeLayoutShift?: number;
  timeToInteractive?: number;
}

export interface ImageOptimizationConfig {
  quality?: number;
  format?: 'webp' | 'avif' | 'auto';
  lazy?: boolean;
  placeholder?: string;
  sizes?: string;
}

@Injectable({
  providedIn: 'root'
})
export class PerformanceService {
  private readonly metrics = signal<PerformanceMetrics>({
    loadTime: 0,
    domContentLoaded: 0
  });

  private readonly isOnline = signal<boolean>(true);
  private readonly connectionType = signal<string>('unknown');
  private performanceObserver?: PerformanceObserver;

  constructor() {
    this.initializePerformanceMonitoring();
    this.initializeNetworkMonitoring();
    this.measureCoreWebVitals();
  }

  private initializePerformanceMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Basic performance metrics
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      this.metrics.update(current => ({
        ...current,
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart
      }));
    });

    // Performance Observer for detailed metrics
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.processPerformanceEntry(entry);
        }
      });

      try {
        this.performanceObserver.observe({ entryTypes: ['paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
      } catch (e) {
        console.warn('Performance Observer not fully supported');
      }
    }
  }

  private initializeNetworkMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Online/offline status
    this.isOnline.set(navigator.onLine);
    window.addEventListener('online', () => this.isOnline.set(true));
    window.addEventListener('offline', () => this.isOnline.set(false));

    // Connection type
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    if (connection) {
      this.connectionType.set(connection.effectiveType || 'unknown');
      connection.addEventListener('change', () => {
        this.connectionType.set(connection.effectiveType || 'unknown');
      });
    }
  }

  private measureCoreWebVitals(): void {
    // First Contentful Paint
    this.observePerformanceEntry('paint', (entry) => {
      if (entry.name === 'first-contentful-paint') {
        this.metrics.update(current => ({
          ...current,
          firstContentfulPaint: entry.startTime
        }));
      }
    });

    // Largest Contentful Paint
    this.observePerformanceEntry('largest-contentful-paint', (entry) => {
      this.metrics.update(current => ({
        ...current,
        largestContentfulPaint: entry.startTime
      }));
    });

    // First Input Delay
    this.observePerformanceEntry('first-input', (entry: any) => {
      this.metrics.update(current => ({
        ...current,
        firstInputDelay: entry.processingStart - entry.startTime
      }));
    });

    // Cumulative Layout Shift
    let clsValue = 0;
    this.observePerformanceEntry('layout-shift', (entry: any) => {
      if (!entry.hadRecentInput) {
        clsValue += entry.value;
        this.metrics.update(current => ({
          ...current,
          cumulativeLayoutShift: clsValue
        }));
      }
    });
  }

  private observePerformanceEntry(entryType: string, callback: (entry: PerformanceEntry) => void): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          callback(entry);
        }
      });
      observer.observe({ entryTypes: [entryType] });
    } catch (e) {
      console.warn(`Performance Observer for ${entryType} not supported`);
    }
  }

  private processPerformanceEntry(entry: PerformanceEntry): void {
    // Process different types of performance entries
    switch (entry.entryType) {
      case 'paint':
        if (entry.name === 'first-contentful-paint') {
          this.metrics.update(current => ({
            ...current,
            firstContentfulPaint: entry.startTime
          }));
        }
        break;
      case 'largest-contentful-paint':
        this.metrics.update(current => ({
          ...current,
          largestContentfulPaint: entry.startTime
        }));
        break;
    }
  }

  // Image optimization utilities
  optimizeImageUrl(url: string, config: ImageOptimizationConfig = {}): string {
    if (!url) return url;

    const {
      quality = 80,
      format = 'auto',
      sizes = '100vw'
    } = config;

    // Simple URL optimization (in a real app, you'd use a service like Cloudinary or ImageKit)
    const params = new URLSearchParams();
    params.set('q', quality.toString());
    
    if (format !== 'auto') {
      params.set('f', format);
    }

    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}${params.toString()}`;
  }

  // Lazy loading utilities
  createIntersectionObserver(
    callback: (entries: IntersectionObserverEntry[]) => void,
    options: IntersectionObserverInit = {}
  ): IntersectionObserver | null {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return null;
    }

    return new IntersectionObserver(callback, {
      rootMargin: '50px 0px',
      threshold: 0.01,
      ...options
    });
  }

  // Resource preloading
  preloadResource(url: string, type: 'image' | 'script' | 'style' | 'font' = 'image'): void {
    if (typeof document === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = url;
    link.as = type;

    if (type === 'font') {
      link.crossOrigin = 'anonymous';
    }

    document.head.appendChild(link);
  }

  // Critical resource hints
  addResourceHint(url: string, type: 'dns-prefetch' | 'preconnect' | 'prefetch'): void {
    if (typeof document === 'undefined') return;

    const link = document.createElement('link');
    link.rel = type;
    link.href = url;

    if (type === 'preconnect') {
      link.crossOrigin = 'anonymous';
    }

    document.head.appendChild(link);
  }

  // Memory usage monitoring
  getMemoryUsage(): any {
    if (typeof window === 'undefined' || !(performance as any).memory) {
      return null;
    }

    const memory = (performance as any).memory;
    return {
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      usagePercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    };
  }

  // Bundle size analysis
  analyzeResourceTiming(): any[] {
    if (typeof window === 'undefined') return [];

    const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    
    return resources.map(resource => ({
      name: resource.name,
      type: this.getResourceType(resource.name),
      size: resource.transferSize || 0,
      duration: resource.responseEnd - resource.requestStart,
      cached: resource.transferSize === 0 && resource.decodedBodySize > 0
    }));
  }

  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script';
    if (url.includes('.css')) return 'stylesheet';
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return 'image';
    if (url.match(/\.(woff|woff2|ttf|eot)$/i)) return 'font';
    return 'other';
  }

  // Performance budget checking
  checkPerformanceBudget(): {
    passed: boolean;
    metrics: any;
    recommendations: string[];
  } {
    const current = this.metrics();
    const recommendations: string[] = [];
    let passed = true;

    // Check FCP (should be < 1.8s)
    if (current.firstContentfulPaint && current.firstContentfulPaint > 1800) {
      passed = false;
      recommendations.push('First Contentful Paint is too slow. Consider optimizing critical resources.');
    }

    // Check LCP (should be < 2.5s)
    if (current.largestContentfulPaint && current.largestContentfulPaint > 2500) {
      passed = false;
      recommendations.push('Largest Contentful Paint is too slow. Optimize your largest content element.');
    }

    // Check FID (should be < 100ms)
    if (current.firstInputDelay && current.firstInputDelay > 100) {
      passed = false;
      recommendations.push('First Input Delay is too high. Reduce JavaScript execution time.');
    }

    // Check CLS (should be < 0.1)
    if (current.cumulativeLayoutShift && current.cumulativeLayoutShift > 0.1) {
      passed = false;
      recommendations.push('Cumulative Layout Shift is too high. Ensure elements have defined dimensions.');
    }

    return {
      passed,
      metrics: current,
      recommendations
    };
  }

  // Getters for reactive data
  getMetrics() {
    return this.metrics.asReadonly();
  }

  getNetworkStatus() {
    return {
      isOnline: this.isOnline.asReadonly(),
      connectionType: this.connectionType.asReadonly()
    };
  }

  // Cleanup
  destroy(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
  }
}
