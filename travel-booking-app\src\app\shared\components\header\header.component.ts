import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { AuthService, StateService } from '../../../core/services';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <header class="header" [class.scrolled]="isScrolled()">
      <div class="container">
        <div class="header-content">
          <!-- Logo -->
          <div class="logo">
            <a routerLink="/home" class="logo-link">
              <span class="logo-icon">✈️</span>
              <span class="logo-text">TravelBooking</span>
            </a>
          </div>

          <!-- Desktop Navigation -->
          <nav class="desktop-nav">
            <ul class="nav-list">
              <li><a routerLink="/home" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">Home</a></li>
              <li><a routerLink="/trips" routerLinkActive="active">Trips</a></li>
              <li><a routerLink="/blogs" routerLinkActive="active">Blog</a></li>
              @if (isAuthenticated()) {
                <li><a routerLink="/dashboard" routerLinkActive="active">Dashboard</a></li>
                @if (isAdmin()) {
                  <li><a routerLink="/admin" routerLinkActive="active" class="admin-link">Admin</a></li>
                }
              }
            </ul>
          </nav>

          <!-- User Actions -->
          <div class="user-actions">
            @if (isAuthenticated()) {
              <div class="user-menu" [class.open]="userMenuOpen()">
                <button class="user-button" (click)="toggleUserMenu()">
                  <div class="user-avatar">
                    <span>{{ getUserInitials() }}</span>
                  </div>
                  <span class="user-name">{{ currentUser()?.firstName }}</span>
                  <span class="dropdown-arrow">▼</span>
                </button>
                
                <div class="user-dropdown">
                  <div class="dropdown-header">
                    <div class="user-info">
                      <div class="user-avatar large">
                        <span>{{ getUserInitials() }}</span>
                      </div>
                      <div class="user-details">
                        <div class="user-full-name">{{ currentUser()?.firstName }} {{ currentUser()?.lastName }}</div>
                        <div class="user-email">{{ currentUser()?.email }}</div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="dropdown-menu">
                    <a routerLink="/dashboard" class="dropdown-item" (click)="closeUserMenu()">
                      <span class="item-icon">📊</span>
                      Dashboard
                    </a>
                    <a routerLink="/profile" class="dropdown-item" (click)="closeUserMenu()">
                      <span class="item-icon">👤</span>
                      Profile
                    </a>
                    <a routerLink="/bookings" class="dropdown-item" (click)="closeUserMenu()">
                      <span class="item-icon">🎒</span>
                      My Bookings
                    </a>
                    @if (isAdmin()) {
                      <div class="dropdown-divider"></div>
                      <a routerLink="/admin" class="dropdown-item admin" (click)="closeUserMenu()">
                        <span class="item-icon">⚙️</span>
                        Admin Panel
                      </a>
                    }
                    <div class="dropdown-divider"></div>
                    <button class="dropdown-item logout" (click)="logout()">
                      <span class="item-icon">🚪</span>
                      Sign Out
                    </button>
                  </div>
                </div>
              </div>
            } @else {
              <div class="auth-buttons">
                <a routerLink="/auth/login" class="btn btn-outline">Sign In</a>
                <a routerLink="/auth/register" class="btn btn-primary">Sign Up</a>
              </div>
            }

            <!-- Theme Toggle -->
            <button class="theme-toggle" (click)="toggleTheme()" title="Toggle theme">
              {{ isDarkTheme() ? '☀️' : '🌙' }}
            </button>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" (click)="toggleMobileMenu()">
              <span class="hamburger" [class.open]="mobileMenuOpen()">
                <span></span>
                <span></span>
                <span></span>
              </span>
            </button>
          </div>
        </div>

        <!-- Mobile Navigation -->
        <nav class="mobile-nav" [class.open]="mobileMenuOpen()">
          <ul class="mobile-nav-list">
            <li><a routerLink="/home" (click)="closeMobileMenu()">Home</a></li>
            <li><a routerLink="/trips" (click)="closeMobileMenu()">Trips</a></li>
            <li><a routerLink="/blogs" (click)="closeMobileMenu()">Blog</a></li>
            @if (isAuthenticated()) {
              <li><a routerLink="/dashboard" (click)="closeMobileMenu()">Dashboard</a></li>
              <li><a routerLink="/profile" (click)="closeMobileMenu()">Profile</a></li>
              <li><a routerLink="/bookings" (click)="closeMobileMenu()">My Bookings</a></li>
              @if (isAdmin()) {
                <li><a routerLink="/admin" (click)="closeMobileMenu()" class="admin-link">Admin Panel</a></li>
              }
              <li><button class="mobile-logout" (click)="logout()">Sign Out</button></li>
            } @else {
              <li><a routerLink="/auth/login" (click)="closeMobileMenu()">Sign In</a></li>
              <li><a routerLink="/auth/register" (click)="closeMobileMenu()">Sign Up</a></li>
            }
          </ul>
        </nav>
      </div>
    </header>
  `,
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent {
  private readonly authService = inject(AuthService);
  private readonly stateService = inject(StateService);
  private readonly router = inject(Router);

  // Signals
  isScrolled = signal<boolean>(false);
  userMenuOpen = signal<boolean>(false);
  mobileMenuOpen = signal<boolean>(false);

  // Computed properties
  isAuthenticated = this.authService.isAuthenticated;
  currentUser = this.authService.currentUser;
  isDarkTheme = this.stateService.theme;

  constructor() {
    // Listen for scroll events
    if (typeof window !== 'undefined') {
      window.addEventListener('scroll', () => {
        this.isScrolled.set(window.scrollY > 50);
      });

      // Close menus when clicking outside
      document.addEventListener('click', (event) => {
        const target = event.target as HTMLElement;
        if (!target.closest('.user-menu')) {
          this.userMenuOpen.set(false);
        }
        if (!target.closest('.mobile-nav') && !target.closest('.mobile-menu-toggle')) {
          this.mobileMenuOpen.set(false);
        }
      });
    }
  }

  isAdmin(): boolean {
    return this.authService.isAdmin();
  }

  getUserInitials(): string {
    const user = this.currentUser();
    if (!user) return '';
    return `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`.toUpperCase();
  }

  toggleUserMenu(): void {
    this.userMenuOpen.update(open => !open);
  }

  closeUserMenu(): void {
    this.userMenuOpen.set(false);
  }

  toggleMobileMenu(): void {
    this.mobileMenuOpen.update(open => !open);
  }

  closeMobileMenu(): void {
    this.mobileMenuOpen.set(false);
  }

  toggleTheme(): void {
    this.stateService.toggleTheme();
  }

  logout(): void {
    this.closeUserMenu();
    this.closeMobileMenu();
    this.authService.logout();
  }
}
