.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.auth-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    padding: 30px 25px;
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 10px;
  }

  p {
    color: #718096;
    font-size: 1rem;
  }
}

.auth-form {
  .form-group {
    margin-bottom: 20px;

    label {
      display: block;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 8px;
      font-size: 0.9rem;
    }

    input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 10px;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: #f7fafc;

      &:focus {
        outline: none;
        border-color: #667eea;
        background: white;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      &.error {
        border-color: #e53e3e;
        background: #fed7d7;
      }

      &::placeholder {
        color: #a0aec0;
      }
    }

    .password-input {
      position: relative;

      .password-toggle {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
        font-size: 1.2rem;
        color: #718096;
        padding: 4px;

        &:hover {
          color: #4a5568;
        }
      }
    }

    .error-message {
      color: #e53e3e;
      font-size: 0.8rem;
      margin-top: 5px;
      font-weight: 500;
    }
  }

  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;

    @media (max-width: 480px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 0.9rem;
      color: #4a5568;

      input[type="checkbox"] {
        display: none;
      }

      .checkmark {
        width: 18px;
        height: 18px;
        border: 2px solid #e2e8f0;
        border-radius: 4px;
        margin-right: 8px;
        position: relative;
        transition: all 0.3s ease;

        &::after {
          content: '';
          position: absolute;
          left: 5px;
          top: 2px;
          width: 4px;
          height: 8px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
      }

      input[type="checkbox"]:checked + .checkmark {
        background: #667eea;
        border-color: #667eea;

        &::after {
          opacity: 1;
        }
      }
    }

    .forgot-link {
      color: #667eea;
      text-decoration: none;
      font-size: 0.9rem;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 0.9rem;
    font-weight: 500;

    &.alert-error {
      background: #fed7d7;
      color: #c53030;
      border: 1px solid #feb2b2;
    }
  }

  .btn-full {
    width: 100%;
    padding: 14px;
    font-size: 1rem;
    font-weight: 600;
    position: relative;

    .spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
      margin-right: 8px;
    }
  }
}

.auth-footer {
  text-align: center;
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;

  p {
    color: #718096;
    font-size: 0.9rem;

    a {
      color: #667eea;
      text-decoration: none;
      font-weight: 600;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.divider {
  text-align: center;
  margin: 25px 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e2e8f0;
  }

  span {
    background: white;
    padding: 0 15px;
    color: #718096;
    font-size: 0.9rem;
    position: relative;
  }
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .btn-social {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    background: white;
    color: #4a5568;
    font-weight: 600;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      border-color: #cbd5e0;
      background: #f7fafc;
    }

    .social-icon {
      font-size: 1.2rem;
    }

    &.google:hover {
      border-color: #db4437;
      color: #db4437;
    }

    &.facebook:hover {
      border-color: #4267b2;
      color: #4267b2;
    }
  }
}

.btn {
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover:not(:disabled) {
      background: #5a67d8;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
