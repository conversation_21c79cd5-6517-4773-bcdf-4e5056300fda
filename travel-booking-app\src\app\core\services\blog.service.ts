import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  Blog, 
  BlogCategory, 
  BlogTag, 
  CreateBlogDto, 
  UpdateBlogDto, 
  BlogFilterDto 
} from '../models/blog.model';
import { PagedResult, PaginationParameters } from '../models/common.model';

@Injectable({
  providedIn: 'root'
})
export class BlogService {
  private readonly apiService = inject(ApiService);

  // Public blog methods
  getBlogs(pagination: PaginationParameters, filters?: BlogFilterDto): Observable<PagedResult<Blog>> {
    return this.apiService.getPaged<Blog>('blogs', pagination, filters);
  }

  getBlogById(id: number): Observable<Blog> {
    return this.apiService.get<Blog>(`blogs/${id}`);
  }

  getBlogBySlug(slug: string): Observable<Blog> {
    return this.apiService.get<Blog>(`blogs/slug/${slug}`);
  }

  getFeaturedBlogs(): Observable<Blog[]> {
    return this.apiService.get<Blog[]>('blogs/featured');
  }

  getBlogCategories(): Observable<BlogCategory[]> {
    return this.apiService.get<BlogCategory[]>('blogs/categories');
  }

  getBlogTags(): Observable<BlogTag[]> {
    return this.apiService.get<BlogTag[]>('blogs/tags');
  }

  searchBlogs(searchTerm: string, filters?: BlogFilterDto): Observable<PagedResult<Blog>> {
    const searchFilters = { ...filters, search: searchTerm };
    const pagination: PaginationParameters = { page: 1, pageSize: 20 };
    return this.getBlogs(pagination, searchFilters);
  }

  incrementViewCount(id: number): Observable<void> {
    return this.apiService.post<void>(`blogs/${id}/view`, {});
  }

  // Admin blog methods
  getAdminBlogs(pagination: PaginationParameters, filters?: BlogFilterDto): Observable<PagedResult<Blog>> {
    return this.apiService.getPaged<Blog>('admin/blogs', pagination, filters);
  }

  createBlog(blogData: CreateBlogDto): Observable<Blog> {
    return this.apiService.post<Blog>('admin/blogs', blogData);
  }

  updateBlog(blogData: UpdateBlogDto): Observable<Blog> {
    return this.apiService.put<Blog>(`admin/blogs/${blogData.id}`, blogData);
  }

  deleteBlog(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/blogs/${id}`);
  }

  publishBlog(id: number): Observable<Blog> {
    return this.apiService.put<Blog>(`admin/blogs/${id}/publish`, {});
  }

  unpublishBlog(id: number): Observable<Blog> {
    return this.apiService.put<Blog>(`admin/blogs/${id}/unpublish`, {});
  }

  toggleFeaturedStatus(id: number): Observable<Blog> {
    return this.apiService.put<Blog>(`admin/blogs/${id}/toggle-featured`, {});
  }

  // Category management
  createBlogCategory(categoryData: Omit<BlogCategory, 'id' | 'createdAt' | 'updatedAt'>): Observable<BlogCategory> {
    return this.apiService.post<BlogCategory>('admin/blog-categories', categoryData);
  }

  updateBlogCategory(categoryData: BlogCategory): Observable<BlogCategory> {
    return this.apiService.put<BlogCategory>(`admin/blog-categories/${categoryData.id}`, categoryData);
  }

  deleteBlogCategory(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/blog-categories/${id}`);
  }

  // Tag management
  createBlogTag(tagData: Omit<BlogTag, 'id' | 'createdAt'>): Observable<BlogTag> {
    return this.apiService.post<BlogTag>('admin/blog-tags', tagData);
  }

  updateBlogTag(tagData: BlogTag): Observable<BlogTag> {
    return this.apiService.put<BlogTag>(`admin/blog-tags/${tagData.id}`, tagData);
  }

  deleteBlogTag(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/blog-tags/${id}`);
  }

  // Image management
  uploadBlogImage(blogId: number, file: File): Observable<any> {
    return this.apiService.uploadFile(`admin/blogs/${blogId}/images`, file);
  }

  deleteBlogImage(blogId: number, imageId: number): Observable<void> {
    return this.apiService.delete<void>(`admin/blogs/${blogId}/images/${imageId}`);
  }

  uploadFeaturedImage(blogId: number, file: File): Observable<any> {
    return this.apiService.uploadFile(`admin/blogs/${blogId}/featured-image`, file);
  }

  // Blog statistics
  getBlogStats(blogId: number): Observable<any> {
    return this.apiService.get(`admin/blogs/${blogId}/stats`);
  }

  getOverallBlogStats(): Observable<any> {
    return this.apiService.get('admin/blogs/stats');
  }

  getPopularBlogs(limit: number = 10): Observable<Blog[]> {
    return this.apiService.get<Blog[]>(`admin/blogs/popular?limit=${limit}`);
  }

  // Bulk operations
  bulkUpdateBlogs(blogIds: number[], updateData: Partial<Blog>): Observable<void> {
    return this.apiService.put<void>('admin/blogs/bulk-update', { blogIds, updateData });
  }

  bulkDeleteBlogs(blogIds: number[]): Observable<void> {
    return this.apiService.delete<void>('admin/blogs/bulk-delete', { blogIds });
  }

  bulkPublishBlogs(blogIds: number[]): Observable<void> {
    return this.apiService.put<void>('admin/blogs/bulk-publish', { blogIds });
  }

  bulkUnpublishBlogs(blogIds: number[]): Observable<void> {
    return this.apiService.put<void>('admin/blogs/bulk-unpublish', { blogIds });
  }

  // SEO and content optimization
  generateSlug(title: string): Observable<string> {
    return this.apiService.post<string>('admin/blogs/generate-slug', { title });
  }

  validateSlug(slug: string, blogId?: number): Observable<boolean> {
    return this.apiService.post<boolean>('admin/blogs/validate-slug', { slug, blogId });
  }

  calculateReadingTime(content: string): Observable<number> {
    return this.apiService.post<number>('admin/blogs/calculate-reading-time', { content });
  }

  // Export functionality
  exportBlogs(filters?: BlogFilterDto): Observable<Blob> {
    return this.apiService.getRaw<Blob>('admin/blogs/export', filters);
  }

  // Related content
  getRelatedBlogs(blogId: number, limit: number = 5): Observable<Blog[]> {
    return this.apiService.get<Blog[]>(`blogs/${blogId}/related?limit=${limit}`);
  }

  getBlogsByCategory(categoryId: number, pagination: PaginationParameters): Observable<PagedResult<Blog>> {
    return this.apiService.getPaged<Blog>(`blogs/category/${categoryId}`, pagination);
  }

  getBlogsByTag(tagId: number, pagination: PaginationParameters): Observable<PagedResult<Blog>> {
    return this.apiService.getPaged<Blog>(`blogs/tag/${tagId}`, pagination);
  }

  getBlogsByAuthor(authorId: number, pagination: PaginationParameters): Observable<PagedResult<Blog>> {
    return this.apiService.getPaged<Blog>(`blogs/author/${authorId}`, pagination);
  }
}
