import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-admin-users',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="admin-users-container">
      <div class="container">
        <h1>User Management</h1>
        <p>Admin user management will be implemented here</p>
        <a routerLink="/admin" class="btn btn-primary">Back to Admin Dashboard</a>
      </div>
    </div>
  `,
  styles: [`
    .admin-users-container {
      min-height: 100vh;
      background: #f1f5f9;
      padding: 40px 0;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
      background: white;
      border-radius: 15px;
      text-align: center;
    }
    h1 {
      font-size: 2rem;
      color: #1e293b;
      margin-bottom: 15px;
    }
    p {
      color: #64748b;
      margin-bottom: 30px;
    }
    .btn {
      padding: 12px 24px;
      background: #3b82f6;
      color: white;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
    }
  `]
})
export class AdminUsersComponent {}
