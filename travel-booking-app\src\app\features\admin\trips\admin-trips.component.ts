import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { TripService, CategoryService, NotificationService } from '../../../core/services';
import { Trip, Category, TripFilterDto, PaginationParameters } from '../../../core/models';

@Component({
  selector: 'app-admin-trips',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <div class="admin-trips-container">
      <div class="container">
        <!-- Header -->
        <div class="page-header">
          <div class="header-content">
            <h1>Trip Management</h1>
            <p>Manage travel packages and destinations</p>
          </div>
          <div class="header-actions">
            <button class="btn btn-primary" (click)="openCreateModal()">
              + Add New Trip
            </button>
            <a routerLink="/admin" class="btn btn-outline">
              ← Back to Dashboard
            </a>
          </div>
        </div>

        <!-- Filters and Search -->
        <div class="filters-section">
          <div class="search-bar">
            <input
              type="text"
              placeholder="Search trips..."
              [(ngModel)]="searchQuery"
              (input)="onSearchChange()"
              class="search-input"
            />
          </div>

          <div class="filters-row">
            <div class="filter-group">
              <label>Category</label>
              <select [(ngModel)]="selectedCategory" (change)="onFilterChange()">
                <option value="">All Categories</option>
                @for (category of categories(); track category.id) {
                  <option [value]="category.id">{{ category.name }}</option>
                }
              </select>
            </div>

            <div class="filter-group">
              <label>Status</label>
              <select [(ngModel)]="selectedStatus" (change)="onFilterChange()">
                <option value="">All Status</option>
                <option value="true">Active</option>
                <option value="false">Inactive</option>
              </select>
            </div>

            <div class="filter-group">
              <label>Sort By</label>
              <select [(ngModel)]="sortBy" (change)="onFilterChange()">
                <option value="createdAt">Created Date</option>
                <option value="name">Name</option>
                <option value="price">Price</option>
                <option value="duration">Duration</option>
              </select>
            </div>

            <div class="filter-group">
              <label>Order</label>
              <select [(ngModel)]="sortOrder" (change)="onFilterChange()">
                <option value="desc">Newest First</option>
                <option value="asc">Oldest First</option>
              </select>
            </div>
          </div>

          <div class="results-info">
            <span>{{ totalResults() }} trips found</span>
            <button class="btn btn-sm btn-outline" (click)="clearFilters()">
              Clear Filters
            </button>
          </div>
        </div>

        <!-- Trips Table -->
        <div class="trips-section">
          @if (isLoading()) {
            <div class="loading-state">
              <div class="spinner"></div>
              <p>Loading trips...</p>
            </div>
          } @else if (trips().length === 0) {
            <div class="empty-state">
              <div class="empty-icon">🎒</div>
              <h3>No trips found</h3>
              <p>No trips match your current filters. Try adjusting your search criteria.</p>
              <button class="btn btn-primary" (click)="openCreateModal()">
                Create First Trip
              </button>
            </div>
          } @else {
            <div class="trips-table-container">
              <table class="trips-table">
                <thead>
                  <tr>
                    <th>Trip</th>
                    <th>Category</th>
                    <th>Duration</th>
                    <th>Price</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  @for (trip of trips(); track trip.id) {
                    <tr>
                      <td class="trip-info">
                        <div class="trip-image">
                          <img [src]="trip.mainImageUrl" [alt]="trip.name" />
                        </div>
                        <div class="trip-details">
                          <div class="trip-name">{{ trip.name }}</div>
                          <div class="trip-location">{{ trip.destinationCity.name }}, {{ trip.destinationCity.country.name }}</div>
                        </div>
                      </td>
                      <td>
                        <span class="category-badge">{{ trip.category.name }}</span>
                      </td>
                      <td>{{ trip.duration }} days</td>
                      <td class="price">\${{ trip.price | number:'1.2-2' }}</td>
                      <td>
                        <span class="status-badge" [class]="trip.isActive ? 'active' : 'inactive'">
                          {{ trip.isActive ? 'Active' : 'Inactive' }}
                        </span>
                      </td>
                      <td>{{ trip.createdAt | date:'shortDate' }}</td>
                      <td class="actions">
                        <button class="btn btn-sm btn-outline" (click)="editTrip(trip)">
                          Edit
                        </button>
                        <button class="btn btn-sm btn-secondary" (click)="viewTrip(trip.id)">
                          View
                        </button>
                        <button
                          class="btn btn-sm"
                          [class.btn-danger]="trip.isActive"
                          [class.btn-success]="!trip.isActive"
                          (click)="toggleTripStatus(trip)"
                        >
                          {{ trip.isActive ? 'Deactivate' : 'Activate' }}
                        </button>
                        <button class="btn btn-sm btn-danger" (click)="deleteTrip(trip)">
                          Delete
                        </button>
                      </td>
                    </tr>
                  }
                </tbody>
              </table>
            </div>
          }
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./admin-trips.component.scss']
})
export class AdminTripsComponent implements OnInit {
  private readonly tripService = inject(TripService);
  private readonly categoryService = inject(CategoryService);
  private readonly notificationService = inject(NotificationService);

  // Data signals
  trips = signal<Trip[]>([]);
  categories = signal<Category[]>([]);
  isLoading = signal<boolean>(false);

  // Filter signals
  searchQuery = signal<string>('');
  selectedCategory = signal<string>('');
  selectedStatus = signal<string>('');
  sortBy = signal<string>('createdAt');
  sortOrder = signal<string>('desc');

  // Pagination signals
  currentPage = signal<number>(1);
  totalResults = signal<number>(0);
  totalPages = signal<number>(0);

  ngOnInit(): void {
    this.loadCategories();
    this.loadTrips();
  }

  private async loadCategories(): Promise<void> {
    try {
      const categories = await this.categoryService.getCategories().toPromise();
      this.categories.set(categories || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  }

  private async loadTrips(): Promise<void> {
    this.isLoading.set(true);

    try {
      const filters = this.buildFilters();
      const pagination: PaginationParameters = {
        page: this.currentPage(),
        pageSize: 10,
        sortBy: this.sortBy(),
        sortOrder: this.sortOrder() as 'asc' | 'desc'
      };

      const result = await this.tripService.getTrips(pagination, filters).toPromise();

      if (result) {
        this.trips.set(result.items);
        this.totalResults.set(result.totalCount);
        this.totalPages.set(Math.ceil(result.totalCount / 10));
      }
    } catch (error) {
      console.error('Error loading trips:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  private buildFilters(): TripFilterDto {
    const filters: TripFilterDto = {};

    if (this.searchQuery()) {
      filters.searchTerm = this.searchQuery();
    }

    if (this.selectedCategory()) {
      filters.categoryId = Number(this.selectedCategory());
    }

    if (this.selectedStatus()) {
      filters.isActive = this.selectedStatus() === 'true';
    }

    return filters;
  }

  onSearchChange(): void {
    this.currentPage.set(1);
    this.loadTrips();
  }

  onFilterChange(): void {
    this.currentPage.set(1);
    this.loadTrips();
  }

  clearFilters(): void {
    this.searchQuery.set('');
    this.selectedCategory.set('');
    this.selectedStatus.set('');
    this.sortBy.set('createdAt');
    this.sortOrder.set('desc');
    this.currentPage.set(1);
    this.loadTrips();
  }

  openCreateModal(): void {
    // TODO: Implement create modal
    this.notificationService.showInfo('Create trip feature coming soon!');
  }

  editTrip(trip: Trip): void {
    // TODO: Implement edit modal
    this.notificationService.showInfo('Edit trip feature coming soon!');
  }

  viewTrip(tripId: number): void {
    // Navigate to trip detail page
    window.open(`/trips/${tripId}`, '_blank');
  }

  async toggleTripStatus(trip: Trip): Promise<void> {
    try {
      const updatedTrip = { ...trip, isActive: !trip.isActive };
      await this.tripService.updateTrip(trip.id, updatedTrip).toPromise();

      this.notificationService.showSuccess(
        `Trip ${updatedTrip.isActive ? 'activated' : 'deactivated'} successfully!`
      );

      this.loadTrips();
    } catch (error: any) {
      this.notificationService.showError(
        error?.error?.message || 'An error occurred while updating the trip'
      );
    }
  }

  async deleteTrip(trip: Trip): Promise<void> {
    if (confirm(`Are you sure you want to delete "${trip.name}"? This action cannot be undone.`)) {
      try {
        await this.tripService.deleteTrip(trip.id).toPromise();
        this.notificationService.showSuccess('Trip deleted successfully!');
        this.loadTrips();
      } catch (error: any) {
        this.notificationService.showError(
          error?.error?.message || 'An error occurred while deleting the trip'
        );
      }
    }
  }
}
