export interface Blog {
  id: number;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  author: User;
  category: BlogCategory;
  featuredImageUrl?: string;
  images: BlogImage[];
  tags: BlogTag[];
  isPublished: boolean;
  isFeatured: boolean;
  publishedAt?: Date;
  viewCount: number;
  readingTime: number;
  createdAt: Date;
  updatedAt?: Date;
}

export interface BlogCategory {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

export interface BlogTag {
  id: number;
  name: string;
  createdAt: Date;
}

export interface BlogImage {
  id: number;
  blogId: number;
  imageUrl: string;
  caption?: string;
  displayOrder: number;
  createdAt: Date;
}

export interface CreateBlogDto {
  title: string;
  content: string;
  excerpt: string;
  categoryId: number;
  featuredImageUrl?: string;
  tagIds: number[];
  isPublished: boolean;
  isFeatured: boolean;
}

export interface UpdateBlogDto extends CreateBlogDto {
  id: number;
}

export interface BlogFilterDto {
  search?: string;
  categoryId?: number;
  authorId?: number;
  tagIds?: number[];
  isPublished?: boolean;
  isFeatured?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export enum BlogStatus {
  Draft = 1,
  Published = 2,
  Archived = 3
}

// Import User interface from user.model.ts
import { User } from './user.model';
