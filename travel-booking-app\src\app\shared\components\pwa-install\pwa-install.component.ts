import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PWAService } from '../../../core/services';

@Component({
  selector: 'app-pwa-install',
  standalone: true,
  imports: [CommonModule],
  template: `
    <!-- Install Banner -->
    @if (pwaService.getInstallableStatus()() && !pwaService.getInstalledStatus()()) {
      <div class="install-banner">
        <div class="install-content">
          <div class="install-icon">📱</div>
          <div class="install-text">
            <h3>Install Travel Booking App</h3>
            <p>Get the full experience with our mobile app. Install now for offline access and faster loading!</p>
          </div>
          <div class="install-actions">
            <button class="btn btn-primary" (click)="installApp()">
              Install App
            </button>
            <button class="btn btn-outline" (click)="dismissBanner()">
              Not Now
            </button>
          </div>
        </div>
        <button class="close-btn" (click)="dismissBanner()">×</button>
      </div>
    }

    <!-- Update Banner -->
    @if (pwaService.getUpdateAvailableStatus()()) {
      <div class="update-banner">
        <div class="update-content">
          <div class="update-icon">🔄</div>
          <div class="update-text">
            <h3>Update Available</h3>
            <p>A new version of the app is available with improvements and bug fixes.</p>
          </div>
          <div class="update-actions">
            <button class="btn btn-primary" (click)="updateApp()">
              Update Now
            </button>
            <button class="btn btn-outline" (click)="dismissUpdateBanner()">
              Later
            </button>
          </div>
        </div>
        <button class="close-btn" (click)="dismissUpdateBanner()">×</button>
      </div>
    }

    <!-- Offline Banner -->
    @if (!pwaService.getOnlineStatus()()) {
      <div class="offline-banner">
        <div class="offline-content">
          <div class="offline-icon">📡</div>
          <div class="offline-text">
            <h3>You're Offline</h3>
            <p>Some features may be limited. We'll sync your data when you're back online.</p>
          </div>
        </div>
      </div>
    }

    <!-- Network Status (for debugging) -->
    @if (showNetworkStatus && pwaService.getNetworkStatus()()) {
      <div class="network-status">
        <h4>Network Status</h4>
        <div class="status-grid">
          <div class="status-item">
            <span class="label">Status:</span>
            <span class="value" [class]="pwaService.getOnlineStatus()() ? 'online' : 'offline'">
              {{ pwaService.getOnlineStatus()() ? 'Online' : 'Offline' }}
            </span>
          </div>
          <div class="status-item">
            <span class="label">Connection:</span>
            <span class="value">{{ pwaService.getNetworkStatus()()?.effectiveType || 'Unknown' }}</span>
          </div>
          <div class="status-item">
            <span class="label">Speed:</span>
            <span class="value">{{ pwaService.getNetworkStatus()()?.downlink || 0 }} Mbps</span>
          </div>
        </div>
      </div>
    }
  `,
  styles: [`
    .install-banner,
    .update-banner {
      position: fixed;
      top: 70px;
      left: 50%;
      transform: translateX(-50%);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      z-index: 1000;
      max-width: 500px;
      width: 90%;
      animation: slideDown 0.3s ease-out;

      @media (max-width: 768px) {
        top: 10px;
        width: 95%;
        padding: 15px;
      }
    }

    .update-banner {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .offline-banner {
      position: fixed;
      top: 70px;
      left: 50%;
      transform: translateX(-50%);
      background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
      color: #2d3436;
      padding: 15px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
      z-index: 1000;
      max-width: 400px;
      width: 90%;
      animation: slideDown 0.3s ease-out;

      @media (max-width: 768px) {
        top: 10px;
        width: 95%;
      }
    }

    .install-content,
    .update-content,
    .offline-content {
      display: flex;
      align-items: center;
      gap: 15px;

      @media (max-width: 768px) {
        flex-direction: column;
        text-align: center;
        gap: 10px;
      }
    }

    .install-icon,
    .update-icon,
    .offline-icon {
      font-size: 2rem;
      flex-shrink: 0;
    }

    .install-text,
    .update-text,
    .offline-text {
      flex: 1;

      h3 {
        margin: 0 0 5px 0;
        font-size: 1.1rem;
        font-weight: 600;
      }

      p {
        margin: 0;
        font-size: 0.9rem;
        opacity: 0.9;
      }
    }

    .install-actions,
    .update-actions {
      display: flex;
      gap: 10px;
      flex-shrink: 0;

      @media (max-width: 768px) {
        width: 100%;
        justify-content: center;
      }
    }

    .close-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      background: none;
      border: none;
      color: white;
      font-size: 1.5rem;
      cursor: pointer;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.2s;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }

    .offline-banner .close-btn {
      color: #2d3436;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }

    .btn {
      padding: 8px 16px;
      border-radius: 6px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
      font-size: 0.9rem;

      &.btn-primary {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }

      &.btn-outline {
        background: transparent;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.5);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }
      }
    }

    .network-status {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      min-width: 200px;

      h4 {
        margin: 0 0 10px 0;
        font-size: 0.9rem;
        font-weight: 600;
        color: #2d3748;
      }

      .status-grid {
        display: flex;
        flex-direction: column;
        gap: 5px;
      }

      .status-item {
        display: flex;
        justify-content: space-between;
        font-size: 0.8rem;

        .label {
          color: #718096;
        }

        .value {
          font-weight: 600;

          &.online {
            color: #38a169;
          }

          &.offline {
            color: #e53e3e;
          }
        }
      }
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
      }
    }
  `]
})
export class PWAInstallComponent implements OnInit {
  readonly pwaService = inject(PWAService);
  
  showNetworkStatus = false; // Set to true for debugging

  ngOnInit(): void {
    // Enable network status display in development
    this.showNetworkStatus = !this.isProduction();
  }

  async installApp(): Promise<void> {
    const success = await this.pwaService.installApp();
    if (!success) {
      console.log('Installation was cancelled or failed');
    }
  }

  async updateApp(): Promise<void> {
    await this.pwaService.applyUpdate();
  }

  dismissBanner(): void {
    // Hide the install banner (you might want to store this preference)
    localStorage.setItem('pwa-install-dismissed', Date.now().toString());
  }

  dismissUpdateBanner(): void {
    // Hide the update banner temporarily
    localStorage.setItem('pwa-update-dismissed', Date.now().toString());
  }

  private isProduction(): boolean {
    return window.location.hostname !== 'localhost' && 
           window.location.hostname !== '127.0.0.1';
  }
}
