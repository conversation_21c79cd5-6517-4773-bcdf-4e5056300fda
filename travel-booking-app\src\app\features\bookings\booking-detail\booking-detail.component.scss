.booking-detail-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 40px 0;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
  }
  
  p {
    color: #718096;
    font-size: 1.1rem;
  }
}

// Header
.booking-header {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  .breadcrumb {
    margin-bottom: 20px;
    font-size: 0.9rem;
    color: #718096;

    a {
      color: #667eea;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    span {
      margin: 0 8px;
    }
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
    }

    h1 {
      font-size: 2rem;
      font-weight: 700;
      color: #2d3748;
      margin: 0;
    }

    .booking-status {
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.9rem;
      font-weight: 600;
      color: white;

      &.status-1 { background: #f6ad55; } // Pending
      &.status-2 { background: #38a169; } // Confirmed
      &.status-3 { background: #e53e3e; } // Cancelled
      &.status-4 { background: #4299e1; } // Completed
    }
  }
}

// Content Sections
.booking-content {
  .section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 25px;
      padding-bottom: 10px;
      border-bottom: 2px solid #667eea;
    }
  }
}

// Trip Info
.trip-card {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 30px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }

  .trip-image {
    border-radius: 12px;
    overflow: hidden;
    height: 200px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .trip-details {
    h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 10px;
    }

    p {
      color: #718096;
      margin-bottom: 20px;
      line-height: 1.6;
    }

    .trip-meta {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;

      .meta-item {
        display: flex;
        flex-direction: column;
        gap: 5px;

        .label {
          font-size: 0.8rem;
          color: #718096;
          font-weight: 500;
        }

        .value {
          font-weight: 600;
          color: #2d3748;
        }
      }
    }
  }
}

// Details Grid
.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.detail-card, .contact-card, .requests-card, .payment-card, .cancellation-card {
  background: #f7fafc;
  border-radius: 12px;
  padding: 25px;

  h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 20px;
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e2e8f0;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    &.total {
      font-weight: 600;
      border-top: 2px solid #667eea;
      padding-top: 12px;
      margin-top: 12px;

      .value {
        color: #38a169;
        font-size: 1.1rem;
      }
    }

    .label {
      color: #718096;
      font-weight: 500;
      font-size: 0.9rem;
    }

    .value {
      color: #2d3748;
      font-weight: 600;
      text-align: right;

      &.discount {
        color: #e53e3e;
      }
    }
  }
}

// Payment Status
.payment-status-1 { color: #f6ad55; } // Pending
.payment-status-2 { color: #38a169; } // Paid
.payment-status-3 { color: #e53e3e; } // Failed
.payment-status-4 { color: #4299e1; } // Refunded

// Special Requests
.requests-card {
  p {
    color: #4a5568;
    line-height: 1.6;
    margin: 0;
    font-style: italic;
  }
}

// Cancellation Info
.cancellation-card {
  background: #fed7d7;
  border: 1px solid #feb2b2;
}

// Actions
.booking-actions {
  .actions-grid {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      flex-direction: column;
    }
  }
}

// Error State
.error-container {
  text-align: center;
  padding: 100px 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  h2 {
    font-size: 2rem;
    color: #2d3748;
    margin-bottom: 15px;
  }

  p {
    color: #718096;
    margin-bottom: 30px;
    font-size: 1.1rem;
  }
}

// Button Styles
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  text-align: center;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover {
      background: #5a67d8;
    }
  }

  &.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;

    &:hover {
      background: #667eea;
      color: white;
    }
  }

  &.btn-secondary {
    background: #edf2f7;
    color: #4a5568;

    &:hover {
      background: #e2e8f0;
    }
  }

  &.btn-danger {
    background: #e53e3e;
    color: white;

    &:hover {
      background: #c53030;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
