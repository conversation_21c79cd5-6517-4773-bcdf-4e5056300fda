import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  User, 
  UserProfile, 
  UpdateUserDto, 
  UserFilterDto 
} from '../models/user.model';
import { PagedResult, PaginationParameters } from '../models/common.model';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private readonly apiService = inject(ApiService);

  // User profile methods
  getUserProfile(): Observable<UserProfile> {
    return this.apiService.get<UserProfile>('users/profile');
  }

  updateUserProfile(userData: UpdateUserDto): Observable<UserProfile> {
    return this.apiService.put<UserProfile>('users/profile', userData);
  }

  deleteUserAccount(): Observable<void> {
    return this.apiService.delete<void>('users/account');
  }

  uploadProfileImage(file: File): Observable<any> {
    return this.apiService.uploadFile('users/profile/image', file);
  }

  // Admin user management methods
  getAdminUsers(pagination: PaginationParameters, filters?: UserFilterDto): Observable<PagedResult<User>> {
    return this.apiService.getPaged<User>('admin/users', pagination, filters);
  }

  getUserById(id: number): Observable<User> {
    return this.apiService.get<User>(`admin/users/${id}`);
  }

  updateUser(id: number, userData: UpdateUserDto): Observable<User> {
    return this.apiService.put<User>(`admin/users/${id}`, userData);
  }

  toggleUserStatus(id: number): Observable<User> {
    return this.apiService.put<User>(`admin/users/${id}/toggle-status`, {});
  }

  deleteUser(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/users/${id}`);
  }

  // User statistics
  getUserStats(): Observable<any> {
    return this.apiService.get('admin/users/stats');
  }

  getUserRegistrationStats(year: number): Observable<any> {
    return this.apiService.get(`admin/users/stats/registrations/${year}`);
  }

  // Bulk operations
  bulkUpdateUsers(userIds: number[], updateData: Partial<User>): Observable<void> {
    return this.apiService.put<void>('admin/users/bulk-update', { userIds, updateData });
  }

  bulkDeleteUsers(userIds: number[]): Observable<void> {
    return this.apiService.delete<void>('admin/users/bulk-delete', { userIds });
  }

  // Export functionality
  exportUsers(filters?: UserFilterDto): Observable<Blob> {
    return this.apiService.getRaw<Blob>('admin/users/export', filters);
  }

  // Search and filter
  searchUsers(searchTerm: string, filters?: UserFilterDto): Observable<PagedResult<User>> {
    const searchFilters = { ...filters, search: searchTerm };
    const pagination: PaginationParameters = { page: 1, pageSize: 20 };
    return this.getAdminUsers(pagination, searchFilters);
  }
}
