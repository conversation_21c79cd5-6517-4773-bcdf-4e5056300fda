.notification-container {
  position: fixed;
  top: 90px; // Below header
  right: 20px;
  z-index: 1050;
  max-width: 400px;
  width: 100%;

  @media (max-width: 480px) {
    left: 20px;
    right: 20px;
    max-width: none;
  }
}

.notification {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  margin-bottom: 15px;
  overflow: hidden;
  position: relative;
  border-left: 4px solid;
  animation: slideIn 0.3s ease-out;

  &.notification-success {
    border-left-color: #38a169;
  }

  &.notification-error {
    border-left-color: #e53e3e;
  }

  &.notification-warning {
    border-left-color: #f6ad55;
  }

  &.notification-info {
    border-left-color: #4299e1;
  }

  .notification-content {
    display: flex;
    align-items: flex-start;
    padding: 16px 20px;
    gap: 12px;
    position: relative;
  }

  .notification-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .notification-text {
    flex: 1;
    min-width: 0;

    .notification-title {
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 4px;
      font-size: 0.95rem;
    }

    .notification-message {
      color: #4a5568;
      font-size: 0.9rem;
      line-height: 1.4;
      word-wrap: break-word;
    }
  }

  .notification-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;

    .notification-action {
      padding: 6px 12px;
      border: none;
      border-radius: 6px;
      font-size: 0.8rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &.action-primary {
        background: #667eea;
        color: white;

        &:hover {
          background: #5a67d8;
        }
      }

      &.action-secondary {
        background: #edf2f7;
        color: #4a5568;

        &:hover {
          background: #e2e8f0;
        }
      }
    }
  }

  .notification-close {
    position: absolute;
    top: 12px;
    right: 12px;
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    font-size: 0.9rem;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: #f7fafc;
      color: #4a5568;
    }
  }

  .notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: currentColor;
    opacity: 0.3;
    animation: progress linear;
    transform-origin: left;
  }

  &.notification-success .notification-progress {
    background: #38a169;
  }

  &.notification-error .notification-progress {
    background: #e53e3e;
  }

  &.notification-warning .notification-progress {
    background: #f6ad55;
  }

  &.notification-info .notification-progress {
    background: #4299e1;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes progress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

// Dark theme support
@media (prefers-color-scheme: dark) {
  .notification {
    background: #2d3748;
    color: #e2e8f0;

    .notification-text {
      .notification-title {
        color: #f7fafc;
      }

      .notification-message {
        color: #cbd5e0;
      }
    }

    .notification-close {
      color: #718096;

      &:hover {
        background: #4a5568;
        color: #e2e8f0;
      }
    }

    .notification-actions {
      .notification-action {
        &.action-secondary {
          background: #4a5568;
          color: #e2e8f0;

          &:hover {
            background: #718096;
          }
        }
      }
    }
  }
}
