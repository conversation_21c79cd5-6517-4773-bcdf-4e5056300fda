// Additional styles specific to register component
.auth-card {
  max-width: 500px; // Slightly wider for register form
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .form-group {
    margin-bottom: 0;

    @media (max-width: 480px) {
      margin-bottom: 20px;
    }
  }
}

.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 10px;

  .strength-bar {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: #e2e8f0;
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      border-radius: 2px;
      transition: all 0.3s ease;
    }

    &.weak::after {
      width: 33%;
      background: #e53e3e;
    }

    &.medium::after {
      width: 66%;
      background: #f6ad55;
    }

    &.strong::after {
      width: 100%;
      background: #38a169;
    }
  }

  .strength-text {
    font-size: 0.8rem;
    font-weight: 500;
    min-width: 60px;

    &.weak {
      color: #e53e3e;
    }

    &.medium {
      color: #f6ad55;
    }

    &.strong {
      color: #38a169;
    }
  }
}

.checkbox-label {
  font-size: 0.9rem;
  line-height: 1.4;

  a {
    color: #667eea;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

// Override some styles for better spacing in register form
.form-group {
  margin-bottom: 20px;

  &:last-of-type {
    margin-bottom: 25px;
  }
}

.auth-form {
  .form-group:nth-last-child(3) {
    margin-bottom: 15px; // Terms checkbox
  }

  .form-group:nth-last-child(2) {
    margin-bottom: 25px; // Newsletter checkbox
  }
}
