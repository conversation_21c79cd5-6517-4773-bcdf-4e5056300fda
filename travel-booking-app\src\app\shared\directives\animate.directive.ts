import { 
  Directive, 
  ElementRef, 
  Input, 
  OnInit, 
  On<PERSON><PERSON>roy, 
  inject,
  AfterViewInit
} from '@angular/core';
import { AnimationService, AnimationConfig, ScrollAnimationConfig } from '../../core/services/animation.service';

@Directive({
  selector: '[appAnimate]',
  standalone: true
})
export class AnimateDirective implements OnInit, AfterViewInit, OnDestroy {
  private readonly elementRef = inject(ElementRef);
  private readonly animationService = inject(AnimationService);

  @Input('appAnimate') animationType: string = 'fadeIn';
  @Input() animationConfig: AnimationConfig = {};
  @Input() animationTrigger: 'immediate' | 'scroll' | 'hover' = 'scroll';
  @Input() animationDelay: number = 0;
  @Input() animationDuration: number = 600;
  @Input() animationStagger: number = 0;

  private animation?: Animation;

  ngOnInit(): void {
    // Set up animation configuration
    this.animationConfig = {
      duration: this.animationDuration,
      delay: this.animationDelay,
      ...this.animationConfig
    };
  }

  ngAfterViewInit(): void {
    const element = this.elementRef.nativeElement as HTMLElement;

    switch (this.animationTrigger) {
      case 'immediate':
        this.triggerImmediateAnimation(element);
        break;
      case 'scroll':
        this.setupScrollAnimation(element);
        break;
      case 'hover':
        this.setupHoverAnimation(element);
        break;
    }
  }

  private triggerImmediateAnimation(element: HTMLElement): void {
    setTimeout(() => {
      this.animation = this.animationService[this.animationType as keyof AnimationService](
        element, 
        this.animationConfig
      ) as Animation;
    }, this.animationDelay);
  }

  private setupScrollAnimation(element: HTMLElement): void {
    const scrollConfig: ScrollAnimationConfig = {
      ...this.animationConfig,
      threshold: 0.1,
      once: true
    };

    this.animationService.animateOnScroll(element, this.animationType, scrollConfig);
  }

  private setupHoverAnimation(element: HTMLElement): void {
    this.animationService.addHoverAnimation(element, this.animationType);
  }

  ngOnDestroy(): void {
    if (this.animation) {
      this.animation.cancel();
    }
  }
}

@Directive({
  selector: '[appParallax]',
  standalone: true
})
export class ParallaxDirective implements AfterViewInit {
  private readonly elementRef = inject(ElementRef);
  private readonly animationService = inject(AnimationService);

  @Input('appParallax') speed: number = 0.5;

  ngAfterViewInit(): void {
    const element = this.elementRef.nativeElement as HTMLElement;
    this.animationService.createParallaxEffect(element, this.speed);
  }
}

@Directive({
  selector: '[appStagger]',
  standalone: true
})
export class StaggerDirective implements AfterViewInit {
  private readonly elementRef = inject(ElementRef);
  private readonly animationService = inject(AnimationService);

  @Input('appStagger') animationType: string = 'fadeIn';
  @Input() staggerDelay: number = 100;
  @Input() staggerConfig: AnimationConfig = {};
  @Input() staggerSelector: string = '.stagger-item';

  ngAfterViewInit(): void {
    const container = this.elementRef.nativeElement as HTMLElement;
    const elements = Array.from(
      container.querySelectorAll(this.staggerSelector)
    ) as HTMLElement[];

    if (elements.length > 0) {
      this.animationService.staggerAnimation(elements, this.animationType, {
        ...this.staggerConfig,
        staggerDelay: this.staggerDelay
      });
    }
  }
}

@Directive({
  selector: '[appTilt]',
  standalone: true
})
export class TiltDirective implements AfterViewInit, OnDestroy {
  private readonly elementRef = inject(ElementRef);
  
  @Input() tiltMaxAngle: number = 15;
  @Input() tiltPerspective: number = 1000;
  @Input() tiltScale: number = 1.05;
  @Input() tiltSpeed: number = 300;

  private element!: HTMLElement;
  private isHovering = false;

  ngAfterViewInit(): void {
    this.element = this.elementRef.nativeElement as HTMLElement;
    this.setupTiltEffect();
  }

  private setupTiltEffect(): void {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) return;

    this.element.style.transformStyle = 'preserve-3d';
    this.element.style.transition = `transform ${this.tiltSpeed}ms cubic-bezier(0.03, 0.98, 0.52, 0.99)`;

    this.element.addEventListener('mouseenter', this.onMouseEnter.bind(this));
    this.element.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.element.addEventListener('mouseleave', this.onMouseLeave.bind(this));
  }

  private onMouseEnter(): void {
    this.isHovering = true;
    this.element.style.willChange = 'transform';
  }

  private onMouseMove(event: MouseEvent): void {
    if (!this.isHovering) return;

    const rect = this.element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const mouseX = event.clientX - centerX;
    const mouseY = event.clientY - centerY;

    const rotateX = (mouseY / (rect.height / 2)) * this.tiltMaxAngle;
    const rotateY = (mouseX / (rect.width / 2)) * this.tiltMaxAngle;

    this.element.style.transform = `
      perspective(${this.tiltPerspective}px)
      rotateX(${-rotateX}deg)
      rotateY(${rotateY}deg)
      scale3d(${this.tiltScale}, ${this.tiltScale}, ${this.tiltScale})
    `;
  }

  private onMouseLeave(): void {
    this.isHovering = false;
    this.element.style.willChange = 'auto';
    this.element.style.transform = `
      perspective(${this.tiltPerspective}px)
      rotateX(0deg)
      rotateY(0deg)
      scale3d(1, 1, 1)
    `;
  }

  ngOnDestroy(): void {
    if (this.element) {
      this.element.removeEventListener('mouseenter', this.onMouseEnter);
      this.element.removeEventListener('mousemove', this.onMouseMove);
      this.element.removeEventListener('mouseleave', this.onMouseLeave);
    }
  }
}

@Directive({
  selector: '[appRevealOnScroll]',
  standalone: true
})
export class RevealOnScrollDirective implements AfterViewInit, OnDestroy {
  private readonly elementRef = inject(ElementRef);
  
  @Input() revealThreshold: number = 0.1;
  @Input() revealAnimation: string = 'fadeIn';
  @Input() revealOnce: boolean = true;

  private observer?: IntersectionObserver;
  private hasAnimated = false;

  ngAfterViewInit(): void {
    this.setupIntersectionObserver();
  }

  private setupIntersectionObserver(): void {
    if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return;
    }

    const element = this.elementRef.nativeElement as HTMLElement;
    
    // Set initial state
    element.style.opacity = '0';
    element.style.transform = 'translateY(30px)';
    element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && (!this.revealOnce || !this.hasAnimated)) {
            this.revealElement(entry.target as HTMLElement);
            this.hasAnimated = true;
            
            if (this.revealOnce && this.observer) {
              this.observer.unobserve(entry.target);
            }
          } else if (!entry.isIntersecting && !this.revealOnce) {
            this.hideElement(entry.target as HTMLElement);
          }
        });
      },
      {
        threshold: this.revealThreshold,
        rootMargin: '0px 0px -50px 0px'
      }
    );

    this.observer.observe(element);
  }

  private revealElement(element: HTMLElement): void {
    element.style.opacity = '1';
    element.style.transform = 'translateY(0)';
  }

  private hideElement(element: HTMLElement): void {
    element.style.opacity = '0';
    element.style.transform = 'translateY(30px)';
  }

  ngOnDestroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}
