.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.3s ease;

  &.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
  }
}

// Logo
.logo {
  .logo-link {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: #2d3748;
    font-weight: 700;
    font-size: 1.2rem;
    transition: color 0.3s ease;

    &:hover {
      color: #667eea;
    }

    .logo-icon {
      font-size: 1.5rem;
    }

    .logo-text {
      @media (max-width: 480px) {
        display: none;
      }
    }
  }
}

// Desktop Navigation
.desktop-nav {
  @media (max-width: 768px) {
    display: none;
  }

  .nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;

    li {
      a {
        text-decoration: none;
        color: #4a5568;
        font-weight: 500;
        padding: 8px 0;
        position: relative;
        transition: color 0.3s ease;

        &:hover,
        &.active {
          color: #667eea;
        }

        &.active::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          right: 0;
          height: 2px;
          background: #667eea;
          border-radius: 1px;
        }

        &.admin-link {
          color: #3b82f6;
          font-weight: 600;
        }
      }
    }
  }
}

// User Actions
.user-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.auth-buttons {
  display: flex;
  gap: 10px;

  @media (max-width: 768px) {
    display: none;
  }
}

// User Menu
.user-menu {
  position: relative;

  .user-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: background 0.3s ease;

    &:hover {
      background: #f7fafc;
    }

    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
      font-weight: 600;

      &.large {
        width: 40px;
        height: 40px;
        font-size: 1rem;
      }
    }

    .user-name {
      font-weight: 500;
      color: #2d3748;

      @media (max-width: 480px) {
        display: none;
      }
    }

    .dropdown-arrow {
      font-size: 0.7rem;
      color: #718096;
      transition: transform 0.3s ease;
    }
  }

  &.open .user-button .dropdown-arrow {
    transform: rotate(180deg);
  }

  .user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid #e2e8f0;
    min-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
  }

  &.open .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .dropdown-header {
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .user-details {
        .user-full-name {
          font-weight: 600;
          color: #2d3748;
          margin-bottom: 2px;
        }

        .user-email {
          font-size: 0.8rem;
          color: #718096;
        }
      }
    }
  }

  .dropdown-menu {
    padding: 8px 0;

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 20px;
      text-decoration: none;
      color: #4a5568;
      transition: background 0.3s ease;
      border: none;
      background: none;
      width: 100%;
      text-align: left;
      cursor: pointer;
      font-size: 0.9rem;

      &:hover {
        background: #f7fafc;
      }

      &.admin {
        color: #3b82f6;
      }

      &.logout {
        color: #e53e3e;
      }

      .item-icon {
        font-size: 1rem;
      }
    }

    .dropdown-divider {
      height: 1px;
      background: #e2e8f0;
      margin: 8px 0;
    }
  }
}

// Theme Toggle
.theme-toggle {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background 0.3s ease;

  &:hover {
    background: #f7fafc;
  }
}

// Mobile Menu Toggle
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;

  @media (max-width: 768px) {
    display: block;
  }

  .hamburger {
    display: flex;
    flex-direction: column;
    gap: 3px;
    width: 20px;

    span {
      height: 2px;
      background: #4a5568;
      border-radius: 1px;
      transition: all 0.3s ease;
    }

    &.open {
      span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
      }

      span:nth-child(2) {
        opacity: 0;
      }

      span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
      }
    }
  }
}

// Mobile Navigation
.mobile-nav {
  display: none;
  background: white;
  border-top: 1px solid #e2e8f0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;

  @media (max-width: 768px) {
    display: block;
  }

  &.open {
    max-height: 400px;
  }

  .mobile-nav-list {
    list-style: none;
    margin: 0;
    padding: 20px 0;

    li {
      a, button {
        display: block;
        padding: 12px 20px;
        text-decoration: none;
        color: #4a5568;
        font-weight: 500;
        transition: background 0.3s ease;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        cursor: pointer;

        &:hover {
          background: #f7fafc;
          color: #667eea;
        }

        &.admin-link {
          color: #3b82f6;
        }
      }

      .mobile-logout {
        color: #e53e3e;
      }
    }
  }
}

// Button Styles
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  font-size: 0.9rem;

  &.btn-primary {
    background: #667eea;
    color: white;
    border: 1px solid #667eea;

    &:hover {
      background: #5a67d8;
      border-color: #5a67d8;
    }
  }

  &.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;

    &:hover {
      background: #667eea;
      color: white;
    }
  }
}

// Add top padding to body to account for fixed header
:host {
  ~ * {
    padding-top: 70px;
  }
}
