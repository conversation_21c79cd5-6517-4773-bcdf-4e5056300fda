import { Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { 
  User, 
  Trip, 
  Blog, 
  Booking,
  PaginationParameters,
  PagedResult
} from '../models';

export interface DashboardStats {
  totalUsers: number;
  totalBookings: number;
  totalRevenue: number;
  totalTrips: number;
  pendingBookings: number;
  activeUsers: number;
  popularDestinations: Array<{name: string; bookings: number}>;
  recentBookings: Array<any>;
  monthlyRevenue: Array<{month: string; revenue: number}>;
  bookingsByStatus: Array<{status: string; count: number}>;
}

export interface AdminUserFilterDto {
  search?: string;
  role?: string;
  isActive?: boolean;
  registeredFrom?: Date;
  registeredTo?: Date;
}

export interface AdminTripFilterDto {
  search?: string;
  categoryId?: number;
  isActive?: boolean;
  isFeatured?: boolean;
  createdFrom?: Date;
  createdTo?: Date;
}

export interface AdminBookingFilterDto {
  search?: string;
  status?: string;
  tripId?: number;
  userId?: number;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface AdminBlogFilterDto {
  search?: string;
  categoryId?: number;
  authorId?: number;
  isPublished?: boolean;
  createdFrom?: Date;
  createdTo?: Date;
}

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  private readonly apiService = inject(ApiService);

  // Dashboard and Statistics
  getDashboardStats(): Observable<DashboardStats> {
    return this.apiService.get<DashboardStats>('admin/dashboard/stats');
  }

  getDashboardOverview(): Observable<any> {
    return this.apiService.get('admin/dashboard/overview');
  }

  getSystemStatistics(): Observable<any> {
    return this.apiService.get('admin/dashboard/system-stats');
  }

  // User Management
  getUsers(pagination: PaginationParameters, filters?: AdminUserFilterDto): Observable<PagedResult<User>> {
    return this.apiService.getPaged<User>('admin/users', pagination, filters);
  }

  getUserById(id: number): Observable<User> {
    return this.apiService.get<User>(`admin/users/${id}`);
  }

  updateUser(id: number, userData: Partial<User>): Observable<User> {
    return this.apiService.put<User>(`admin/users/${id}`, userData);
  }

  deleteUser(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/users/${id}`);
  }

  toggleUserStatus(id: number): Observable<User> {
    return this.apiService.put<User>(`admin/users/${id}/toggle-status`, {});
  }

  // Trip Management
  getTrips(pagination: PaginationParameters, filters?: AdminTripFilterDto): Observable<PagedResult<Trip>> {
    return this.apiService.getPaged<Trip>('admin/trips', pagination, filters);
  }

  getTripById(id: number): Observable<Trip> {
    return this.apiService.get<Trip>(`admin/trips/${id}`);
  }

  createTrip(tripData: any): Observable<Trip> {
    return this.apiService.post<Trip>('admin/trips', tripData);
  }

  updateTrip(id: number, tripData: Partial<Trip>): Observable<Trip> {
    return this.apiService.put<Trip>(`admin/trips/${id}`, tripData);
  }

  deleteTrip(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/trips/${id}`);
  }

  toggleTripStatus(id: number): Observable<Trip> {
    return this.apiService.put<Trip>(`admin/trips/${id}/toggle-status`, {});
  }

  // Booking Management
  getBookings(pagination: PaginationParameters, filters?: AdminBookingFilterDto): Observable<PagedResult<Booking>> {
    return this.apiService.getPaged<Booking>('admin/bookings', pagination, filters);
  }

  getBookingById(id: number): Observable<Booking> {
    return this.apiService.get<Booking>(`admin/bookings/${id}`);
  }

  updateBookingStatus(id: number, status: string): Observable<Booking> {
    return this.apiService.put<Booking>(`admin/bookings/${id}/status`, { status });
  }

  cancelBooking(id: number, reason?: string): Observable<Booking> {
    return this.apiService.put<Booking>(`admin/bookings/${id}/cancel`, { reason });
  }

  // Blog Management
  getBlogs(pagination: PaginationParameters, filters?: AdminBlogFilterDto): Observable<PagedResult<Blog>> {
    return this.apiService.getPaged<Blog>('admin/blogs', pagination, filters);
  }

  getBlogById(id: number): Observable<Blog> {
    return this.apiService.get<Blog>(`admin/blogs/${id}`);
  }

  createBlog(blogData: any): Observable<Blog> {
    return this.apiService.post<Blog>('admin/blogs', blogData);
  }

  updateBlog(id: number, blogData: Partial<Blog>): Observable<Blog> {
    return this.apiService.put<Blog>(`admin/blogs/${id}`, blogData);
  }

  deleteBlog(id: number): Observable<void> {
    return this.apiService.delete<void>(`admin/blogs/${id}`);
  }

  toggleBlogStatus(id: number): Observable<Blog> {
    return this.apiService.put<Blog>(`admin/blogs/${id}/toggle-status`, {});
  }

  // Analytics
  getRevenueAnalytics(params: any): Observable<any> {
    return this.apiService.get('admin/analytics/revenue', params);
  }

  getUserAnalytics(params: any): Observable<any> {
    return this.apiService.get('admin/analytics/users', params);
  }

  getBookingTrends(params: any): Observable<any> {
    return this.apiService.get('admin/analytics/booking-trends', params);
  }

  getPopularDestinations(params: any): Observable<any> {
    return this.apiService.get('admin/analytics/popular-destinations', params);
  }

  // System Management
  clearSystemCache(): Observable<any> {
    return this.apiService.post('admin/system/clear-cache', {});
  }

  getSystemHealth(): Observable<any> {
    return this.apiService.get('admin/system/health');
  }

  getAuditLogs(pagination: PaginationParameters, filters?: any): Observable<PagedResult<any>> {
    return this.apiService.getPaged('admin/system/audit-logs', pagination, filters);
  }

  // Export functionality
  exportUsers(filters?: AdminUserFilterDto): Observable<Blob> {
    return this.apiService.getRaw<Blob>('admin/users/export', filters);
  }

  exportTrips(filters?: AdminTripFilterDto): Observable<Blob> {
    return this.apiService.getRaw<Blob>('admin/trips/export', filters);
  }

  exportBookings(filters?: AdminBookingFilterDto): Observable<Blob> {
    return this.apiService.getRaw<Blob>('admin/bookings/export', filters);
  }

  exportBlogs(filters?: AdminBlogFilterDto): Observable<Blob> {
    return this.apiService.getRaw<Blob>('admin/blogs/export', filters);
  }
}
