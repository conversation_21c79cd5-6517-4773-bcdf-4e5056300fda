.trip-list-container {
  min-height: 100vh;
  background: #f8f9fa;
}

// Page Header
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
  text-align: center;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
  }

  p {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
  }
}

// Filters Section
.filters-section {
  background: white;
  padding: 30px 0;
  border-bottom: 1px solid #e2e8f0;

  .filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .filter-group {
    label {
      display: block;
      font-weight: 600;
      margin-bottom: 8px;
      color: #2d3748;
      font-size: 0.9rem;
    }

    input, select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      font-size: 0.9rem;
      transition: border-color 0.2s;

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    .price-inputs {
      display: flex;
      align-items: center;
      gap: 10px;

      input {
        flex: 1;
      }

      span {
        color: #718096;
        font-weight: 500;
      }
    }
  }

  .filter-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 15px;
    }

    .results-count {
      color: #718096;
      font-weight: 500;
    }
  }
}

// Results Section
.results-section {
  padding: 40px 0;

  .trips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
  }

  .trip-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .trip-image {
      position: relative;
      height: 220px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .discount-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        background: #e53e3e;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
      }

      .featured-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: #38a169;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
      }
    }

    &:hover .trip-image img {
      transform: scale(1.05);
    }

    .trip-content {
      padding: 25px;

      .trip-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        h3 {
          font-size: 1.3rem;
          font-weight: 600;
          color: #2d3748;
          line-height: 1.3;
          flex: 1;
        }

        .trip-rating {
          margin-left: 10px;
          
          .rating {
            font-size: 0.9rem;
            color: #f6ad55;
            font-weight: 500;
          }
        }
      }

      .trip-description {
        color: #718096;
        margin-bottom: 15px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .trip-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 20px;

        span {
          font-size: 0.8rem;
          color: #4a5568;
          background: #edf2f7;
          padding: 4px 8px;
          border-radius: 12px;
          font-weight: 500;
        }
      }

      .trip-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .trip-price {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .original-price {
            text-decoration: line-through;
            color: #a0aec0;
            font-size: 0.9rem;
          }

          .current-price {
            font-size: 1.3rem;
            font-weight: 700;
            color: #38a169;
          }

          .price-note {
            font-size: 0.8rem;
            color: #718096;
          }
        }

        .btn-sm {
          padding: 8px 16px;
          font-size: 0.9rem;
        }
      }
    }
  }

  .no-results {
    text-align: center;
    padding: 60px 20px;
    color: #718096;

    h3 {
      font-size: 1.5rem;
      margin-bottom: 10px;
      color: #4a5568;
    }
  }
}

// Pagination
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 40px;

  .btn {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

// Loading States
.loading-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;

  .trip-card-skeleton {
    background: white;
    border-radius: 15px;
    height: 400px;
    animation: pulse 1.5s ease-in-out infinite alternate;
  }
}

// Common Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover {
      background: #5a67d8;
      transform: translateY(-1px);
    }
  }

  &.btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;

    &:hover {
      background: #667eea;
      color: white;
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  100% { opacity: 0.4; }
}
