import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { TripService } from '../../../core/services';
import { Trip } from '../../../core/models';

@Component({
  selector: 'app-trip-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="trip-detail-container">
      @if (isLoading()) {
        <div class="loading-skeleton">
          <div class="skeleton-header"></div>
          <div class="skeleton-content"></div>
        </div>
      } @else if (trip()) {
        <!-- Hero Section -->
        <section class="trip-hero">
          <div class="hero-image">
            <img [src]="trip()!.mainImageUrl" [alt]="trip()!.name" />
            <div class="hero-overlay">
              <div class="container">
                <div class="hero-content">
                  <h1>{{ trip()!.name }}</h1>
                  <div class="trip-meta">
                    <span class="duration">{{ trip()!.duration }} days</span>
                    <span class="difficulty">{{ getDifficultyText(trip()!.difficulty) }}</span>
                    <span class="category">{{ trip()!.category.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Main Content -->
        <section class="trip-content">
          <div class="container">
            <div class="content-grid">
              <!-- Left Column - Details -->
              <div class="trip-details">
                <div class="description-section">
                  <h2>About This Trip</h2>
                  <p>{{ trip()!.description }}</p>
                </div>

                <!-- Itinerary -->
                @if (trip()!.itinerary && trip()!.itinerary.length > 0) {
                  <div class="itinerary-section">
                    <h2>Itinerary</h2>
                    <div class="itinerary-list">
                      @for (day of trip()!.itinerary; track day.id) {
                        <div class="itinerary-day">
                          <div class="day-number">Day {{ day.day }}</div>
                          <div class="day-content">
                            <h3>{{ day.title }}</h3>
                            <p>{{ day.description }}</p>
                            @if (day.activities) {
                              <div class="day-activities">
                                <strong>Activities:</strong> {{ day.activities }}
                              </div>
                            }
                            @if (day.meals) {
                              <div class="day-meals">
                                <strong>Meals:</strong> {{ day.meals }}
                              </div>
                            }
                            @if (day.accommodation) {
                              <div class="day-accommodation">
                                <strong>Accommodation:</strong> {{ day.accommodation }}
                              </div>
                            }
                          </div>
                        </div>
                      }
                    </div>
                  </div>
                }

                <!-- What's Included -->
                <div class="included-section">
                  <h2>What's Included</h2>
                  <div class="included-grid">
                    <div class="included-item" [class.included]="trip()!.includesAccommodation">
                      <span class="icon">🏨</span>
                      <span>Accommodation</span>
                    </div>
                    <div class="included-item" [class.included]="trip()!.includesTransport">
                      <span class="icon">🚗</span>
                      <span>Transportation</span>
                    </div>
                    <div class="included-item" [class.included]="trip()!.includesMeals">
                      <span class="icon">🍽️</span>
                      <span>Meals</span>
                    </div>
                    <div class="included-item" [class.included]="trip()!.includesGuide">
                      <span class="icon">👨‍🏫</span>
                      <span>Tour Guide</span>
                    </div>
                  </div>
                </div>

                <!-- Image Gallery -->
                @if (trip()!.images && trip()!.images.length > 0) {
                  <div class="gallery-section">
                    <h2>Gallery</h2>
                    <div class="image-gallery">
                      @for (image of trip()!.images; track image.id) {
                        <div class="gallery-item">
                          <img [src]="image.imageUrl" [alt]="image.caption || trip()!.name" />
                        </div>
                      }
                    </div>
                  </div>
                }
              </div>

              <!-- Right Column - Booking -->
              <div class="booking-sidebar">
                <div class="booking-card">
                  <div class="price-section">
                    @if (trip()!.discountPrice) {
                      <div class="original-price">\${{ trip()!.price }}</div>
                      <div class="current-price">\${{ trip()!.discountPrice }}</div>
                      <div class="discount-badge">
                        {{ calculateDiscount(trip()!.price, trip()!.discountPrice) }}% OFF
                      </div>
                    } @else {
                      <div class="current-price">\${{ trip()!.price }}</div>
                    }
                    <div class="price-note">per person</div>
                  </div>

                  <div class="trip-info">
                    <div class="info-item">
                      <span class="label">Duration:</span>
                      <span class="value">{{ trip()!.duration }} days</span>
                    </div>
                    <div class="info-item">
                      <span class="label">Max Capacity:</span>
                      <span class="value">{{ trip()!.maxCapacity }} people</span>
                    </div>
                    @if (trip()!.minAge || trip()!.maxAge) {
                      <div class="info-item">
                        <span class="label">Age Range:</span>
                        <span class="value">
                          {{ trip()!.minAge || 0 }} - {{ trip()!.maxAge || '∞' }} years
                        </span>
                      </div>
                    }
                    <div class="info-item">
                      <span class="label">Difficulty:</span>
                      <span class="value">{{ getDifficultyText(trip()!.difficulty) }}</span>
                    </div>
                  </div>

                  <div class="booking-actions">
                    <button class="btn btn-primary btn-large" (click)="bookNow()">
                      Book Now
                    </button>
                    <button class="btn btn-outline btn-large" (click)="addToWishlist()">
                      Add to Wishlist
                    </button>
                  </div>

                  <div class="contact-info">
                    <p>Need help? <a href="tel:+1234567890">Call us</a></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      } @else {
        <div class="error-state">
          <h2>Trip not found</h2>
          <p>The trip you're looking for doesn't exist or has been removed.</p>
          <button class="btn btn-primary" routerLink="/trips">
            Browse All Trips
          </button>
        </div>
      }
    </div>
  `,
  styleUrls: ['./trip-detail.component.scss']
})
export class TripDetailComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly tripService = inject(TripService);

  trip = signal<Trip | null>(null);
  isLoading = signal<boolean>(false);

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const tripId = Number(params['id']);
      if (tripId) {
        this.loadTrip(tripId);
      }
    });
  }

  private async loadTrip(id: number): Promise<void> {
    this.isLoading.set(true);
    
    try {
      const trip = await this.tripService.getTripById(id).toPromise();
      this.trip.set(trip || null);
    } catch (error) {
      console.error('Error loading trip:', error);
      this.trip.set(null);
    } finally {
      this.isLoading.set(false);
    }
  }

  calculateDiscount(originalPrice: number, discountPrice: number): number {
    return Math.round(((originalPrice - discountPrice) / originalPrice) * 100);
  }

  getDifficultyText(difficulty: number): string {
    const difficultyMap = { 1: 'Easy', 2: 'Moderate', 3: 'Hard' };
    return difficultyMap[difficulty as keyof typeof difficultyMap] || 'Unknown';
  }

  bookNow(): void {
    // TODO: Navigate to booking page
    console.log('Booking trip:', this.trip()?.id);
  }

  addToWishlist(): void {
    // TODO: Add to wishlist functionality
    console.log('Adding to wishlist:', this.trip()?.id);
  }
}
