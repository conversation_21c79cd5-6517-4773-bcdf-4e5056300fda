import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { BookingService, NotificationService } from '../../../core/services';
import { Booking } from '../../../core/models';

@Component({
  selector: 'app-booking-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="booking-detail-container">
      @if (isLoading()) {
        <div class="loading-container">
          <div class="spinner"></div>
          <p>Loading booking details...</p>
        </div>
      } @else if (booking()) {
        <div class="container">
          <!-- Header -->
          <div class="booking-header">
            <div class="breadcrumb">
              <a routerLink="/bookings">My Bookings</a>
              <span>/</span>
              <span>Booking #{{ booking()!.bookingNumber }}</span>
            </div>

            <div class="header-content">
              <h1>{{ booking()!.trip.name }}</h1>
              <div class="booking-status" [class]="'status-' + booking()!.status">
                {{ getStatusText(booking()!.status) }}
              </div>
            </div>
          </div>

          <div class="booking-content">
            <!-- Trip Information -->
            <div class="section trip-info">
              <h2>Trip Information</h2>
              <div class="trip-card">
                <div class="trip-image">
                  <img [src]="booking()!.trip.mainImageUrl" [alt]="booking()!.trip.name" />
                </div>
                <div class="trip-details">
                  <h3>{{ booking()!.trip.name }}</h3>
                  <p>{{ booking()!.trip.shortDescription }}</p>
                  <div class="trip-meta">
                    <div class="meta-item">
                      <span class="label">Duration:</span>
                      <span class="value">{{ booking()!.trip.duration }} days</span>
                    </div>
                    <div class="meta-item">
                      <span class="label">Difficulty:</span>
                      <span class="value">{{ getDifficultyText(booking()!.trip.difficulty) }}</span>
                    </div>
                    <div class="meta-item">
                      <span class="label">Category:</span>
                      <span class="value">{{ booking()!.trip.category.name }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Booking Details -->
            <div class="section booking-details">
              <h2>Booking Details</h2>
              <div class="details-grid">
                <div class="detail-card">
                  <h3>Travel Information</h3>
                  <div class="detail-item">
                    <span class="label">Travel Date:</span>
                    <span class="value">{{ booking()!.travelDate | date:'fullDate' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Booking Date:</span>
                    <span class="value">{{ booking()!.bookingDate | date:'mediumDate' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Booking Number:</span>
                    <span class="value">{{ booking()!.bookingNumber }}</span>
                  </div>
                </div>

                <div class="detail-card">
                  <h3>Travelers</h3>
                  <div class="detail-item">
                    <span class="label">Adults:</span>
                    <span class="value">{{ booking()!.numberOfAdults }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Children:</span>
                    <span class="value">{{ booking()!.numberOfChildren }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Infants:</span>
                    <span class="value">{{ booking()!.numberOfInfants }}</span>
                  </div>
                  <div class="detail-item total">
                    <span class="label">Total People:</span>
                    <span class="value">{{ booking()!.totalPeople }}</span>
                  </div>
                </div>

                <div class="detail-card">
                  <h3>Pricing</h3>
                  <div class="detail-item">
                    <span class="label">Price per Person:</span>
                    <span class="value">\${{ booking()!.pricePerPerson | number:'1.2-2' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Total Amount:</span>
                    <span class="value">\${{ booking()!.totalAmount | number:'1.2-2' }}</span>
                  </div>
                  @if (booking()!.discountAmount > 0) {
                    <div class="detail-item">
                      <span class="label">Discount:</span>
                      <span class="value discount">-\${{ booking()!.discountAmount | number:'1.2-2' }}</span>
                    </div>
                  }
                  <div class="detail-item total">
                    <span class="label">Final Amount:</span>
                    <span class="value">\${{ booking()!.finalAmount | number:'1.2-2' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Contact Information -->
            @if (booking()!.emergencyContactName || booking()!.emergencyContactPhone) {
              <div class="section contact-info">
                <h2>Emergency Contact</h2>
                <div class="contact-card">
                  @if (booking()!.emergencyContactName) {
                    <div class="detail-item">
                      <span class="label">Name:</span>
                      <span class="value">{{ booking()!.emergencyContactName }}</span>
                    </div>
                  }
                  @if (booking()!.emergencyContactPhone) {
                    <div class="detail-item">
                      <span class="label">Phone:</span>
                      <span class="value">{{ booking()!.emergencyContactPhone }}</span>
                    </div>
                  }
                </div>
              </div>
            }

            <!-- Special Requests -->
            @if (booking()!.specialRequests) {
              <div class="section special-requests">
                <h2>Special Requests</h2>
                <div class="requests-card">
                  <p>{{ booking()!.specialRequests }}</p>
                </div>
              </div>
            }

            <!-- Payment Information -->
            @if (booking()!.payment) {
              <div class="section payment-info">
                <h2>Payment Information</h2>
                <div class="payment-card">
                  <div class="detail-item">
                    <span class="label">Payment Method:</span>
                    <span class="value">{{ booking()!.payment!.paymentMethod }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Transaction ID:</span>
                    <span class="value">{{ booking()!.payment!.transactionId }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="label">Payment Status:</span>
                    <span class="value" [class]="'payment-status-' + booking()!.payment!.status">
                      {{ getPaymentStatusText(booking()!.payment!.status) }}
                    </span>
                  </div>
                  @if (booking()!.payment!.paymentDate) {
                    <div class="detail-item">
                      <span class="label">Payment Date:</span>
                      <span class="value">{{ booking()!.payment!.paymentDate | date:'medium' }}</span>
                    </div>
                  }
                </div>
              </div>
            }

            <!-- Cancellation Information -->
            @if (booking()!.status === 3 && booking()!.cancellationReason) {
              <div class="section cancellation-info">
                <h2>Cancellation Information</h2>
                <div class="cancellation-card">
                  <div class="detail-item">
                    <span class="label">Reason:</span>
                    <span class="value">{{ booking()!.cancellationReason }}</span>
                  </div>
                  @if (booking()!.cancelledAt) {
                    <div class="detail-item">
                      <span class="label">Cancelled Date:</span>
                      <span class="value">{{ booking()!.cancelledAt | date:'medium' }}</span>
                    </div>
                  }
                </div>
              </div>
            }

            <!-- Actions -->
            <div class="section booking-actions">
              <div class="actions-grid">
                <a routerLink="/bookings" class="btn btn-outline">
                  ← Back to Bookings
                </a>

                @if (booking()!.status === 2 && canCancelBooking(booking()!)) {
                  <button class="btn btn-danger" (click)="cancelBooking()">
                    Cancel Booking
                  </button>
                }

                @if (booking()!.status === 1 && booking()!.paymentStatus === 1) {
                  <button class="btn btn-primary" (click)="makePayment()">
                    Complete Payment
                  </button>
                }

                <button class="btn btn-secondary" (click)="downloadReceipt()">
                  Download Receipt
                </button>
              </div>
            </div>
          </div>
        </div>
      } @else {
        <div class="error-container">
          <h2>Booking not found</h2>
          <p>The booking you're looking for doesn't exist or you don't have permission to view it.</p>
          <a routerLink="/bookings" class="btn btn-primary">Back to Bookings</a>
        </div>
      }
    </div>
  `,
  styleUrls: ['./booking-detail.component.scss']
})
export class BookingDetailComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly bookingService = inject(BookingService);
  private readonly notificationService = inject(NotificationService);

  booking = signal<Booking | null>(null);
  isLoading = signal<boolean>(false);

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const bookingId = Number(params['id']);
      if (bookingId) {
        this.loadBooking(bookingId);
      }
    });
  }

  private async loadBooking(id: number): Promise<void> {
    this.isLoading.set(true);

    try {
      const booking = await this.bookingService.getBookingById(id).toPromise();
      this.booking.set(booking || null);
    } catch (error) {
      console.error('Error loading booking:', error);
      this.booking.set(null);
    } finally {
      this.isLoading.set(false);
    }
  }

  getStatusText(status: number): string {
    const statusMap = {
      1: 'Pending',
      2: 'Confirmed',
      3: 'Cancelled',
      4: 'Completed'
    };
    return statusMap[status as keyof typeof statusMap] || 'Unknown';
  }

  getPaymentStatusText(status: number): string {
    const statusMap = {
      1: 'Pending',
      2: 'Paid',
      3: 'Failed',
      4: 'Refunded'
    };
    return statusMap[status as keyof typeof statusMap] || 'Unknown';
  }

  getDifficultyText(difficulty: number): string {
    const difficultyMap = { 1: 'Easy', 2: 'Moderate', 3: 'Hard' };
    return difficultyMap[difficulty as keyof typeof difficultyMap] || 'Unknown';
  }

  canCancelBooking(booking: Booking): boolean {
    const travelDate = new Date(booking.travelDate);
    const now = new Date();
    const daysDifference = Math.ceil((travelDate.getTime() - now.getTime()) / (1000 * 3600 * 24));

    return daysDifference > 7;
  }

  async cancelBooking(): Promise<void> {
    if (confirm('Are you sure you want to cancel this booking? This action cannot be undone.')) {
      try {
        await this.bookingService.cancelBooking(this.booking()!.id).toPromise();
        this.notificationService.showSuccess('Booking cancelled successfully');
        this.loadBooking(this.booking()!.id); // Reload to show updated status
      } catch (error) {
        console.error('Error cancelling booking:', error);
        this.notificationService.showError('Failed to cancel booking');
      }
    }
  }

  makePayment(): void {
    // TODO: Implement payment flow
    this.notificationService.showInfo('Payment feature coming soon!');
  }

  downloadReceipt(): void {
    // TODO: Implement receipt download
    this.notificationService.showInfo('Receipt download feature coming soon!');
  }
}
