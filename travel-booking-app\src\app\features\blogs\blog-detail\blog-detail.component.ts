import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { BlogService } from '../../../core/services';
import { Blog } from '../../../core/models';

@Component({
  selector: 'app-blog-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="blog-detail-container">
      @if (isLoading()) {
        <div class="loading-skeleton">
          <div class="skeleton-header"></div>
          <div class="skeleton-content"></div>
        </div>
      } @else if (blog()) {
        <article class="blog-article">
          <!-- Hero Section -->
          <header class="blog-header">
            <div class="container">
              <div class="breadcrumb">
                <a routerLink="/blogs">Blog</a>
                <span>/</span>
                <span>{{ blog()!.category.name }}</span>
              </div>
              
              <h1>{{ blog()!.title }}</h1>
              
              <div class="blog-meta">
                <div class="author-info">
                  <span class="author">By {{ blog()!.author.firstName }} {{ blog()!.author.lastName }}</span>
                  <span class="publish-date">{{ formatDate(blog()!.publishedAt) }}</span>
                </div>
                <div class="blog-stats">
                  <span class="reading-time">{{ blog()!.readingTime }} min read</span>
                  <span class="views">{{ blog()!.viewCount }} views</span>
                </div>
              </div>

              @if (blog()!.featuredImageUrl) {
                <div class="featured-image">
                  <img [src]="blog()!.featuredImageUrl" [alt]="blog()!.title" />
                </div>
              }
            </div>
          </header>

          <!-- Content Section -->
          <section class="blog-content">
            <div class="container">
              <div class="content-wrapper">
                <div class="article-content" [innerHTML]="blog()!.content"></div>
                
                <!-- Tags -->
                @if (blog()!.tags && blog()!.tags.length > 0) {
                  <div class="tags-section">
                    <h3>Tags</h3>
                    <div class="tags">
                      @for (tag of blog()!.tags; track tag.id) {
                        <span class="tag">{{ tag.name }}</span>
                      }
                    </div>
                  </div>
                }

                <!-- Share Section -->
                <div class="share-section">
                  <h3>Share this article</h3>
                  <div class="share-buttons">
                    <button class="share-btn facebook" (click)="shareOnFacebook()">
                      Facebook
                    </button>
                    <button class="share-btn twitter" (click)="shareOnTwitter()">
                      Twitter
                    </button>
                    <button class="share-btn linkedin" (click)="shareOnLinkedIn()">
                      LinkedIn
                    </button>
                    <button class="share-btn copy" (click)="copyLink()">
                      Copy Link
                    </button>
                  </div>
                </div>
              </div>

              <!-- Sidebar -->
              <aside class="blog-sidebar">
                <div class="author-card">
                  <h3>About the Author</h3>
                  <div class="author-details">
                    <div class="author-name">
                      {{ blog()!.author.firstName }} {{ blog()!.author.lastName }}
                    </div>
                    <div class="author-bio">
                      Travel enthusiast and storyteller sharing adventures from around the world.
                    </div>
                  </div>
                </div>

                <!-- Related Articles -->
                @if (relatedBlogs().length > 0) {
                  <div class="related-articles">
                    <h3>Related Articles</h3>
                    <div class="related-list">
                      @for (relatedBlog of relatedBlogs(); track relatedBlog.id) {
                        <a [routerLink]="['/blogs', relatedBlog.slug]" class="related-item">
                          <div class="related-image">
                            <img [src]="relatedBlog.featuredImageUrl" [alt]="relatedBlog.title" />
                          </div>
                          <div class="related-content">
                            <h4>{{ relatedBlog.title }}</h4>
                            <span class="related-date">{{ formatDate(relatedBlog.publishedAt) }}</span>
                          </div>
                        </a>
                      }
                    </div>
                  </div>
                }
              </aside>
            </div>
          </section>

          <!-- Navigation -->
          <section class="blog-navigation">
            <div class="container">
              <div class="nav-buttons">
                <button class="btn btn-outline" routerLink="/blogs">
                  ← Back to Blog
                </button>
                <button class="btn btn-primary" routerLink="/trips">
                  Explore Trips →
                </button>
              </div>
            </div>
          </section>
        </article>
      } @else {
        <div class="error-state">
          <h2>Article not found</h2>
          <p>The article you're looking for doesn't exist or has been removed.</p>
          <button class="btn btn-primary" routerLink="/blogs">
            Browse All Articles
          </button>
        </div>
      }
    </div>
  `,
  styleUrls: ['./blog-detail.component.scss']
})
export class BlogDetailComponent implements OnInit {
  private readonly route = inject(ActivatedRoute);
  private readonly blogService = inject(BlogService);

  blog = signal<Blog | null>(null);
  relatedBlogs = signal<Blog[]>([]);
  isLoading = signal<boolean>(false);

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const slug = params['slug'];
      if (slug) {
        this.loadBlog(slug);
      }
    });
  }

  private async loadBlog(slug: string): Promise<void> {
    this.isLoading.set(true);
    
    try {
      const blog = await this.blogService.getBlogBySlug(slug).toPromise();
      this.blog.set(blog || null);
      
      if (blog) {
        // Increment view count
        this.blogService.incrementViewCount(blog.id).subscribe();
        
        // Load related blogs
        this.loadRelatedBlogs(blog.id);
      }
    } catch (error) {
      console.error('Error loading blog:', error);
      this.blog.set(null);
    } finally {
      this.isLoading.set(false);
    }
  }

  private async loadRelatedBlogs(blogId: number): Promise<void> {
    try {
      const related = await this.blogService.getRelatedBlogs(blogId, 3).toPromise();
      this.relatedBlogs.set(related || []);
    } catch (error) {
      console.error('Error loading related blogs:', error);
    }
  }

  formatDate(date: Date | undefined): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  shareOnFacebook(): void {
    const url = encodeURIComponent(window.location.href);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
  }

  shareOnTwitter(): void {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(this.blog()?.title || '');
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
  }

  shareOnLinkedIn(): void {
    const url = encodeURIComponent(window.location.href);
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
  }

  copyLink(): void {
    navigator.clipboard.writeText(window.location.href).then(() => {
      // TODO: Show success notification
      console.log('Link copied to clipboard');
    });
  }
}
