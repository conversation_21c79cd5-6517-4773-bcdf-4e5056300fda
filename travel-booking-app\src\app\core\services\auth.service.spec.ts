import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { AuthService } from './auth.service';
import { ApiService } from './api.service';
import { LoginDto, RegisterDto, AuthResultDto } from '../models/auth.model';
import { User } from '../models/user.model';

describe('AuthService', () => {
  let service: AuthService;
  let apiServiceSpy: jasmine.SpyObj<ApiService>;
  let routerSpy: jasmine.SpyObj<Router>;

  const mockUser: User = {
    id: 1,
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phoneNumber: '+1234567890',
    role: 'User',
    isEmailVerified: true,
    profilePictureUrl: null,
    dateOfBirth: new Date('1990-01-01'),
    address: '123 Main St',
    city: 'New York',
    state: 'NY',
    zipCode: '10001',
    country: 'USA',
    emergencyContactName: '<PERSON>',
    emergencyContactPhone: '+1234567891',
    emailNotifications: true,
    smsNotifications: false,
    newsletter: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockAuthResult: AuthResultDto = {
    user: mockUser,
    token: 'mock-jwt-token',
    refreshToken: 'mock-refresh-token',
    expiresAt: new Date(Date.now() + 3600000) // 1 hour from now
  };

  beforeEach(() => {
    const apiSpy = jasmine.createSpyObj('ApiService', ['post', 'get', 'put']);
    const routerSpyObj = jasmine.createSpyObj('Router', ['navigate']);

    TestBed.configureTestingModule({
      providers: [
        AuthService,
        { provide: ApiService, useValue: apiSpy },
        { provide: Router, useValue: routerSpyObj }
      ]
    });

    service = TestBed.inject(AuthService);
    apiServiceSpy = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    routerSpy = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Clear localStorage before each test
    localStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('login', () => {
    it('should login successfully and set user data', (done) => {
      const loginData: LoginDto = {
        email: '<EMAIL>',
        password: 'password123'
      };

      apiServiceSpy.post.and.returnValue(of(mockAuthResult));

      service.login(loginData).subscribe({
        next: (result) => {
          expect(result).toEqual(mockAuthResult);
          expect(service.currentUser()).toEqual(mockUser);
          expect(service.isAuthenticated()).toBe(true);
          expect(localStorage.getItem('auth_token')).toBe('mock-jwt-token');
          expect(localStorage.getItem('refresh_token')).toBe('mock-refresh-token');
          done();
        }
      });

      expect(apiServiceSpy.post).toHaveBeenCalledWith('auth/login', loginData);
    });

    it('should handle login error', (done) => {
      const loginData: LoginDto = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const errorResponse = { error: { message: 'Invalid credentials' } };
      apiServiceSpy.post.and.returnValue(throwError(() => errorResponse));

      service.login(loginData).subscribe({
        error: (error) => {
          expect(error).toEqual(errorResponse);
          expect(service.currentUser()).toBeNull();
          expect(service.isAuthenticated()).toBe(false);
          done();
        }
      });
    });
  });

  describe('register', () => {
    it('should register successfully', (done) => {
      const registerData: RegisterDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        phoneNumber: '+1234567890',
        dateOfBirth: new Date('1990-01-01')
      };

      apiServiceSpy.post.and.returnValue(of(mockAuthResult));

      service.register(registerData).subscribe({
        next: (result) => {
          expect(result).toEqual(mockAuthResult);
          expect(service.currentUser()).toEqual(mockUser);
          expect(service.isAuthenticated()).toBe(true);
          done();
        }
      });

      expect(apiServiceSpy.post).toHaveBeenCalledWith('auth/register', registerData);
    });
  });

  describe('logout', () => {
    it('should logout and clear user data', () => {
      // Set up authenticated state
      localStorage.setItem('auth_token', 'mock-token');
      localStorage.setItem('refresh_token', 'mock-refresh-token');
      localStorage.setItem('current_user', JSON.stringify(mockUser));
      service['setCurrentUser'](mockUser);
      service['_isAuthenticated'].set(true);

      service.logout();

      expect(service.currentUser()).toBeNull();
      expect(service.isAuthenticated()).toBe(false);
      expect(localStorage.getItem('auth_token')).toBeNull();
      expect(localStorage.getItem('refresh_token')).toBeNull();
      expect(localStorage.getItem('current_user')).toBeNull();
      expect(routerSpy.navigate).toHaveBeenCalledWith(['/auth/login']);
    });
  });

  describe('token management', () => {
    it('should check if token is expired', () => {
      const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';
      const validToken = service['generateMockToken'](Date.now() + 3600000); // 1 hour from now

      expect(service['isTokenExpired'](expiredToken)).toBe(true);
      expect(service['isTokenExpired'](validToken)).toBe(false);
    });

    it('should refresh token when expired', (done) => {
      const newAuthResult: AuthResultDto = {
        ...mockAuthResult,
        token: 'new-mock-jwt-token',
        refreshToken: 'new-mock-refresh-token'
      };

      apiServiceSpy.post.and.returnValue(of(newAuthResult));
      localStorage.setItem('refresh_token', 'old-refresh-token');

      service.refreshToken().subscribe({
        next: (result) => {
          expect(result).toEqual(newAuthResult);
          expect(localStorage.getItem('auth_token')).toBe('new-mock-jwt-token');
          expect(localStorage.getItem('refresh_token')).toBe('new-mock-refresh-token');
          done();
        }
      });

      expect(apiServiceSpy.post).toHaveBeenCalledWith('auth/refresh-token', {
        refreshToken: 'old-refresh-token'
      });
    });
  });

  describe('password management', () => {
    it('should send forgot password request', (done) => {
      const email = '<EMAIL>';
      apiServiceSpy.post.and.returnValue(of({ message: 'Reset email sent' }));

      service.forgotPassword({ email }).subscribe({
        next: (result) => {
          expect(result).toEqual({ message: 'Reset email sent' });
          done();
        }
      });

      expect(apiServiceSpy.post).toHaveBeenCalledWith('auth/forgot-password', { email });
    });

    it('should reset password', (done) => {
      const resetData = {
        token: 'reset-token',
        newPassword: 'newpassword123',
        confirmPassword: 'newpassword123'
      };

      apiServiceSpy.post.and.returnValue(of({ message: 'Password reset successful' }));

      service.resetPassword(resetData).subscribe({
        next: (result) => {
          expect(result).toEqual({ message: 'Password reset successful' });
          done();
        }
      });

      expect(apiServiceSpy.post).toHaveBeenCalledWith('auth/reset-password', resetData);
    });
  });

  describe('user roles', () => {
    it('should check if user is admin', () => {
      const adminUser = { ...mockUser, role: 'Admin' };
      service['setCurrentUser'](adminUser);
      expect(service.isAdmin()).toBe(true);

      const regularUser = { ...mockUser, role: 'User' };
      service['setCurrentUser'](regularUser);
      expect(service.isAdmin()).toBe(false);

      service['setCurrentUser'](null);
      expect(service.isAdmin()).toBe(false);
    });

    it('should check if user has specific role', () => {
      const adminUser = { ...mockUser, role: 'Admin' };
      service['setCurrentUser'](adminUser);
      expect(service.hasRole('Admin')).toBe(true);
      expect(service.hasRole('User')).toBe(false);
    });
  });

  describe('initialization', () => {
    it('should initialize with stored user data', () => {
      const validToken = service['generateMockToken'](Date.now() + 3600000);
      localStorage.setItem('auth_token', validToken);
      localStorage.setItem('current_user', JSON.stringify(mockUser));

      // Create new service instance to test initialization
      const newService = new AuthService();
      
      expect(newService.currentUser()).toEqual(mockUser);
      expect(newService.isAuthenticated()).toBe(true);
    });

    it('should not initialize with expired token', () => {
      const expiredToken = service['generateMockToken'](Date.now() - 3600000);
      localStorage.setItem('auth_token', expiredToken);
      localStorage.setItem('current_user', JSON.stringify(mockUser));

      // Create new service instance to test initialization
      const newService = new AuthService();
      
      expect(newService.currentUser()).toBeNull();
      expect(newService.isAuthenticated()).toBe(false);
    });
  });
});
