import { Injectable, signal } from '@angular/core';

export interface AnimationConfig {
  duration?: number;
  delay?: number;
  easing?: string;
  fill?: 'forwards' | 'backwards' | 'both' | 'none';
}

export interface ScrollAnimationConfig extends AnimationConfig {
  threshold?: number;
  rootMargin?: string;
  once?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AnimationService {
  private readonly prefersReducedMotion = signal<boolean>(false);
  private intersectionObserver?: IntersectionObserver;
  private animatedElements = new Set<Element>();

  constructor() {
    this.checkReducedMotionPreference();
    this.setupIntersectionObserver();
  }

  private checkReducedMotionPreference(): void {
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      this.prefersReducedMotion.set(mediaQuery.matches);
      
      mediaQuery.addEventListener('change', (e) => {
        this.prefersReducedMotion.set(e.matches);
      });
    }
  }

  private setupIntersectionObserver(): void {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.intersectionObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const element = entry.target as HTMLElement;
              const animationType = element.dataset['animation'];
              const config = JSON.parse(element.dataset['animationConfig'] || '{}');
              
              if (animationType) {
                this.triggerAnimation(element, animationType, config);
              }
            }
          });
        },
        {
          threshold: 0.1,
          rootMargin: '0px 0px -50px 0px'
        }
      );
    }
  }

  // Fade animations
  fadeIn(element: HTMLElement, config: AnimationConfig = {}): Animation | null {
    if (this.prefersReducedMotion()) return null;

    const keyframes = [
      { opacity: 0, transform: 'translateY(20px)' },
      { opacity: 1, transform: 'translateY(0)' }
    ];

    return this.createAnimation(element, keyframes, {
      duration: 600,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      fill: 'forwards',
      ...config
    });
  }

  fadeOut(element: HTMLElement, config: AnimationConfig = {}): Animation | null {
    if (this.prefersReducedMotion()) return null;

    const keyframes = [
      { opacity: 1, transform: 'translateY(0)' },
      { opacity: 0, transform: 'translateY(-20px)' }
    ];

    return this.createAnimation(element, keyframes, {
      duration: 400,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      fill: 'forwards',
      ...config
    });
  }

  // Slide animations
  slideInLeft(element: HTMLElement, config: AnimationConfig = {}): Animation | null {
    if (this.prefersReducedMotion()) return null;

    const keyframes = [
      { opacity: 0, transform: 'translateX(-100px)' },
      { opacity: 1, transform: 'translateX(0)' }
    ];

    return this.createAnimation(element, keyframes, {
      duration: 700,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      fill: 'forwards',
      ...config
    });
  }

  slideInRight(element: HTMLElement, config: AnimationConfig = {}): Animation | null {
    if (this.prefersReducedMotion()) return null;

    const keyframes = [
      { opacity: 0, transform: 'translateX(100px)' },
      { opacity: 1, transform: 'translateX(0)' }
    ];

    return this.createAnimation(element, keyframes, {
      duration: 700,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      fill: 'forwards',
      ...config
    });
  }

  slideInUp(element: HTMLElement, config: AnimationConfig = {}): Animation | null {
    if (this.prefersReducedMotion()) return null;

    const keyframes = [
      { opacity: 0, transform: 'translateY(100px)' },
      { opacity: 1, transform: 'translateY(0)' }
    ];

    return this.createAnimation(element, keyframes, {
      duration: 700,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
      fill: 'forwards',
      ...config
    });
  }

  // Scale animations
  scaleIn(element: HTMLElement, config: AnimationConfig = {}): Animation | null {
    if (this.prefersReducedMotion()) return null;

    const keyframes = [
      { opacity: 0, transform: 'scale(0.8)' },
      { opacity: 1, transform: 'scale(1)' }
    ];

    return this.createAnimation(element, keyframes, {
      duration: 500,
      easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
      fill: 'forwards',
      ...config
    });
  }

  // Bounce animation
  bounce(element: HTMLElement, config: AnimationConfig = {}): Animation | null {
    if (this.prefersReducedMotion()) return null;

    const keyframes = [
      { transform: 'translateY(0)' },
      { transform: 'translateY(-10px)' },
      { transform: 'translateY(0)' }
    ];

    return this.createAnimation(element, keyframes, {
      duration: 600,
      easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      ...config
    });
  }

  // Pulse animation
  pulse(element: HTMLElement, config: AnimationConfig = {}): Animation | null {
    if (this.prefersReducedMotion()) return null;

    const keyframes = [
      { transform: 'scale(1)' },
      { transform: 'scale(1.05)' },
      { transform: 'scale(1)' }
    ];

    return this.createAnimation(element, keyframes, {
      duration: 1000,
      easing: 'ease-in-out',
      ...config
    });
  }

  // Shake animation
  shake(element: HTMLElement, config: AnimationConfig = {}): Animation | null {
    if (this.prefersReducedMotion()) return null;

    const keyframes = [
      { transform: 'translateX(0)' },
      { transform: 'translateX(-10px)' },
      { transform: 'translateX(10px)' },
      { transform: 'translateX(-10px)' },
      { transform: 'translateX(10px)' },
      { transform: 'translateX(0)' }
    ];

    return this.createAnimation(element, keyframes, {
      duration: 500,
      easing: 'ease-in-out',
      ...config
    });
  }

  // Stagger animation for multiple elements
  staggerAnimation(
    elements: HTMLElement[],
    animationType: string,
    config: AnimationConfig & { staggerDelay?: number } = {}
  ): Animation[] {
    const animations: Animation[] = [];
    const staggerDelay = config.staggerDelay || 100;

    elements.forEach((element, index) => {
      const elementConfig = {
        ...config,
        delay: (config.delay || 0) + (index * staggerDelay)
      };

      const animation = this.triggerAnimation(element, animationType, elementConfig);
      if (animation) {
        animations.push(animation);
      }
    });

    return animations;
  }

  // Scroll-triggered animations
  animateOnScroll(
    element: HTMLElement,
    animationType: string,
    config: ScrollAnimationConfig = {}
  ): void {
    if (!this.intersectionObserver) return;

    element.dataset['animation'] = animationType;
    element.dataset['animationConfig'] = JSON.stringify(config);
    
    // Set initial state
    element.style.opacity = '0';
    element.style.transform = this.getInitialTransform(animationType);

    this.intersectionObserver.observe(element);
  }

  // Parallax effect
  createParallaxEffect(element: HTMLElement, speed: number = 0.5): void {
    if (this.prefersReducedMotion()) return;

    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      const parallax = scrolled * speed;
      element.style.transform = `translateY(${parallax}px)`;
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
  }

  // Hover animations
  addHoverAnimation(element: HTMLElement, animationType: string): void {
    if (this.prefersReducedMotion()) return;

    element.addEventListener('mouseenter', () => {
      this.triggerAnimation(element, animationType);
    });

    element.addEventListener('mouseleave', () => {
      element.style.transform = '';
      element.style.transition = 'transform 0.3s ease';
    });
  }

  // Private helper methods
  private createAnimation(
    element: HTMLElement,
    keyframes: Keyframe[],
    options: KeyframeAnimationOptions
  ): Animation | null {
    if (!element.animate) return null;

    return element.animate(keyframes, options);
  }

  private triggerAnimation(
    element: HTMLElement,
    animationType: string,
    config: AnimationConfig = {}
  ): Animation | null {
    switch (animationType) {
      case 'fadeIn':
        return this.fadeIn(element, config);
      case 'slideInLeft':
        return this.slideInLeft(element, config);
      case 'slideInRight':
        return this.slideInRight(element, config);
      case 'slideInUp':
        return this.slideInUp(element, config);
      case 'scaleIn':
        return this.scaleIn(element, config);
      case 'bounce':
        return this.bounce(element, config);
      case 'pulse':
        return this.pulse(element, config);
      default:
        return this.fadeIn(element, config);
    }
  }

  private getInitialTransform(animationType: string): string {
    switch (animationType) {
      case 'slideInLeft':
        return 'translateX(-100px)';
      case 'slideInRight':
        return 'translateX(100px)';
      case 'slideInUp':
        return 'translateY(100px)';
      case 'scaleIn':
        return 'scale(0.8)';
      default:
        return 'translateY(20px)';
    }
  }

  // Cleanup
  destroy(): void {
    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }
}
