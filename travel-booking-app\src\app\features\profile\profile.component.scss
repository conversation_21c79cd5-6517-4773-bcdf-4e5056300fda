.profile-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 40px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.profile-header {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 15px;
  }

  p {
    color: #718096;
    font-size: 1.1rem;
  }
}

.profile-content {
  background: white;
  border-radius: 15px;
  padding: 40px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  @media (max-width: 768px) {
    padding: 25px;
  }
}

// Profile Picture Section
.profile-picture-section {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e2e8f0;

  .profile-picture {
    display: inline-block;

    .avatar {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: #667eea;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2.5rem;
      font-weight: 600;
      margin: 0 auto 20px;
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .change-picture-btn {
      background: #edf2f7;
      color: #4a5568;
      border: none;
      padding: 8px 16px;
      border-radius: 6px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #e2e8f0;
      }
    }
  }
}

// Form Sections
.form-section {
  margin-bottom: 40px;

  h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid #667eea;
  }
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.form-group {
  margin-bottom: 20px;

  label {
    display: block;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 0.9rem;
  }

  input, select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f7fafc;

    &:focus {
      outline: none;
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    &.error {
      border-color: #e53e3e;
      background: #fed7d7;
    }
  }

  .error-message {
    color: #e53e3e;
    font-size: 0.8rem;
    margin-top: 5px;
    font-weight: 500;
  }
}

// Checkbox Styles
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9rem;
  color: #4a5568;
  line-height: 1.4;

  input[type="checkbox"] {
    display: none;
  }

  .checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    margin-right: 12px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;

    &::after {
      content: '';
      position: absolute;
      left: 5px;
      top: 2px;
      width: 4px;
      height: 8px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;

    &::after {
      opacity: 1;
    }
  }
}

// Alert Styles
.alert {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  font-weight: 500;

  &.alert-error {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
  }
}

// Form Actions
.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;

  @media (max-width: 480px) {
    flex-direction: column;
  }
}

// Password Section
.password-section {
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e2e8f0;

  h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 10px;
  }

  p {
    color: #718096;
    margin-bottom: 20px;
  }
}

// Account Actions
.account-actions {
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #e2e8f0;

  h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 20px;
  }

  .danger-zone {
    background: #fed7d7;
    border: 1px solid #feb2b2;
    border-radius: 8px;
    padding: 20px;

    h3 {
      color: #c53030;
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 10px;
    }

    p {
      color: #9c4221;
      margin-bottom: 15px;
      font-size: 0.9rem;
    }
  }
}

// Button Styles
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover:not(:disabled) {
      background: #5a67d8;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
  }

  &.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;

    &:hover:not(:disabled) {
      background: #667eea;
      color: white;
    }
  }

  &.btn-danger {
    background: #e53e3e;
    color: white;

    &:hover:not(:disabled) {
      background: #c53030;
    }
  }

  .spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
