.home-container {
  min-height: 100vh;
}

// Hero Section
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  min-height: 70vh;
  display: flex;
  align-items: center;

  .hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }
  }

  .hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }

  .hero-subtitle {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 30px;
    opacity: 0.9;
  }

  .hero-actions {
    display: flex;
    gap: 20px;

    @media (max-width: 768px) {
      justify-content: center;
    }
  }

  .hero-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  }
}

// Common Section Styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;

  h2 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #2d3748;
  }

  p {
    font-size: 1.1rem;
    color: #718096;
    max-width: 600px;
    margin: 0 auto;
  }
}

.section-footer {
  text-align: center;
  margin-top: 50px;
}

// Featured Trips Section
.featured-trips {
  padding: 80px 0;
  background: #f7fafc;

  .trips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
  }

  .trip-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .trip-image {
      position: relative;
      height: 200px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .discount-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        background: #e53e3e;
        color: white;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
      }
    }

    &:hover .trip-image img {
      transform: scale(1.05);
    }

    .trip-content {
      padding: 25px;

      h3 {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 10px;
        color: #2d3748;
      }

      p {
        color: #718096;
        margin-bottom: 15px;
        line-height: 1.5;
      }

      .trip-meta {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;

        span {
          font-size: 0.9rem;
          color: #4a5568;
          background: #edf2f7;
          padding: 4px 8px;
          border-radius: 8px;
        }
      }

      .trip-price {
        display: flex;
        align-items: center;
        gap: 10px;

        .original-price {
          text-decoration: line-through;
          color: #a0aec0;
          font-size: 0.9rem;
        }

        .current-price {
          font-size: 1.2rem;
          font-weight: 600;
          color: #38a169;
        }
      }
    }
  }
}

// Featured Blogs Section
.featured-blogs {
  padding: 80px 0;

  .blogs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
  }

  .blog-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

    .blog-image {
      height: 200px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
    }

    &:hover .blog-image img {
      transform: scale(1.05);
    }

    .blog-content {
      padding: 25px;

      .blog-meta {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;

        .category {
          background: #667eea;
          color: white;
          padding: 4px 12px;
          border-radius: 15px;
          font-size: 0.8rem;
          font-weight: 500;
        }

        .reading-time {
          color: #718096;
          font-size: 0.9rem;
        }
      }

      h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 10px;
        color: #2d3748;
        line-height: 1.4;
      }

      p {
        color: #718096;
        margin-bottom: 15px;
        line-height: 1.5;
      }

      .blog-author {
        color: #4a5568;
        font-size: 0.9rem;
      }
    }
  }
}

// Newsletter Section
.newsletter-section {
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
  padding: 60px 0;

  .newsletter-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;

    h2 {
      font-size: 2rem;
      margin-bottom: 15px;
    }

    p {
      margin-bottom: 30px;
      opacity: 0.9;
    }

    .newsletter-form {
      display: flex;
      gap: 15px;
      max-width: 400px;
      margin: 0 auto;

      @media (max-width: 768px) {
        flex-direction: column;
      }

      input {
        flex: 1;
        padding: 12px 16px;
        border: none;
        border-radius: 8px;
        font-size: 1rem;

        &:focus {
          outline: none;
          box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
}

// Button Styles
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;

  &.btn-primary {
    background: #667eea;
    color: white;

    &:hover {
      background: #5a67d8;
      transform: translateY(-2px);
    }
  }

  &.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;

    &:hover {
      background: #667eea;
      color: white;
    }
  }
}

// Loading Skeletons
.loading-skeleton {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;

  .trip-card-skeleton,
  .blog-card-skeleton {
    background: #f7fafc;
    border-radius: 15px;
    height: 350px;
    animation: pulse 1.5s ease-in-out infinite alternate;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.4;
  }
}
