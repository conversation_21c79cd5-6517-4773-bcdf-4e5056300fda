import { Component, OnInit, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { TripService, StateService } from '../../../core/services';
import { Trip, TripCategory, TripFilterDto, PaginationParameters } from '../../../core/models';

@Component({
  selector: 'app-trip-list',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <div class="trip-list-container">
      <!-- Header Section -->
      <section class="page-header">
        <div class="container">
          <h1>Discover Amazing Trips</h1>
          <p>Find your perfect adventure from our curated collection of travel experiences</p>
        </div>
      </section>

      <!-- Filters Section -->
      <section class="filters-section">
        <div class="container">
          <div class="filters-grid">
            <!-- Search -->
            <div class="filter-group">
              <label>Search Destinations</label>
              <input 
                type="text" 
                placeholder="Where do you want to go?"
                [(ngModel)]="searchTerm"
                (input)="onSearchChange()"
                class="search-input"
              />
            </div>

            <!-- Category Filter -->
            <div class="filter-group">
              <label>Category</label>
              <select [(ngModel)]="selectedCategory" (change)="onFilterChange()">
                <option value="">All Categories</option>
                @for (category of categories(); track category.id) {
                  <option [value]="category.id">{{ category.name }}</option>
                }
              </select>
            </div>

            <!-- Price Range -->
            <div class="filter-group">
              <label>Price Range</label>
              <div class="price-inputs">
                <input 
                  type="number" 
                  placeholder="Min"
                  [(ngModel)]="minPrice"
                  (input)="onFilterChange()"
                />
                <span>-</span>
                <input 
                  type="number" 
                  placeholder="Max"
                  [(ngModel)]="maxPrice"
                  (input)="onFilterChange()"
                />
              </div>
            </div>

            <!-- Duration Filter -->
            <div class="filter-group">
              <label>Duration</label>
              <select [(ngModel)]="selectedDuration" (change)="onFilterChange()">
                <option value="">Any Duration</option>
                <option value="1-3">1-3 days</option>
                <option value="4-7">4-7 days</option>
                <option value="8-14">8-14 days</option>
                <option value="15+">15+ days</option>
              </select>
            </div>

            <!-- Difficulty Filter -->
            <div class="filter-group">
              <label>Difficulty</label>
              <select [(ngModel)]="selectedDifficulty" (change)="onFilterChange()">
                <option value="">Any Difficulty</option>
                <option value="1">Easy</option>
                <option value="2">Moderate</option>
                <option value="3">Hard</option>
              </select>
            </div>

            <!-- Sort -->
            <div class="filter-group">
              <label>Sort By</label>
              <select [(ngModel)]="sortBy" (change)="onFilterChange()">
                <option value="name">Name</option>
                <option value="price">Price</option>
                <option value="duration">Duration</option>
                <option value="createdAt">Newest</option>
              </select>
            </div>
          </div>

          <div class="filter-actions">
            <button class="btn btn-outline" (click)="clearFilters()">
              Clear Filters
            </button>
            <div class="results-count">
              {{ totalResults() }} trips found
            </div>
          </div>
        </div>
      </section>

      <!-- Results Section -->
      <section class="results-section">
        <div class="container">
          @if (isLoading()) {
            <div class="loading-grid">
              @for (item of [1,2,3,4,5,6]; track item) {
                <div class="trip-card-skeleton"></div>
              }
            </div>
          } @else if (trips().length === 0) {
            <div class="no-results">
              <h3>No trips found</h3>
              <p>Try adjusting your filters or search terms</p>
            </div>
          } @else {
            <div class="trips-grid">
              @for (trip of trips(); track trip.id) {
                <div class="trip-card" [routerLink]="['/trips', trip.id]">
                  <div class="trip-image">
                    <img [src]="trip.mainImageUrl" [alt]="trip.name" />
                    @if (trip.discountPrice) {
                      <div class="discount-badge">
                        {{ calculateDiscount(trip.price, trip.discountPrice) }}% OFF
                      </div>
                    }
                    @if (trip.isFeatured) {
                      <div class="featured-badge">Featured</div>
                    }
                  </div>
                  <div class="trip-content">
                    <div class="trip-header">
                      <h3>{{ trip.name }}</h3>
                      <div class="trip-rating">
                        <!-- TODO: Add rating display -->
                        <span class="rating">4.5 ⭐</span>
                      </div>
                    </div>
                    <p class="trip-description">{{ trip.shortDescription }}</p>
                    <div class="trip-meta">
                      <span class="duration">{{ trip.duration }} days</span>
                      <span class="difficulty">{{ getDifficultyText(trip.difficulty) }}</span>
                      <span class="category">{{ trip.category.name }}</span>
                    </div>
                    <div class="trip-footer">
                      <div class="trip-price">
                        @if (trip.discountPrice) {
                          <span class="original-price">\${{ trip.price }}</span>
                          <span class="current-price">\${{ trip.discountPrice }}</span>
                        } @else {
                          <span class="current-price">\${{ trip.price }}</span>
                        }
                        <span class="price-note">per person</span>
                      </div>
                      <button class="btn btn-primary btn-sm">View Details</button>
                    </div>
                  </div>
                </div>
              }
            </div>

            <!-- Pagination -->
            @if (totalPages() > 1) {
              <div class="pagination">
                <button 
                  class="btn btn-outline"
                  [disabled]="currentPage() === 1"
                  (click)="goToPage(currentPage() - 1)"
                >
                  Previous
                </button>
                
                @for (page of getPageNumbers(); track page) {
                  <button 
                    class="btn"
                    [class.btn-primary]="page === currentPage()"
                    [class.btn-outline]="page !== currentPage()"
                    (click)="goToPage(page)"
                  >
                    {{ page }}
                  </button>
                }
                
                <button 
                  class="btn btn-outline"
                  [disabled]="currentPage() === totalPages()"
                  (click)="goToPage(currentPage() + 1)"
                >
                  Next
                </button>
              </div>
            }
          }
        </div>
      </section>
    </div>
  `,
  styleUrls: ['./trip-list.component.scss']
})
export class TripListComponent implements OnInit {
  private readonly tripService = inject(TripService);
  private readonly stateService = inject(StateService);

  // Filter signals
  searchTerm = signal<string>('');
  selectedCategory = signal<string>('');
  minPrice = signal<number | null>(null);
  maxPrice = signal<number | null>(null);
  selectedDuration = signal<string>('');
  selectedDifficulty = signal<string>('');
  sortBy = signal<string>('name');

  // Data signals
  trips = signal<Trip[]>([]);
  categories = signal<TripCategory[]>([]);
  isLoading = signal<boolean>(false);
  currentPage = signal<number>(1);
  totalResults = signal<number>(0);
  totalPages = computed(() => Math.ceil(this.totalResults() / 12));

  private searchTimeout: any;

  ngOnInit(): void {
    this.loadCategories();
    this.loadTrips();
  }

  private async loadCategories(): Promise<void> {
    try {
      const categories = await this.tripService.getTripCategories().toPromise();
      this.categories.set(categories || []);
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  }

  private async loadTrips(): Promise<void> {
    this.isLoading.set(true);
    
    try {
      const filters = this.buildFilters();
      const pagination: PaginationParameters = {
        page: this.currentPage(),
        pageSize: 12,
        sortBy: this.sortBy(),
        sortOrder: 'asc'
      };

      const result = await this.tripService.getTrips(pagination, filters).toPromise();
      
      if (result) {
        this.trips.set(result.items);
        this.totalResults.set(result.totalCount);
      }
    } catch (error) {
      console.error('Error loading trips:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  private buildFilters(): TripFilterDto {
    const filters: TripFilterDto = {};

    if (this.searchTerm()) filters.search = this.searchTerm();
    if (this.selectedCategory()) filters.categoryId = Number(this.selectedCategory());
    if (this.minPrice()) filters.minPrice = this.minPrice()!;
    if (this.maxPrice()) filters.maxPrice = this.maxPrice()!;
    if (this.selectedDifficulty()) filters.difficulty = Number(this.selectedDifficulty());

    return filters;
  }

  onSearchChange(): void {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
      this.currentPage.set(1);
      this.loadTrips();
    }, 500);
  }

  onFilterChange(): void {
    this.currentPage.set(1);
    this.loadTrips();
  }

  clearFilters(): void {
    this.searchTerm.set('');
    this.selectedCategory.set('');
    this.minPrice.set(null);
    this.maxPrice.set(null);
    this.selectedDuration.set('');
    this.selectedDifficulty.set('');
    this.sortBy.set('name');
    this.currentPage.set(1);
    this.loadTrips();
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
      this.loadTrips();
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }

  getPageNumbers(): number[] {
    const total = this.totalPages();
    const current = this.currentPage();
    const pages: number[] = [];

    if (total <= 7) {
      for (let i = 1; i <= total; i++) {
        pages.push(i);
      }
    } else {
      if (current <= 4) {
        for (let i = 1; i <= 5; i++) pages.push(i);
        pages.push(-1); // ellipsis
        pages.push(total);
      } else if (current >= total - 3) {
        pages.push(1);
        pages.push(-1); // ellipsis
        for (let i = total - 4; i <= total; i++) pages.push(i);
      } else {
        pages.push(1);
        pages.push(-1); // ellipsis
        for (let i = current - 1; i <= current + 1; i++) pages.push(i);
        pages.push(-1); // ellipsis
        pages.push(total);
      }
    }

    return pages;
  }

  calculateDiscount(originalPrice: number, discountPrice: number): number {
    return Math.round(((originalPrice - discountPrice) / originalPrice) * 100);
  }

  getDifficultyText(difficulty: number): string {
    const difficultyMap = { 1: 'Easy', 2: 'Moderate', 3: 'Hard' };
    return difficultyMap[difficulty as keyof typeof difficultyMap] || 'Unknown';
  }
}
