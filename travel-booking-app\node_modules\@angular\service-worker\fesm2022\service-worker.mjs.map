{"version": 3, "file": "service-worker.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/service-worker/src/low_level.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/service-worker/src/push.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/service-worker/src/update.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/service-worker/src/provider.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/service-worker/src/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ApplicationRef, type Injector, ɵRuntimeError as RuntimeError} from '@angular/core';\nimport {Observable, Subject} from 'rxjs';\nimport {filter, map, switchMap, take} from 'rxjs/operators';\n\nimport {RuntimeErrorCode} from './errors';\n\nexport const ERR_SW_NOT_SUPPORTED = 'Service workers are disabled or not supported by this browser';\n\n/**\n * An event emitted when the service worker has checked the version of the app on the server and it\n * didn't find a new version that it doesn't have already downloaded.\n *\n * @see {@link /ecosystem/service-workers/communications Service Worker Communication Guide}\n\n *\n * @publicApi\n */\nexport interface NoNewVersionDetectedEvent {\n  type: 'NO_NEW_VERSION_DETECTED';\n  version: {hash: string; appData?: Object};\n}\n\n/**\n * An event emitted when the service worker has detected a new version of the app on the server and\n * is about to start downloading it.\n *\n * @see {@link /ecosystem/service-workers/communications Service Worker Communication Guide}\n\n *\n * @publicApi\n */\nexport interface VersionDetectedEvent {\n  type: 'VERSION_DETECTED';\n  version: {hash: string; appData?: object};\n}\n\n/**\n * An event emitted when the installation of a new version failed.\n * It may be used for logging/monitoring purposes.\n *\n * @see {@link /ecosystem/service-workers/communications Service Worker Communication Guide}\n *a\n * @publicApi\n */\nexport interface VersionInstallationFailedEvent {\n  type: 'VERSION_INSTALLATION_FAILED';\n  version: {hash: string; appData?: object};\n  error: string;\n}\n\n/**\n * An event emitted when a new version of the app is available.\n *\n * @see {@link /ecosystem/service-workers/communications Service Worker Communication Guide}\n\n *\n * @publicApi\n */\nexport interface VersionReadyEvent {\n  type: 'VERSION_READY';\n  currentVersion: {hash: string; appData?: object};\n  latestVersion: {hash: string; appData?: object};\n}\n\n/**\n * A union of all event types that can be emitted by\n * {@link SwUpdate#versionUpdates}.\n *\n * @publicApi\n */\nexport type VersionEvent =\n  | VersionDetectedEvent\n  | VersionInstallationFailedEvent\n  | VersionReadyEvent\n  | NoNewVersionDetectedEvent;\n\n/**\n * An event emitted when the version of the app used by the service worker to serve this client is\n * in a broken state that cannot be recovered from and a full page reload is required.\n *\n * For example, the service worker may not be able to retrieve a required resource, neither from the\n * cache nor from the server. This could happen if a new version is deployed to the server and the\n * service worker cache has been partially cleaned by the browser, removing some files of a previous\n * app version but not all.\n *\n * @see {@link /ecosystem/service-workers/communications Service Worker Communication Guide}\n\n *\n * @publicApi\n */\nexport interface UnrecoverableStateEvent {\n  type: 'UNRECOVERABLE_STATE';\n  reason: string;\n}\n\n/**\n * An event emitted when a `PushEvent` is received by the service worker.\n */\nexport interface PushEvent {\n  type: 'PUSH';\n  data: any;\n}\n\nexport type IncomingEvent = UnrecoverableStateEvent | VersionEvent;\n\nexport interface TypedEvent {\n  type: string;\n}\n\ntype OperationCompletedEvent =\n  | {\n      type: 'OPERATION_COMPLETED';\n      nonce: number;\n      result: boolean;\n    }\n  | {\n      type: 'OPERATION_COMPLETED';\n      nonce: number;\n      result?: undefined;\n      error: string;\n    };\n\n/**\n * @publicApi\n */\nexport class NgswCommChannel {\n  readonly worker: Observable<ServiceWorker>;\n\n  readonly registration: Observable<ServiceWorkerRegistration>;\n\n  readonly events: Observable<TypedEvent>;\n\n  constructor(\n    private serviceWorker: ServiceWorkerContainer | undefined,\n    injector?: Injector,\n  ) {\n    if (!serviceWorker) {\n      this.worker =\n        this.events =\n        this.registration =\n          new Observable<never>((subscriber) =>\n            subscriber.error(\n              new RuntimeError(\n                RuntimeErrorCode.SERVICE_WORKER_DISABLED_OR_NOT_SUPPORTED_BY_THIS_BROWSER,\n                (typeof ngDevMode === 'undefined' || ngDevMode) && ERR_SW_NOT_SUPPORTED,\n              ),\n            ),\n          );\n    } else {\n      let currentWorker: ServiceWorker | null = null;\n      const workerSubject = new Subject<ServiceWorker>();\n      this.worker = new Observable((subscriber) => {\n        if (currentWorker !== null) {\n          subscriber.next(currentWorker);\n        }\n        return workerSubject.subscribe((v) => subscriber.next(v));\n      });\n      const updateController = () => {\n        const {controller} = serviceWorker;\n        if (controller === null) {\n          return;\n        }\n        currentWorker = controller;\n        workerSubject.next(currentWorker);\n      };\n      serviceWorker.addEventListener('controllerchange', updateController);\n      updateController();\n\n      this.registration = <Observable<ServiceWorkerRegistration>>(\n        this.worker.pipe(switchMap(() => serviceWorker.getRegistration()))\n      );\n\n      const _events = new Subject<TypedEvent>();\n      this.events = _events.asObservable();\n\n      const messageListener = (event: MessageEvent) => {\n        const {data} = event;\n        if (data?.type) {\n          _events.next(data);\n        }\n      };\n      serviceWorker.addEventListener('message', messageListener);\n\n      // The injector is optional to avoid breaking changes.\n      const appRef = injector?.get(ApplicationRef, null, {optional: true});\n      appRef?.onDestroy(() => {\n        serviceWorker.removeEventListener('controllerchange', updateController);\n        serviceWorker.removeEventListener('message', messageListener);\n      });\n    }\n  }\n\n  postMessage(action: string, payload: Object): Promise<void> {\n    return new Promise<void>((resolve) => {\n      this.worker.pipe(take(1)).subscribe((sw) => {\n        sw.postMessage({\n          action,\n          ...payload,\n        });\n\n        resolve();\n      });\n    });\n  }\n\n  postMessageWithOperation(\n    type: string,\n    payload: Object,\n    operationNonce: number,\n  ): Promise<boolean> {\n    const waitForOperationCompleted = this.waitForOperationCompleted(operationNonce);\n    const postMessage = this.postMessage(type, payload);\n    return Promise.all([postMessage, waitForOperationCompleted]).then(([, result]) => result);\n  }\n\n  generateNonce(): number {\n    return Math.round(Math.random() * 10000000);\n  }\n\n  eventsOfType<T extends TypedEvent>(type: T['type'] | T['type'][]): Observable<T> {\n    let filterFn: (event: TypedEvent) => event is T;\n    if (typeof type === 'string') {\n      filterFn = (event: TypedEvent): event is T => event.type === type;\n    } else {\n      filterFn = (event: TypedEvent): event is T => type.includes(event.type);\n    }\n    return this.events.pipe(filter(filterFn));\n  }\n\n  nextEventOfType<T extends TypedEvent>(type: T['type']): Observable<T> {\n    return this.eventsOfType(type).pipe(take(1));\n  }\n\n  waitForOperationCompleted(nonce: number): Promise<boolean> {\n    return new Promise<boolean>((resolve, reject) => {\n      this.eventsOfType<OperationCompletedEvent>('OPERATION_COMPLETED')\n        .pipe(\n          filter((event) => event.nonce === nonce),\n          take(1),\n          map((event) => {\n            if (event.result !== undefined) {\n              return event.result;\n            }\n            throw new Error(event.error!);\n          }),\n        )\n        .subscribe({\n          next: resolve,\n          error: reject,\n        });\n    });\n  }\n\n  get isEnabled(): boolean {\n    return !!this.serviceWorker;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, ɵRuntimeError as RuntimeError} from '@angular/core';\nimport {NEVER, Observable, Subject} from 'rxjs';\nimport {map, switchMap, take} from 'rxjs/operators';\n\nimport {RuntimeErrorCode} from './errors';\nimport {ERR_SW_NOT_SUPPORTED, NgswCommChannel, PushEvent} from './low_level';\n\n/**\n * Subscribe and listen to\n * [Web Push\n * Notifications](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices) through\n * Angular Service Worker.\n *\n * @usageNotes\n *\n * You can inject a `SwPush` instance into any component or service\n * as a dependency.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"inject-sw-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * To subscribe, call `SwPush.requestSubscription()`, which asks the user for permission.\n * The call returns a `Promise` with a new\n * [`PushSubscription`](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n * instance.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * A request is rejected if the user denies permission, or if the browser\n * blocks or does not support the Push API or ServiceWorkers.\n * Check `SwPush.isEnabled` to confirm status.\n *\n * Invoke Push Notifications by pushing a message with the following payload.\n *\n * ```ts\n * {\n *   \"notification\": {\n *     \"actions\": NotificationAction[],\n *     \"badge\": USVString,\n *     \"body\": DOMString,\n *     \"data\": any,\n *     \"dir\": \"auto\"|\"ltr\"|\"rtl\",\n *     \"icon\": USVString,\n *     \"image\": USVString,\n *     \"lang\": DOMString,\n *     \"renotify\": boolean,\n *     \"requireInteraction\": boolean,\n *     \"silent\": boolean,\n *     \"tag\": DOMString,\n *     \"timestamp\": DOMTimeStamp,\n *     \"title\": DOMString,\n *     \"vibrate\": number[]\n *   }\n * }\n * ```\n *\n * Only `title` is required. See `Notification`\n * [instance\n * properties](https://developer.mozilla.org/en-US/docs/Web/API/Notification#Instance_properties).\n *\n * While the subscription is active, Service Worker listens for\n * [PushEvent](https://developer.mozilla.org/en-US/docs/Web/API/PushEvent)\n * occurrences and creates\n * [Notification](https://developer.mozilla.org/en-US/docs/Web/API/Notification)\n * instances in response.\n *\n * Unsubscribe using `SwPush.unsubscribe()`.\n *\n * An application can subscribe to `SwPush.notificationClicks` observable to be notified when a user\n * clicks on a notification. For example:\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-notification-clicks\"\n * header=\"app.component.ts\"></code-example>\n *\n * You can read more on handling notification clicks in the [Service worker notifications\n * guide](ecosystem/service-workers/push-notifications).\n *\n * @see [Push Notifications](https://developers.google.com/web/fundamentals/codelabs/push-notifications/)\n * @see [Angular Push Notifications](https://blog.angular-university.io/angular-push-notifications/)\n * @see [MDN: Push API](https://developer.mozilla.org/en-US/docs/Web/API/Push_API)\n * @see [MDN: Notifications API](https://developer.mozilla.org/en-US/docs/Web/API/Notifications_API)\n * @see [MDN: Web Push API Notifications best practices](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices)\n *\n * @publicApi\n */\n@Injectable()\nexport class SwPush {\n  /**\n   * Emits the payloads of the received push notification messages.\n   */\n  readonly messages: Observable<object>;\n\n  /**\n   * Emits the payloads of the received push notification messages as well as the action the user\n   * interacted with. If no action was used the `action` property contains an empty string `''`.\n   *\n   * Note that the `notification` property does **not** contain a\n   * [Notification][Mozilla Notification] object but rather a\n   * [NotificationOptions](https://notifications.spec.whatwg.org/#dictdef-notificationoptions)\n   * object that also includes the `title` of the [Notification][Mozilla Notification] object.\n   *\n   * [Mozilla Notification]: https://developer.mozilla.org/en-US/docs/Web/API/Notification\n   */\n  readonly notificationClicks: Observable<{\n    action: string;\n    notification: NotificationOptions & {\n      title: string;\n    };\n  }>;\n\n  /**\n   * Emits the currently active\n   * [PushSubscription](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n   * associated to the Service Worker registration or `null` if there is no subscription.\n   */\n  readonly subscription: Observable<PushSubscription | null>;\n\n  /**\n   * True if the Service Worker is enabled (supported by the browser and enabled via\n   * `ServiceWorkerModule`).\n   */\n  get isEnabled(): boolean {\n    return this.sw.isEnabled;\n  }\n\n  private pushManager: Observable<PushManager> | null = null;\n  private subscriptionChanges = new Subject<PushSubscription | null>();\n\n  constructor(private sw: NgswCommChannel) {\n    if (!sw.isEnabled) {\n      this.messages = NEVER;\n      this.notificationClicks = NEVER;\n      this.subscription = NEVER;\n      return;\n    }\n\n    this.messages = this.sw.eventsOfType<PushEvent>('PUSH').pipe(map((message) => message.data));\n\n    this.notificationClicks = this.sw\n      .eventsOfType('NOTIFICATION_CLICK')\n      .pipe(map((message: any) => message.data));\n\n    this.pushManager = this.sw.registration.pipe(map((registration) => registration.pushManager));\n\n    const workerDrivenSubscriptions = this.pushManager.pipe(\n      switchMap((pm) => pm.getSubscription()),\n    );\n    this.subscription = new Observable((subscriber) => {\n      const workerDrivenSubscription = workerDrivenSubscriptions.subscribe(subscriber);\n      const subscriptionChanges = this.subscriptionChanges.subscribe(subscriber);\n      return () => {\n        workerDrivenSubscription.unsubscribe();\n        subscriptionChanges.unsubscribe();\n      };\n    });\n  }\n\n  /**\n   * Subscribes to Web Push Notifications,\n   * after requesting and receiving user permission.\n   *\n   * @param options An object containing the `serverPublicKey` string.\n   * @returns A Promise that resolves to the new subscription object.\n   */\n  requestSubscription(options: {serverPublicKey: string}): Promise<PushSubscription> {\n    if (!this.sw.isEnabled || this.pushManager === null) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const pushOptions: PushSubscriptionOptionsInit = {userVisibleOnly: true};\n    let key = this.decodeBase64(options.serverPublicKey.replace(/_/g, '/').replace(/-/g, '+'));\n    let applicationServerKey = new Uint8Array(new ArrayBuffer(key.length));\n    for (let i = 0; i < key.length; i++) {\n      applicationServerKey[i] = key.charCodeAt(i);\n    }\n    pushOptions.applicationServerKey = applicationServerKey;\n\n    return new Promise((resolve, reject) => {\n      this.pushManager!.pipe(\n        switchMap((pm) => pm.subscribe(pushOptions)),\n        take(1),\n      ).subscribe({\n        next: (sub) => {\n          this.subscriptionChanges.next(sub);\n          resolve(sub);\n        },\n        error: reject,\n      });\n    });\n  }\n\n  /**\n   * Unsubscribes from Service Worker push notifications.\n   *\n   * @returns A Promise that is resolved when the operation succeeds, or is rejected if there is no\n   *          active subscription or the unsubscribe operation fails.\n   */\n  unsubscribe(): Promise<void> {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n\n    const doUnsubscribe = (sub: PushSubscription | null) => {\n      if (sub === null) {\n        throw new RuntimeError(\n          RuntimeErrorCode.NOT_SUBSCRIBED_TO_PUSH_NOTIFICATIONS,\n          (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            'Not subscribed to push notifications.',\n        );\n      }\n\n      return sub.unsubscribe().then((success) => {\n        if (!success) {\n          throw new RuntimeError(\n            RuntimeErrorCode.PUSH_SUBSCRIPTION_UNSUBSCRIBE_FAILED,\n            (typeof ngDevMode === 'undefined' || ngDevMode) && 'Unsubscribe failed!',\n          );\n        }\n\n        this.subscriptionChanges.next(null);\n      });\n    };\n\n    return new Promise((resolve, reject) => {\n      this.subscription\n        .pipe(take(1), switchMap(doUnsubscribe))\n        .subscribe({next: resolve, error: reject});\n    });\n  }\n\n  private decodeBase64(input: string): string {\n    return atob(input);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, ɵRuntimeError as RuntimeError} from '@angular/core';\nimport {NEVER, Observable} from 'rxjs';\n\nimport {RuntimeErrorCode} from './errors';\nimport {\n  ERR_SW_NOT_SUPPORTED,\n  NgswCommChannel,\n  UnrecoverableStateEvent,\n  VersionEvent,\n} from './low_level';\n\n/**\n * Subscribe to update notifications from the Service Worker, trigger update\n * checks, and forcibly activate updates.\n *\n * @see {@link /ecosystem/service-workers/communications Service Worker Communication Guide}\n *\n * @publicApi\n */\n@Injectable()\nexport class SwUpdate {\n  /**\n   * Emits a `VersionDetectedEvent` event whenever a new version is detected on the server.\n   *\n   * Emits a `VersionInstallationFailedEvent` event whenever checking for or downloading a new\n   * version fails.\n   *\n   * Emits a `VersionReadyEvent` event whenever a new version has been downloaded and is ready for\n   * activation.\n   */\n  readonly versionUpdates: Observable<VersionEvent>;\n\n  /**\n   * Emits an `UnrecoverableStateEvent` event whenever the version of the app used by the service\n   * worker to serve this client is in a broken state that cannot be recovered from without a full\n   * page reload.\n   */\n  readonly unrecoverable: Observable<UnrecoverableStateEvent>;\n\n  /**\n   * True if the Service Worker is enabled (supported by the browser and enabled via\n   * `ServiceWorkerModule`).\n   */\n  get isEnabled(): boolean {\n    return this.sw.isEnabled;\n  }\n\n  private ongoingCheckForUpdate: Promise<boolean> | null = null;\n\n  constructor(private sw: NgswCommChannel) {\n    if (!sw.isEnabled) {\n      this.versionUpdates = NEVER;\n      this.unrecoverable = NEVER;\n      return;\n    }\n    this.versionUpdates = this.sw.eventsOfType<VersionEvent>([\n      'VERSION_DETECTED',\n      'VERSION_INSTALLATION_FAILED',\n      'VERSION_READY',\n      'NO_NEW_VERSION_DETECTED',\n    ]);\n    this.unrecoverable = this.sw.eventsOfType<UnrecoverableStateEvent>('UNRECOVERABLE_STATE');\n  }\n\n  /**\n   * Checks for an update and waits until the new version is downloaded from the server and ready\n   * for activation.\n   *\n   * @returns a promise that\n   * - resolves to `true` if a new version was found and is ready to be activated.\n   * - resolves to `false` if no new version was found\n   * - rejects if any error occurs\n   */\n  checkForUpdate(): Promise<boolean> {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    if (this.ongoingCheckForUpdate) {\n      return this.ongoingCheckForUpdate;\n    }\n    const nonce = this.sw.generateNonce();\n    this.ongoingCheckForUpdate = this.sw\n      .postMessageWithOperation('CHECK_FOR_UPDATES', {nonce}, nonce)\n      .finally(() => {\n        this.ongoingCheckForUpdate = null;\n      });\n    return this.ongoingCheckForUpdate;\n  }\n\n  /**\n   * Updates the current client (i.e. browser tab) to the latest version that is ready for\n   * activation.\n   *\n   * In most cases, you should not use this method and instead should update a client by reloading\n   * the page.\n   *\n   * <div class=\"docs-alert docs-alert-important\">\n   *\n   * Updating a client without reloading can easily result in a broken application due to a version\n   * mismatch between the application shell and other page resources,\n   * such as lazy-loaded chunks, whose filenames may change between\n   * versions.\n   *\n   * Only use this method, if you are certain it is safe for your specific use case.\n   *\n   * </div>\n   *\n   * @returns a promise that\n   *  - resolves to `true` if an update was activated successfully\n   *  - resolves to `false` if no update was available (for example, the client was already on the\n   *    latest version).\n   *  - rejects if any error occurs\n   */\n  activateUpdate(): Promise<boolean> {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(\n        new RuntimeError(\n          RuntimeErrorCode.SERVICE_WORKER_DISABLED_OR_NOT_SUPPORTED_BY_THIS_BROWSER,\n          (typeof ngDevMode === 'undefined' || ngDevMode) && ERR_SW_NOT_SUPPORTED,\n        ),\n      );\n    }\n    const nonce = this.sw.generateNonce();\n    return this.sw.postMessageWithOperation('ACTIVATE_UPDATE', {nonce}, nonce);\n  }\n}\n", "/*!\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ApplicationRef,\n  EnvironmentProviders,\n  inject,\n  InjectionToken,\n  Injector,\n  makeEnvironmentProviders,\n  NgZone,\n  provideAppInitializer,\n  ɵRuntimeError as RuntimeError,\n  ɵformatRuntimeError as formatRuntimeError,\n} from '@angular/core';\nimport type {Observable} from 'rxjs';\n\nimport {NgswCommChannel} from './low_level';\nimport {SwPush} from './push';\nimport {SwUpdate} from './update';\nimport {RuntimeErrorCode} from './errors';\n\nexport const SCRIPT = new InjectionToken<string>(ngDevMode ? 'NGSW_REGISTER_SCRIPT' : '');\n\nexport function ngswAppInitializer(): void {\n  if (typeof ngServerMode !== 'undefined' && ngServerMode) {\n    return;\n  }\n\n  const options = inject(SwRegistrationOptions);\n\n  if (!('serviceWorker' in navigator && options.enabled !== false)) {\n    return;\n  }\n\n  const script = inject(SCRIPT);\n  const ngZone = inject(NgZone);\n  const appRef = inject(ApplicationRef);\n\n  // Set up the `controllerchange` event listener outside of\n  // the Angular zone to avoid unnecessary change detections,\n  // as this event has no impact on view updates.\n  ngZone.runOutsideAngular(() => {\n    // Wait for service worker controller changes, and fire an INITIALIZE action when a new SW\n    // becomes active. This allows the SW to initialize itself even if there is no application\n    // traffic.\n    const sw = navigator.serviceWorker;\n    const onControllerChange = () => sw.controller?.postMessage({action: 'INITIALIZE'});\n\n    sw.addEventListener('controllerchange', onControllerChange);\n\n    appRef.onDestroy(() => {\n      sw.removeEventListener('controllerchange', onControllerChange);\n    });\n  });\n\n  // Run outside the Angular zone to avoid preventing the app from stabilizing (especially\n  // given that some registration strategies wait for the app to stabilize).\n  ngZone.runOutsideAngular(() => {\n    let readyToRegister: Promise<void>;\n\n    const {registrationStrategy} = options;\n    if (typeof registrationStrategy === 'function') {\n      readyToRegister = new Promise((resolve) => registrationStrategy().subscribe(() => resolve()));\n    } else {\n      const [strategy, ...args] = (registrationStrategy || 'registerWhenStable:30000').split(':');\n\n      switch (strategy) {\n        case 'registerImmediately':\n          readyToRegister = Promise.resolve();\n          break;\n        case 'registerWithDelay':\n          readyToRegister = delayWithTimeout(+args[0] || 0);\n          break;\n        case 'registerWhenStable':\n          readyToRegister = Promise.race([appRef.whenStable(), delayWithTimeout(+args[0])]);\n          break;\n        default:\n          // Unknown strategy.\n          throw new RuntimeError(\n            RuntimeErrorCode.UNKNOWN_REGISTRATION_STRATEGY,\n            (typeof ngDevMode === 'undefined' || ngDevMode) &&\n              `Unknown ServiceWorker registration strategy: ${options.registrationStrategy}`,\n          );\n      }\n    }\n\n    // Don't return anything to avoid blocking the application until the SW is registered.\n    // Catch and log the error if SW registration fails to avoid uncaught rejection warning.\n    readyToRegister.then(() => {\n      // If the registration strategy has resolved after the application has\n      // been explicitly destroyed by the user (e.g., by navigating away to\n      // another application), we simply should not register the worker.\n      if (appRef.destroyed) {\n        return;\n      }\n\n      navigator.serviceWorker\n        .register(script, {scope: options.scope})\n        .catch((err) =>\n          console.error(\n            formatRuntimeError(\n              RuntimeErrorCode.SERVICE_WORKER_REGISTRATION_FAILED,\n              (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                'Service worker registration failed with: ' + err,\n            ),\n          ),\n        );\n    });\n  });\n}\n\nfunction delayWithTimeout(timeout: number): Promise<void> {\n  return new Promise((resolve) => setTimeout(resolve, timeout));\n}\n\nexport function ngswCommChannelFactory(\n  opts: SwRegistrationOptions,\n  injector: Injector,\n): NgswCommChannel {\n  const isBrowser = !(typeof ngServerMode !== 'undefined' && ngServerMode);\n\n  return new NgswCommChannel(\n    isBrowser && opts.enabled !== false ? navigator.serviceWorker : undefined,\n    injector,\n  );\n}\n\n/**\n * Token that can be used to provide options for `ServiceWorkerModule` outside of\n * `ServiceWorkerModule.register()`.\n *\n * You can use this token to define a provider that generates the registration options at runtime,\n * for example via a function call:\n *\n * {@example service-worker/registration-options/module.ts region=\"registration-options\"\n *     header=\"app.module.ts\"}\n *\n * @publicApi\n */\nexport abstract class SwRegistrationOptions {\n  /**\n   * Whether the ServiceWorker will be registered and the related services (such as `SwPush` and\n   * `SwUpdate`) will attempt to communicate and interact with it.\n   *\n   * Default: true\n   */\n  enabled?: boolean;\n\n  /**\n   * A URL that defines the ServiceWorker's registration scope; that is, what range of URLs it can\n   * control. It will be used when calling\n   * [ServiceWorkerContainer#register()](https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register).\n   */\n  scope?: string;\n\n  /**\n   * Defines the ServiceWorker registration strategy, which determines when it will be registered\n   * with the browser.\n   *\n   * The default behavior of registering once the application stabilizes (i.e. as soon as there are\n   * no pending micro- and macro-tasks) is designed to register the ServiceWorker as soon as\n   * possible but without affecting the application's first time load.\n   *\n   * Still, there might be cases where you want more control over when the ServiceWorker is\n   * registered (for example, there might be a long-running timeout or polling interval, preventing\n   * the app from stabilizing). The available option are:\n   *\n   * - `registerWhenStable:<timeout>`: Register as soon as the application stabilizes (no pending\n   *     micro-/macro-tasks) but no later than `<timeout>` milliseconds. If the app hasn't\n   *     stabilized after `<timeout>` milliseconds (for example, due to a recurrent asynchronous\n   *     task), the ServiceWorker will be registered anyway.\n   *     If `<timeout>` is omitted, the ServiceWorker will only be registered once the app\n   *     stabilizes.\n   * - `registerImmediately`: Register immediately.\n   * - `registerWithDelay:<timeout>`: Register with a delay of `<timeout>` milliseconds. For\n   *     example, use `registerWithDelay:5000` to register the ServiceWorker after 5 seconds. If\n   *     `<timeout>` is omitted, is defaults to `0`, which will register the ServiceWorker as soon\n   *     as possible but still asynchronously, once all pending micro-tasks are completed.\n   * - An Observable factory function: A function that returns an `Observable`.\n   *     The function will be used at runtime to obtain and subscribe to the `Observable` and the\n   *     ServiceWorker will be registered as soon as the first value is emitted.\n   *\n   * Default: 'registerWhenStable:30000'\n   */\n  registrationStrategy?: string | (() => Observable<unknown>);\n}\n\n/**\n * @publicApi\n *\n * Sets up providers to register the given Angular Service Worker script.\n *\n * If `enabled` is set to `false` in the given options, the module will behave as if service\n * workers are not supported by the browser, and the service worker will not be registered.\n *\n * Example usage:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideServiceWorker('ngsw-worker.js')\n *   ],\n * });\n * ```\n */\nexport function provideServiceWorker(\n  script: string,\n  options: SwRegistrationOptions = {},\n): EnvironmentProviders {\n  return makeEnvironmentProviders([\n    SwPush,\n    SwUpdate,\n    {provide: SCRIPT, useValue: script},\n    {provide: SwRegistrationOptions, useValue: options},\n    {\n      provide: NgswCommChannel,\n      useFactory: ngswCommChannelFactory,\n      deps: [SwRegistrationOptions, Injector],\n    },\n    provideAppInitializer(ngswAppInitializer),\n  ]);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ModuleWithProviders, NgModule} from '@angular/core';\n\nimport {provideServiceWorker, SwRegistrationOptions} from './provider';\nimport {SwPush} from './push';\nimport {SwUpdate} from './update';\n\n/**\n * @publicApi\n */\n@NgModule({providers: [SwPush, SwUpdate]})\nexport class ServiceWorkerModule {\n  /**\n   * Register the given Angular Service Worker script.\n   *\n   * If `enabled` is set to `false` in the given options, the module will behave as if service\n   * workers are not supported by the browser, and the service worker will not be registered.\n   */\n  static register(\n    script: string,\n    options: SwRegistrationOptions = {},\n  ): ModuleWithProviders<ServiceWorkerModule> {\n    return {\n      ngModule: ServiceWorkerModule,\n      providers: [provideServiceWorker(script, options)],\n    };\n  }\n}\n"], "names": ["RuntimeError", "i1.NgswCommChannel", "formatRuntimeError"], "mappings": ";;;;;;;;;;;AAcO,MAAM,oBAAoB,GAAG,+DAA+D;AAoHnG;;AAEG;MACU,eAAe,CAAA;AAQhB,IAAA,aAAA;AAPD,IAAA,MAAM;AAEN,IAAA,YAAY;AAEZ,IAAA,MAAM;IAEf,WACU,CAAA,aAAiD,EACzD,QAAmB,EAAA;QADX,IAAa,CAAA,aAAA,GAAb,aAAa;QAGrB,IAAI,CAAC,aAAa,EAAE;AAClB,YAAA,IAAI,CAAC,MAAM;AACT,gBAAA,IAAI,CAAC,MAAM;AACX,oBAAA,IAAI,CAAC,YAAY;wBACf,IAAI,UAAU,CAAQ,CAAC,UAAU,KAC/B,UAAU,CAAC,KAAK,CACd,IAAIA,aAAY,CAEd,IAAA,kFAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,oBAAoB,CACxE,CACF,CACF;;aACA;YACL,IAAI,aAAa,GAAyB,IAAI;AAC9C,YAAA,MAAM,aAAa,GAAG,IAAI,OAAO,EAAiB;YAClD,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,CAAC,UAAU,KAAI;AAC1C,gBAAA,IAAI,aAAa,KAAK,IAAI,EAAE;AAC1B,oBAAA,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC;;AAEhC,gBAAA,OAAO,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3D,aAAC,CAAC;YACF,MAAM,gBAAgB,GAAG,MAAK;AAC5B,gBAAA,MAAM,EAAC,UAAU,EAAC,GAAG,aAAa;AAClC,gBAAA,IAAI,UAAU,KAAK,IAAI,EAAE;oBACvB;;gBAEF,aAAa,GAAG,UAAU;AAC1B,gBAAA,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC;AACnC,aAAC;AACD,YAAA,aAAa,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;AACpE,YAAA,gBAAgB,EAAE;YAElB,IAAI,CAAC,YAAY,IACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,aAAa,CAAC,eAAe,EAAE,CAAC,CAAC,CACnE;AAED,YAAA,MAAM,OAAO,GAAG,IAAI,OAAO,EAAc;AACzC,YAAA,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,EAAE;AAEpC,YAAA,MAAM,eAAe,GAAG,CAAC,KAAmB,KAAI;AAC9C,gBAAA,MAAM,EAAC,IAAI,EAAC,GAAG,KAAK;AACpB,gBAAA,IAAI,IAAI,EAAE,IAAI,EAAE;AACd,oBAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;;AAEtB,aAAC;AACD,YAAA,aAAa,CAAC,gBAAgB,CAAC,SAAS,EAAE,eAAe,CAAC;;AAG1D,YAAA,MAAM,MAAM,GAAG,QAAQ,EAAE,GAAG,CAAC,cAAc,EAAE,IAAI,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACpE,YAAA,MAAM,EAAE,SAAS,CAAC,MAAK;AACrB,gBAAA,aAAa,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;AACvE,gBAAA,aAAa,CAAC,mBAAmB,CAAC,SAAS,EAAE,eAAe,CAAC;AAC/D,aAAC,CAAC;;;IAIN,WAAW,CAAC,MAAc,EAAE,OAAe,EAAA;AACzC,QAAA,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;AACnC,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,KAAI;gBACzC,EAAE,CAAC,WAAW,CAAC;oBACb,MAAM;AACN,oBAAA,GAAG,OAAO;AACX,iBAAA,CAAC;AAEF,gBAAA,OAAO,EAAE;AACX,aAAC,CAAC;AACJ,SAAC,CAAC;;AAGJ,IAAA,wBAAwB,CACtB,IAAY,EACZ,OAAe,EACf,cAAsB,EAAA;QAEtB,MAAM,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC;QAChF,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC;QACnD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,MAAM,CAAC;;IAG3F,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC;;AAG7C,IAAA,YAAY,CAAuB,IAA6B,EAAA;AAC9D,QAAA,IAAI,QAA2C;AAC/C,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,QAAQ,GAAG,CAAC,KAAiB,KAAiB,KAAK,CAAC,IAAI,KAAK,IAAI;;aAC5D;AACL,YAAA,QAAQ,GAAG,CAAC,KAAiB,KAAiB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;;QAEzE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;;AAG3C,IAAA,eAAe,CAAuB,IAAe,EAAA;AACnD,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;AAG9C,IAAA,yBAAyB,CAAC,KAAa,EAAA;QACrC,OAAO,IAAI,OAAO,CAAU,CAAC,OAAO,EAAE,MAAM,KAAI;AAC9C,YAAA,IAAI,CAAC,YAAY,CAA0B,qBAAqB;iBAC7D,IAAI,CACH,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EACxC,IAAI,CAAC,CAAC,CAAC,EACP,GAAG,CAAC,CAAC,KAAK,KAAI;AACZ,gBAAA,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,EAAE;oBAC9B,OAAO,KAAK,CAAC,MAAM;;AAErB,gBAAA,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,KAAM,CAAC;AAC/B,aAAC,CAAC;AAEH,iBAAA,SAAS,CAAC;AACT,gBAAA,IAAI,EAAE,OAAO;AACb,gBAAA,KAAK,EAAE,MAAM;AACd,aAAA,CAAC;AACN,SAAC,CAAC;;AAGJ,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa;;AAE9B;;ACzPD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8EG;MAEU,MAAM,CAAA;AA0CG,IAAA,EAAA;AAzCpB;;AAEG;AACM,IAAA,QAAQ;AAEjB;;;;;;;;;;AAUG;AACM,IAAA,kBAAkB;AAO3B;;;;AAIG;AACM,IAAA,YAAY;AAErB;;;AAGG;AACH,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS;;IAGlB,WAAW,GAAmC,IAAI;AAClD,IAAA,mBAAmB,GAAG,IAAI,OAAO,EAA2B;AAEpE,IAAA,WAAA,CAAoB,EAAmB,EAAA;QAAnB,IAAE,CAAA,EAAA,GAAF,EAAE;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK;AAC/B,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK;YACzB;;QAGF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAY,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;AAE5F,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;aAC5B,YAAY,CAAC,oBAAoB;AACjC,aAAA,IAAI,CAAC,GAAG,CAAC,CAAC,OAAY,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;QAE5C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,YAAY,KAAK,YAAY,CAAC,WAAW,CAAC,CAAC;QAE7F,MAAM,yBAAyB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CACrD,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,eAAe,EAAE,CAAC,CACxC;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,UAAU,CAAC,CAAC,UAAU,KAAI;YAChD,MAAM,wBAAwB,GAAG,yBAAyB,CAAC,SAAS,CAAC,UAAU,CAAC;YAChF,MAAM,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,UAAU,CAAC;AAC1E,YAAA,OAAO,MAAK;gBACV,wBAAwB,CAAC,WAAW,EAAE;gBACtC,mBAAmB,CAAC,WAAW,EAAE;AACnC,aAAC;AACH,SAAC,CAAC;;AAGJ;;;;;;AAMG;AACH,IAAA,mBAAmB,CAAC,OAAkC,EAAA;AACpD,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE;YACnD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;;AAExD,QAAA,MAAM,WAAW,GAAgC,EAAC,eAAe,EAAE,IAAI,EAAC;QACxE,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC1F,QAAA,IAAI,oBAAoB,GAAG,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACtE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,oBAAoB,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;;AAE7C,QAAA,WAAW,CAAC,oBAAoB,GAAG,oBAAoB;QAEvD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;YACrC,IAAI,CAAC,WAAY,CAAC,IAAI,CACpB,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EAC5C,IAAI,CAAC,CAAC,CAAC,CACR,CAAC,SAAS,CAAC;AACV,gBAAA,IAAI,EAAE,CAAC,GAAG,KAAI;AACZ,oBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC;oBAClC,OAAO,CAAC,GAAG,CAAC;iBACb;AACD,gBAAA,KAAK,EAAE,MAAM;AACd,aAAA,CAAC;AACJ,SAAC,CAAC;;AAGJ;;;;;AAKG;IACH,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;YACtB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;;AAGxD,QAAA,MAAM,aAAa,GAAG,CAAC,GAA4B,KAAI;AACrD,YAAA,IAAI,GAAG,KAAK,IAAI,EAAE;gBAChB,MAAM,IAAIA,aAAY,CAAA,IAAA,8DAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,oBAAA,uCAAuC,CAC1C;;YAGH,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO,KAAI;gBACxC,IAAI,CAAC,OAAO,EAAE;AACZ,oBAAA,MAAM,IAAIA,aAAY,CAEpB,IAAA,8DAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,qBAAqB,CACzE;;AAGH,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;AACrC,aAAC,CAAC;AACJ,SAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,YAAA,IAAI,CAAC;iBACF,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,aAAa,CAAC;iBACtC,SAAS,CAAC,EAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAC,CAAC;AAC9C,SAAC,CAAC;;AAGI,IAAA,YAAY,CAAC,KAAa,EAAA;AAChC,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC;;kHAhJT,MAAM,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAN,MAAM,EAAA,CAAA;;sGAAN,MAAM,EAAA,UAAA,EAAA,CAAA;kBADlB;;;AC3ED;;;;;;;AAOG;MAEU,QAAQ,CAAA;AA6BC,IAAA,EAAA;AA5BpB;;;;;;;;AAQG;AACM,IAAA,cAAc;AAEvB;;;;AAIG;AACM,IAAA,aAAa;AAEtB;;;AAGG;AACH,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS;;IAGlB,qBAAqB,GAA4B,IAAI;AAE7D,IAAA,WAAA,CAAoB,EAAmB,EAAA;QAAnB,IAAE,CAAA,EAAA,GAAF,EAAE;AACpB,QAAA,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;AACjB,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;YAC1B;;QAEF,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAe;YACvD,kBAAkB;YAClB,6BAA6B;YAC7B,eAAe;YACf,yBAAyB;AAC1B,SAAA,CAAC;QACF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAA0B,qBAAqB,CAAC;;AAG3F;;;;;;;;AAQG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;YACtB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;;AAExD,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,OAAO,IAAI,CAAC,qBAAqB;;QAEnC,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AACrC,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;aAC/B,wBAAwB,CAAC,mBAAmB,EAAE,EAAC,KAAK,EAAC,EAAE,KAAK;aAC5D,OAAO,CAAC,MAAK;AACZ,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI;AACnC,SAAC,CAAC;QACJ,OAAO,IAAI,CAAC,qBAAqB;;AAGnC;;;;;;;;;;;;;;;;;;;;;;;AAuBG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;AACtB,YAAA,OAAO,OAAO,CAAC,MAAM,CACnB,IAAID,aAAY,uFAEd,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,oBAAoB,CACxE,CACF;;QAEH,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE;AACrC,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,EAAC,KAAK,EAAC,EAAE,KAAK,CAAC;;kHAvGjE,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAR,QAAQ,EAAA,CAAA;;sGAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBADpB;;;AC3BD;;;;;;AAMG;AAqBI,MAAM,MAAM,GAAG,IAAI,cAAc,CAAS,SAAS,GAAG,sBAAsB,GAAG,EAAE,CAAC;SAEzE,kBAAkB,GAAA;AAChC,IAAA,IAAI,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,EAAE;QACvD;;AAGF,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,qBAAqB,CAAC;AAE7C,IAAA,IAAI,EAAE,eAAe,IAAI,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE;QAChE;;AAGF,IAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,IAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AAC7B,IAAA,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC;;;;AAKrC,IAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;;;;AAI5B,QAAA,MAAM,EAAE,GAAG,SAAS,CAAC,aAAa;AAClC,QAAA,MAAM,kBAAkB,GAAG,MAAM,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,EAAC,MAAM,EAAE,YAAY,EAAC,CAAC;AAEnF,QAAA,EAAE,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;AAE3D,QAAA,MAAM,CAAC,SAAS,CAAC,MAAK;AACpB,YAAA,EAAE,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC;AAChE,SAAC,CAAC;AACJ,KAAC,CAAC;;;AAIF,IAAA,MAAM,CAAC,iBAAiB,CAAC,MAAK;AAC5B,QAAA,IAAI,eAA8B;AAElC,QAAA,MAAM,EAAC,oBAAoB,EAAC,GAAG,OAAO;AACtC,QAAA,IAAI,OAAO,oBAAoB,KAAK,UAAU,EAAE;YAC9C,eAAe,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,oBAAoB,EAAE,CAAC,SAAS,CAAC,MAAM,OAAO,EAAE,CAAC,CAAC;;aACxF;AACL,YAAA,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,IAAI,0BAA0B,EAAE,KAAK,CAAC,GAAG,CAAC;YAE3F,QAAQ,QAAQ;AACd,gBAAA,KAAK,qBAAqB;AACxB,oBAAA,eAAe,GAAG,OAAO,CAAC,OAAO,EAAE;oBACnC;AACF,gBAAA,KAAK,mBAAmB;oBACtB,eAAe,GAAG,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;oBACjD;AACF,gBAAA,KAAK,oBAAoB;oBACvB,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACjF;AACF,gBAAA;;oBAEE,MAAM,IAAID,aAAY,CAAA,IAAA,uDAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,wBAAA,CAAA,6CAAA,EAAgD,OAAO,CAAC,oBAAoB,CAAA,CAAE,CACjF;;;;;AAMP,QAAA,eAAe,CAAC,IAAI,CAAC,MAAK;;;;AAIxB,YAAA,IAAI,MAAM,CAAC,SAAS,EAAE;gBACpB;;AAGF,YAAA,SAAS,CAAC;iBACP,QAAQ,CAAC,MAAM,EAAE,EAAC,KAAK,EAAE,OAAO,CAAC,KAAK,EAAC;AACvC,iBAAA,KAAK,CAAC,CAAC,GAAG,KACT,OAAO,CAAC,KAAK,CACXE,mBAAkB,CAAA,IAAA,4DAEhB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,gBAAA,2CAA2C,GAAG,GAAG,CACpD,CACF,CACF;AACL,SAAC,CAAC;AACJ,KAAC,CAAC;AACJ;AAEA,SAAS,gBAAgB,CAAC,OAAe,EAAA;AACvC,IAAA,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC/D;AAEgB,SAAA,sBAAsB,CACpC,IAA2B,EAC3B,QAAkB,EAAA;IAElB,MAAM,SAAS,GAAG,EAAE,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,CAAC;IAExE,OAAO,IAAI,eAAe,CACxB,SAAS,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,GAAG,SAAS,CAAC,aAAa,GAAG,SAAS,EACzE,QAAQ,CACT;AACH;AAEA;;;;;;;;;;;AAWG;MACmB,qBAAqB,CAAA;AACzC;;;;;AAKG;AACH,IAAA,OAAO;AAEP;;;;AAIG;AACH,IAAA,KAAK;AAEL;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACH,IAAA,oBAAoB;AACrB;AAED;;;;;;;;;;;;;;;;AAgBG;SACa,oBAAoB,CAClC,MAAc,EACd,UAAiC,EAAE,EAAA;AAEnC,IAAA,OAAO,wBAAwB,CAAC;QAC9B,MAAM;QACN,QAAQ;AACR,QAAA,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAC;AACnC,QAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,OAAO,EAAC;AACnD,QAAA;AACE,YAAA,OAAO,EAAE,eAAe;AACxB,YAAA,UAAU,EAAE,sBAAsB;AAClC,YAAA,IAAI,EAAE,CAAC,qBAAqB,EAAE,QAAQ,CAAC;AACxC,SAAA;QACD,qBAAqB,CAAC,kBAAkB,CAAC;AAC1C,KAAA,CAAC;AACJ;;ACpNA;;AAEG;MAEU,mBAAmB,CAAA;AAC9B;;;;;AAKG;AACH,IAAA,OAAO,QAAQ,CACb,MAAc,EACd,UAAiC,EAAE,EAAA;QAEnC,OAAO;AACL,YAAA,QAAQ,EAAE,mBAAmB;YAC7B,SAAS,EAAE,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SACnD;;kHAdQ,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;mHAAnB,mBAAmB,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,EADV,SAAA,EAAA,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAA,CAAA;;sGAC3B,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAC;;;;;"}